@model IEnumerable<EmpMgr.Models.GovernmentDepartment>
@using EmpMgr.Extensions

@{
    ViewData["Title"] = "إدارة الأقسام الحكومية";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-layer-group me-2"></i>
                        إدارة الأقسام الحكومية
                    </h4>
                    <div>
                        <a asp-action="CreateGovernmentDepartment" class="btn btn-light btn-sm">
                            <i class="fas fa-plus me-1"></i>
                            إضافة قسم حكومي جديد
                        </a>
                        <a asp-action="Index" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة للثوابت
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="departmentsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>اسم القسم</th>
                                        <th>المديرية</th>
                                        <th>الوكالة</th>
                                        <th>الوزارة</th>
                                        <th>الرمز</th>
                                        <th>عدد الشعب</th>
                                        <th>عدد الموظفين</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var department in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@department.Name</strong>
                                                @if (!string.IsNullOrEmpty(department.Description))
                                                {
                                                    <br><small class="text-muted">@department.Description</small>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">@department.Directorate.Name</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-dark">@department.Directorate.Agency.Name</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-danger">@department.Directorate.Agency.Ministry.Name</span>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(department.Code))
                                                {
                                                    <span class="badge bg-secondary">@department.Code</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-warning">@department.Divisions.Count</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">@department.Employees.Count</span>
                                            </td>
                                            <td>
                                                @if (department.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="EditGovernmentDepartment" asp-route-id="@department.Id" 
                                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="DeleteGovernmentDepartment" asp-route-id="@department.Id" 
                                                       class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-layer-group fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أقسام حكومية مسجلة</h5>
                            <p class="text-muted">ابدأ بإضافة قسم حكومي جديد لإدارة البنية الهرمية الحكومية</p>
                            <a asp-action="CreateGovernmentDepartment" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>
                                إضافة قسم حكومي جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#departmentsTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
                },
                "responsive": true,
                "order": [[3, "asc"], [2, "asc"], [1, "asc"], [0, "asc"]],
                "pageLength": 25
            });
        });
    </script>
}

<style>
    .table th {
        border-top: none;
        font-weight: 600;
    }
    
    .btn-group .btn {
        margin: 0 2px;
    }
    
    .badge {
        font-size: 0.75em;
    }
</style>
