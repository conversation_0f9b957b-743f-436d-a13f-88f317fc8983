@model EmpMgr.ViewModels.RegisterViewModel
@{
    ViewData["Title"] = "إنشاء حساب جديد";
    Layout = "_LoginLayout";
}

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="logo-container">
                <img src="~/images/moi-logo.png" alt="شعار وزارة الداخلية" class="logo white-logo" />
            </div>
            <h2 class="login-title">إنشاء حساب جديد</h2>
            <p class="login-subtitle">نظام إدارة الضباط والمنتسبين</p>
        </div>

        <form asp-action="Register" asp-controller="Account" method="post" class="login-form">
            <div asp-validation-summary="All" class="text-danger validation-summary"></div>
            
            <div class="form-group">
                <label asp-for="FullName" class="form-label">
                    <i class="fas fa-user"></i>
                    @Html.DisplayNameFor(m => m.FullName)
                </label>
                <input asp-for="FullName" class="form-control" placeholder="أدخل الاسم الكامل" />
                <span asp-validation-for="FullName" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Email" class="form-label">
                    <i class="fas fa-envelope"></i>
                    @Html.DisplayNameFor(m => m.Email)
                </label>
                <input asp-for="Email" class="form-control" placeholder="أدخل البريد الإلكتروني" />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Password" class="form-label">
                    <i class="fas fa-lock"></i>
                    @Html.DisplayNameFor(m => m.Password)
                </label>
                <div class="password-input-container">
                    <input asp-for="Password" class="form-control" placeholder="أدخل كلمة المرور" />
                    <button type="button" class="password-toggle" onclick="togglePassword('Password')">
                        <i class="fas fa-eye" id="password-eye"></i>
                    </button>
                </div>
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="ConfirmPassword" class="form-label">
                    <i class="fas fa-lock"></i>
                    @Html.DisplayNameFor(m => m.ConfirmPassword)
                </label>
                <div class="password-input-container">
                    <input asp-for="ConfirmPassword" class="form-control" placeholder="أعد إدخال كلمة المرور" />
                    <button type="button" class="password-toggle" onclick="togglePassword('ConfirmPassword')">
                        <i class="fas fa-eye" id="confirm-password-eye"></i>
                    </button>
                </div>
                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
            </div>

            <button type="submit" class="btn btn-login">
                <i class="fas fa-user-plus"></i>
                إنشاء الحساب
            </button>

            <div class="login-links">
                <a asp-action="Login" class="register-link">لديك حساب بالفعل؟ سجل الدخول</a>
            </div>
        </form>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="~/css/login.css" asp-append-version="true" />
}

@section Scripts {
    <script>
        function togglePassword(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            const eyeIcon = fieldId === 'Password' ?
                document.getElementById('password-eye') :
                document.getElementById('confirm-password-eye');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // التركيز على حقل الاسم الكامل
            document.getElementById('FullName').focus();

            // إضافة تأثيرات للحقول
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (this.value === '') {
                        this.parentElement.classList.remove('focused');
                    }
                });

                // إذا كان الحقل يحتوي على قيمة عند التحميل
                if (input.value !== '') {
                    input.parentElement.classList.add('focused');
                }
            });
        });
    </script>
}
