@model EmpMgr.Models.Employee
@{
    ViewData["Title"] = "طباعة بيانات الموظف";
    Layout = null; // لا نريد layout للطباعة
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: white;
            color: #333;
            line-height: 1.6;
        }
        
        .print-header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .print-header h1 {
            color: #007bff;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .print-header h2 {
            color: #6c757d;
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
        
        .employee-photo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .employee-photo img {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 10px;
            border: 3px solid #007bff;
        }
        
        .info-section {
            margin-bottom: 25px;
        }
        
        .info-section h4 {
            color: #007bff;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            width: 40%;
        }
        
        .info-value {
            color: #212529;
            width: 55%;
        }
        
        .badge-custom {
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .print-footer {
            margin-top: 40px;
            text-align: center;
            border-top: 2px solid #e9ecef;
            padding-top: 20px;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        @@media print {
            body {
                margin: 0;
                padding: 20px;
            }
            
            .no-print {
                display: none !important;
            }
            
            .print-header {
                page-break-after: avoid;
            }
            
            .info-section {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- أزرار التحكم -->
        <div class="no-print mb-3">
            <div class="d-flex justify-content-between">
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
                <button onclick="window.close()" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
            </div>
        </div>

        <!-- رأس الصفحة -->
        <div class="print-header">
            <h1>وزارة الداخلية - جمهورية العراق</h1>
            <h2>نظام إدارة الضباط والمنتسبين</h2>
            <h3>بطاقة بيانات الموظف</h3>
        </div>

        <!-- صورة الموظف -->
        <div class="employee-photo">
            <div class="no-photo">
                <i class="fas fa-user"></i>
                <span>لا توجد صورة</span>
            </div>
        </div>

        <!-- البيانات الأساسية -->
        <div class="info-section">
            <h4><i class="fas fa-id-card me-2"></i>البيانات الأساسية</h4>
            <div class="info-row">
                <span class="info-label">رقم الموظف:</span>
                <span class="info-value"><span class="badge-custom">@Model.EmployeeNumber</span></span>
            </div>
            <div class="info-row">
                <span class="info-label">الرقم الإحصائي:</span>
                <span class="info-value"><span class="badge-custom">@Model.StatisticalNumber</span></span>
            </div>
            <div class="info-row">
                <span class="info-label">الاسم الكامل:</span>
                <span class="info-value"><strong>@Model.FullName</strong></span>
            </div>
            <div class="info-row">
                <span class="info-label">الرتبة:</span>
                <span class="info-value">@(Model.Rank?.Name ?? "غير محدد")</span>
            </div>
            <div class="info-row">
                <span class="info-label">الجنس:</span>
                <span class="info-value">@(Model.Gender == EmpMgr.Models.Gender.Male ? "ذكر" : "أنثى")</span>
            </div>
            <div class="info-row">
                <span class="info-label">الحالة الاجتماعية:</span>
                <span class="info-value">
                    @switch (Model.MaritalStatus)
                    {
                        case EmpMgr.Models.MaritalStatus.Single:
                            <span>أعزب</span>
                            break;
                        case EmpMgr.Models.MaritalStatus.Married:
                            <span>متزوج</span>
                            break;
                        case EmpMgr.Models.MaritalStatus.Divorced:
                            <span>مطلق</span>
                            break;
                        case EmpMgr.Models.MaritalStatus.Widowed:
                            <span>أرمل</span>
                            break;
                        default:
                            <span>غير محدد</span>
                            break;
                    }
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">فصيلة الدم:</span>
                <span class="info-value">@Model.BloodType.ToString()</span>
            </div>
        </div>

        <!-- البيانات السكنية -->
        <div class="info-section">
            <h4><i class="fas fa-map-marker-alt me-2"></i>البيانات السكنية</h4>
            <div class="info-row">
                <span class="info-label">المحافظة:</span>
                <span class="info-value">@(Model.Province?.Name ?? "غير محدد")</span>
            </div>
            <div class="info-row">
                <span class="info-label">القضاء:</span>
                <span class="info-value">@(Model.District ?? "غير محدد")</span>
            </div>
            <div class="info-row">
                <span class="info-label">الناحية:</span>
                <span class="info-value">@(Model.Subdistrict ?? "غير محدد")</span>
            </div>
            <div class="info-row">
                <span class="info-label">القرية:</span>
                <span class="info-value">@(Model.Village ?? "غير محدد")</span>
            </div>
            <div class="info-row">
                <span class="info-label">الحي:</span>
                <span class="info-value">@(Model.Neighborhood ?? "غير محدد")</span>
            </div>
            <div class="info-row">
                <span class="info-label">المحلة:</span>
                <span class="info-value">@(Model.Quarter ?? "غير محدد")</span>
            </div>
            <div class="info-row">
                <span class="info-label">الزقاق:</span>
                <span class="info-value">@(Model.Alley ?? "غير محدد")</span>
            </div>
            <div class="info-row">
                <span class="info-label">رقم الدار:</span>
                <span class="info-value">@(Model.House ?? "غير محدد")</span>
            </div>
        </div>

        <!-- البيانات التعليمية -->
        <div class="info-section">
            <h4><i class="fas fa-graduation-cap me-2"></i>البيانات التعليمية</h4>
            <div class="info-row">
                <span class="info-label">المستوى التعليمي:</span>
                <span class="info-value">
                    @switch (Model.EducationLevel)
                    {
                        case EmpMgr.Models.EducationLevel.Primary:
                            <span>ابتدائية</span>
                            break;
                        case EmpMgr.Models.EducationLevel.Intermediate:
                            <span>متوسطة</span>
                            break;
                        case EmpMgr.Models.EducationLevel.Secondary:
                            <span>إعدادية</span>
                            break;
                        case EmpMgr.Models.EducationLevel.Diploma:
                            <span>دبلوم</span>
                            break;
                        case EmpMgr.Models.EducationLevel.Bachelor:
                            <span>بكالوريوس</span>
                            break;
                        case EmpMgr.Models.EducationLevel.Master:
                            <span>ماجستير</span>
                            break;
                        case EmpMgr.Models.EducationLevel.PhD:
                            <span>دكتوراه</span>
                            break;
                        default:
                            <span>غير محدد</span>
                            break;
                    }
                </span>
            </div>
            @if (Model.University != null)
            {
                <div class="info-row">
                    <span class="info-label">الجامعة:</span>
                    <span class="info-value">@Model.University.Name</span>
                </div>
            }
            @if (Model.Institute != null)
            {
                <div class="info-row">
                    <span class="info-label">المعهد:</span>
                    <span class="info-value">@Model.Institute.Name</span>
                </div>
            }
        </div>

        <!-- معلومات النظام -->
        <div class="info-section">
            <h4><i class="fas fa-info-circle me-2"></i>معلومات النظام</h4>
            <div class="info-row">
                <span class="info-label">تاريخ الإضافة:</span>
                <span class="info-value">@Model.CreatedDate.ToString("yyyy/MM/dd HH:mm")</span>
            </div>
            <div class="info-row">
                <span class="info-label">آخر تحديث:</span>
                <span class="info-value">@Model.UpdatedDate?.ToString("yyyy/MM/dd HH:mm")</span>
            </div>
        </div>

        <!-- تذييل الصفحة -->
        <div class="print-footer">
            <p>تم إنشاء هذا التقرير في @DateTime.Now.ToString("yyyy/MM/dd HH:mm")</p>
            <p>نظام إدارة الضباط والمنتسبين - وزارة الداخلية</p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند التحميل (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
