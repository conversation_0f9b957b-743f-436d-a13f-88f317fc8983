@model EmpMgr.Models.College

@{
    ViewData["Title"] = "إضافة كلية جديدة";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-plus-circle me-2"></i>
                    إضافة كلية جديدة
                </h2>
                <a asp-action="Colleges" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للكليات
                </a>
            </div>

            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        بيانات الكلية
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="CreateCollege" method="post" id="collegeForm" novalidate>
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="UniversityId" class="form-label required">الجامعة</label>
                                <select asp-for="UniversityId" class="form-select" required>
                                    <option value="">اختر الجامعة</option>
                                    @foreach (var university in (ViewBag.Universities as List<EmpMgr.Models.University>) ?? new List<EmpMgr.Models.University>())
                                    {
                                        <option value="@university.Id">@university.Name</option>
                                    }
                                </select>
                                <span asp-validation-for="UniversityId" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    اختر الجامعة التي تتبع لها الكلية
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="Name" class="form-label required"></label>
                                <input asp-for="Name" class="form-control" placeholder="أدخل اسم الكلية" required />
                                <span asp-validation-for="Name" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    أدخل الاسم الكامل للكلية
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="Code" class="form-label"></label>
                                <input asp-for="Code" class="form-control" placeholder="كود الكلية (اختياري)" maxlength="10" />
                                <span asp-validation-for="Code" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    كود مختصر للكلية (اختياري)
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label asp-for="Type" class="form-label required"></label>
                                <select asp-for="Type" class="form-select" required>
                                    <option value="">اختر نوع الكلية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Scientific)">علمية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Humanities)">إنسانية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Medical)">طبية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Engineering)">هندسية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Technical)">تقنية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Administrative)">إدارية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Legal)">قانونية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Educational)">تربوية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Arts)">فنية</option>
                                </select>
                                <span asp-validation-for="Type" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    حدد نوع الكلية
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label asp-for="EstablishedYear" class="form-label"></label>
                                <input asp-for="EstablishedYear" type="number" class="form-control" 
                                       placeholder="سنة التأسيس" min="1900" max="@DateTime.Now.Year" />
                                <span asp-validation-for="EstablishedYear" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    سنة تأسيس الكلية (اختياري)
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label asp-for="Description" class="form-label"></label>
                                <textarea asp-for="Description" class="form-control" rows="3" 
                                          placeholder="وصف مختصر عن الكلية (اختياري)"></textarea>
                                <span asp-validation-for="Description" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    وصف مختصر عن الكلية وتخصصاتها
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="IsActive" class="form-check-label">
                                        الكلية نشطة
                                    </label>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    الكليات النشطة فقط تظهر في قوائم الاختيار
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ الكلية
                                </button>
                                <button type="reset" class="btn btn-outline-secondary btn-lg ms-2">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                            <a asp-action="Colleges" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-list me-2"></i>
                                عرض جميع الكليات
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card mt-4 border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح مهمة
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            اختر الجامعة أولاً ثم أدخل اسم الكلية
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تأكد من كتابة اسم الكلية بشكل صحيح ومطابق للتسمية الرسمية
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            حدد نوع الكلية بدقة لتسهيل التصنيف والبحث
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            يمكنك إضافة كود مختصر للكلية لتسهيل الإدارة
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            الكليات غير النشطة لن تظهر في قوائم إضافة الموظفين الجدد
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من صحة النموذج
            const form = document.getElementById('collegeForm');
            
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });

            // تنظيف المدخلات
            const nameInput = document.getElementById('Name');
            const codeInput = document.getElementById('Code');
            const descriptionInput = document.getElementById('Description');

            // تنظيف حقل الاسم يتم بواسطة form-fixes.js العام

            if (codeInput) {
                codeInput.addEventListener('input', function() {
                    this.value = this.value.toUpperCase().replace(/\s/g, '');
                });
            }

            // تنظيف حقل الوصف يتم بواسطة form-fixes.js العام

            // إصلاح زر المسافة يتم بواسطة form-fixes.js العام

            // التركيز على اختيار الجامعة
            document.getElementById('UniversityId').focus();
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: #dc3545;
        }

        .form-control:focus, .form-select:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .card {
            border: none;
            border-radius: 15px;
        }

        .card-header {
            border-radius: 15px 15px 0 0 !important;
        }

        .btn {
            border-radius: 10px;
        }

        .form-check-input:checked {
            background-color: #28a745;
            border-color: #28a745;
        }
    </style>
}
