@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

/* إعدادات عامة للتصميم الجديد */
*, *::before, *::after {
    font-family: 'Cairo', sans-serif !important;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    direction: rtl;
    min-height: 100vh;
    overflow-x: hidden;
    font-size: 16px;
    background: #0a0a0a;
    position: relative;
}

/* الحاوي الرئيسي الجديد */
.ultra-modern-login-wrapper {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* الخلفية الديناميكية المتطورة */
.dynamic-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
}

/* طبقات الخلفية المتحركة */
.bg-layer {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0.8;
    animation: layerMove 20s ease-in-out infinite;
}

.layer-1 {
    background: linear-gradient(45deg, #667eea, #764ba2);
    animation-delay: 0s;
}

.layer-2 {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    animation-delay: -7s;
}

.layer-3 {
    background: linear-gradient(225deg, #4facfe, #00f2fe);
    animation-delay: -14s;
}

@keyframes layerMove {
    0%, 100% {
        transform: translateX(0%) rotate(0deg);
        opacity: 0.8;
    }
    25% {
        transform: translateX(5%) rotate(1deg);
        opacity: 0.6;
    }
    50% {
        transform: translateX(-3%) rotate(-1deg);
        opacity: 0.9;
    }
    75% {
        transform: translateX(2%) rotate(0.5deg);
        opacity: 0.7;
    }
}

/* الجزيئات المتحركة */
.particles-container {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: particleFloat 15s linear infinite;
}

.particle-1 { top: 10%; left: 10%; animation-delay: 0s; }
.particle-2 { top: 20%; left: 80%; animation-delay: -2s; }
.particle-3 { top: 60%; left: 20%; animation-delay: -4s; }
.particle-4 { top: 80%; left: 70%; animation-delay: -6s; }
.particle-5 { top: 30%; left: 50%; animation-delay: -8s; }
.particle-6 { top: 70%; left: 90%; animation-delay: -10s; }
.particle-7 { top: 50%; left: 15%; animation-delay: -12s; }
.particle-8 { top: 90%; left: 40%; animation-delay: -14s; }

@keyframes particleFloat {
    0% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) translateX(50px) scale(0);
        opacity: 0;
    }
}

/* الشبكة الهندسية */
.geometric-grid {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0.1;
}

.grid-line {
    position: absolute;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

.grid-line.horizontal {
    width: 100%;
    height: 1px;
    top: 50%;
    animation: gridPulse 4s ease-in-out infinite;
}

.grid-line.vertical {
    width: 1px;
    height: 100%;
    left: 50%;
    animation: gridPulse 4s ease-in-out infinite reverse;
}

@keyframes gridPulse {
    0%, 100% { opacity: 0.1; }
    50% { opacity: 0.3; }
}

/* بطاقة تسجيل الدخول الجديدة */
.ultra-login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(30px);
    border-radius: 32px;
    box-shadow:
        0 40px 80px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    width: 100%;
    max-width: 520px;
    overflow: hidden;
    position: relative;
    animation: cardSlideIn 1s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes cardSlideIn {
    0% {
        opacity: 0;
        transform: translateY(60px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.ultra-login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
    background-size: 300% 100%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* قسم الشعار والعنوان */
.brand-section {
    padding: 50px 40px 40px;
    text-align: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.03), rgba(255, 255, 255, 0.05));
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.brand-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    animation: sectionGlow 10s ease-in-out infinite;
    z-index: 0;
}

@keyframes sectionGlow {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        opacity: 0.5;
    }
    50% {
        transform: rotate(180deg) scale(1.1);
        opacity: 0.8;
    }
}

.brand-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
}

/* الشعار الجديد */
.logo-wrapper-new {
    margin-bottom: 30px;
    position: relative;
    display: inline-block;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 2;
}

.logo-wrapper-new:hover .logo-ring {
    animation-duration: 3s;
    box-shadow:
        0 0 40px rgba(102, 126, 234, 0.6),
        0 0 80px rgba(118, 75, 162, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.4);
    transform: scale(1.05);
}

.logo-wrapper-new:hover .logo-image {
    animation-duration: 2s;
    filter: brightness(0) invert(1) drop-shadow(0 0 15px rgba(255, 255, 255, 0.8));
    transform: scale(1.1);
}

.logo-wrapper-new:hover .logo-pulse {
    animation-duration: 2s;
}

.logo-ring {
    width: 140px;
    height: 140px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
    background-size: 300% 300%;
    padding: 4px;
    position: relative;
    animation: logoRotate 15s linear infinite, gradientRotate 8s ease-in-out infinite;
    box-shadow:
        0 0 30px rgba(102, 126, 234, 0.4),
        0 0 60px rgba(118, 75, 162, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

@keyframes logoRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes gradientRotate {
    0%, 100% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 100% 100%; }
    75% { background-position: 0% 100%; }
}

.logo-inner {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.95));
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow:
        inset 0 4px 8px rgba(0, 0, 0, 0.1),
        inset 0 -2px 4px rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.logo-inner::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 30%;
    height: 30%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8), transparent);
    animation: innerShine 5s ease-in-out infinite;
}

@keyframes innerShine {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
    }
}

.logo-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: all 0.3s ease;
    animation: logoImageRotate 12s linear infinite reverse;
    transform-origin: center center;
    display: block;
    max-width: 100%;
    max-height: 100%;
}

/* فلتر للصور PNG/JPG */
.logo-image[src$=".png"],
.logo-image[src$=".jpg"],
.logo-image[src$=".jpeg"] {
    filter: brightness(0) invert(1) drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

/* بدون فلتر للصور SVG */
.logo-image[src$=".svg"] {
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

@keyframes logoImageRotate {
    0% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(90deg) scale(1.05); }
    50% { transform: rotate(180deg) scale(1); }
    75% { transform: rotate(270deg) scale(1.05); }
    100% { transform: rotate(360deg) scale(1); }
}

.logo-fallback {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 40px;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    animation: logoImageRotate 12s linear infinite reverse;
    transform-origin: center center;
}

.logo-pulse {
    position: absolute;
    top: -25px;
    left: -25px;
    right: -25px;
    bottom: -25px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.2), transparent);
    animation: logoPulse 4s ease-in-out infinite;
    z-index: -1;
}

.logo-pulse::before {
    content: '';
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(240, 147, 251, 0.2), transparent);
    animation: logoPulse 4s ease-in-out infinite reverse;
}

.logo-pulse::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1), transparent);
    animation: logoPulse 6s ease-in-out infinite;
}

@keyframes logoPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    25% {
        transform: scale(1.1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.4;
    }
    75% {
        transform: scale(1.1);
        opacity: 0.6;
    }
}

/* النصوص */
.brand-text {
    position: relative;
    z-index: 2;
}

.system-title {
    font-size: 36px;
    font-weight: 800;
    color: #2c3e50;
    margin: 0 0 20px 0;
    line-height: 1.2;
    background: linear-gradient(135deg, #2c3e50, #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleShine 4s ease-in-out infinite;
}

@keyframes titleShine {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.3); }
}

.system-subtitle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: #7f8c8d;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
}

.subtitle-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    animation: iconBounce 2s ease-in-out infinite;
}

@keyframes iconBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.title-underline {
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    margin: 0 auto;
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.title-underline::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: underlineShine 2s ease-in-out infinite;
}

@keyframes underlineShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* قسم النموذج */
.login-form-section {
    padding: 40px;
}

.ultra-login-form {
    position: relative;
}

.error-summary {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
    border: 1px solid rgba(220, 53, 69, 0.2);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 30px;
    color: #dc3545;
    font-weight: 500;
    animation: errorSlideIn 0.5s ease-out;
}

@keyframes errorSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* مجموعة الحقول */
.input-fields-group {
    margin-bottom: 30px;
}

.ultra-input-group {
    margin-bottom: 30px;
    position: relative;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon-wrapper {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
    color: #7f8c8d;
    font-size: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ultra-input {
    width: 100%;
    padding: 24px 70px 24px 60px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    font-size: 18px;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 2;
    color: #2c3e50;
}

.ultra-input:focus {
    outline: none;
    border-color: #667eea;
    background: rgba(255, 255, 255, 0.95);
    box-shadow:
        0 0 0 6px rgba(102, 126, 234, 0.1),
        0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.ultra-input:focus + .ultra-label {
    transform: translateY(-45px) scale(0.85);
    color: #667eea;
    font-weight: 600;
}

.ultra-input:not(:placeholder-shown) + .ultra-label {
    transform: translateY(-45px) scale(0.85);
    color: #2c3e50;
    font-weight: 600;
}

.ultra-label {
    position: absolute;
    left: 60px;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
    font-size: 18px;
    font-weight: 500;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(to bottom, transparent 40%, rgba(255, 255, 255, 0.9) 40%);
    padding: 0 12px;
    z-index: 3;
}

.input-highlight {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
    border-radius: 2px;
}

.ultra-input:focus ~ .input-highlight {
    width: 100%;
}

.input-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.1);
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
    pointer-events: none;
}

.ultra-input:focus ~ .input-ripple {
    width: 300px;
    height: 300px;
}

.password-reveal {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #7f8c8d;
    font-size: 20px;
    cursor: pointer;
    padding: 12px;
    border-radius: 12px;
    transition: all 0.3s ease;
    z-index: 3;
}

.password-reveal:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-50%) scale(1.1);
}

.field-error {
    display: block;
    margin-top: 10px;
    color: #dc3545;
    font-size: 14px;
    font-weight: 500;
    animation: errorFadeIn 0.3s ease;
}

@keyframes errorFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(120, 119, 198, 0.1) 0%, transparent 50%);
}

/* العناصر المتحركة المحسنة */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    animation: floatAnimation 20s infinite linear;
}

.element-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 25s;
}

.element-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 15%;
    animation-delay: -5s;
    animation-duration: 30s;
}

.element-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: -10s;
    animation-duration: 20s;
}

.element-4 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    right: 25%;
    animation-delay: -15s;
    animation-duration: 35s;
}

.element-5 {
    width: 40px;
    height: 40px;
    top: 50%;
    left: 50%;
    animation-delay: -20s;
    animation-duration: 15s;
}

.element-6 {
    width: 90px;
    height: 90px;
    top: 70%;
    right: 40%;
    animation-delay: -25s;
    animation-duration: 28s;
}

@keyframes floatAnimation {
    0% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    25% {
        transform: translateY(-20px) rotate(90deg);
        opacity: 0.9;
    }
    50% {
        transform: translateY(-40px) rotate(180deg);
        opacity: 0.5;
    }
    75% {
        transform: translateY(-20px) rotate(270deg);
        opacity: 0.8;
    }
    100% {
        transform: translateY(0px) rotate(360deg);
        opacity: 0.7;
    }
}

/* بطاقة تسجيل الدخول المحسنة */
.enhanced-login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    width: 100%;
    max-width: 480px;
    padding: 0;
    position: relative;
    overflow: hidden;
    animation: cardEntrance 0.8s ease-out;
}

@keyframes cardEntrance {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.enhanced-login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* رأس البطاقة المحسن */
.card-header-section {
    padding: 40px 40px 30px;
    text-align: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.logo-section {
    margin-bottom: 25px;
}

.logo-container-modern {
    display: inline-block;
    position: relative;
}

.logo-background {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0 auto;
    box-shadow:
        0 20px 40px rgba(102, 126, 234, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.2);
    animation: logoFloat 6s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
    }
    50% {
        transform: translateY(-10px) rotate(180deg);
        box-shadow: 0 30px 60px rgba(102, 126, 234, 0.4);
    }
}

.ministry-logo-enhanced {
    width: 70px;
    height: 70px;
    filter: brightness(0) invert(1);
    z-index: 2;
    position: relative;
    animation: logoSpin 20s linear infinite;
}

@keyframes logoSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.logo-shine {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    background: conic-gradient(
        from 0deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: logoShine 4s linear infinite;
    z-index: 1;
}

@keyframes logoShine {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.title-section {
    margin-top: 20px;
}

.main-title {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 32px;
    font-weight: 800;
    line-height: 1.2;
    position: relative;
}

.title-text {
    display: block;
    background: linear-gradient(135deg, #2c3e50, #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 3s ease-in-out infinite;
}

@keyframes titleGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.2); }
}

.title-decoration {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    margin: 15px auto;
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.title-decoration::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: decorationShine 2s ease-in-out infinite;
}

@keyframes decorationShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.subtitle-text {
    color: #7f8c8d;
    font-size: 18px;
    font-weight: 500;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0.9;
}

.subtitle-text i {
    color: #667eea;
    font-size: 20px;
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    animation: float 20s infinite linear;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 25s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: -5s;
    animation-duration: 30s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: -10s;
    animation-duration: 20s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 25%;
    animation-delay: -15s;
    animation-duration: 35s;
}

.shape-5 {
    width: 40px;
    height: 40px;
    top: 10%;
    right: 40%;
    animation-delay: -20s;
    animation-duration: 15s;
}

@keyframes float {
    0% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    33% {
        transform: translateY(-30px) rotate(120deg);
        opacity: 0.4;
    }
    66% {
        transform: translateY(30px) rotate(240deg);
        opacity: 0.7;
    }
    100% {
        transform: translateY(0px) rotate(360deg);
        opacity: 0.7;
    }
}

.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    position: relative;
    z-index: 1;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    padding: 45px;
    width: 100%;
    max-width: 480px;
    animation: slideUp 0.8s ease-out;
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.login-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.03), transparent);
    animation: rotate 20s linear infinite;
    pointer-events: none;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: 35px;
    position: relative;
    z-index: 2;
}

.logo-container {
    margin-bottom: 20px;
}

.logo-wrapper {
    position: relative;
    display: inline-block;
}

.logo-circle {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.4),
        inset 0 -2px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.ministry-logo {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 50%;
    position: relative;
    z-index: 2;
    filter: brightness(0) invert(1) contrast(100%);
}

.logo-border {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.5);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.logo {
    width: 95px;
    height: 95px;
    object-fit: contain;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 50%;
    padding: 18px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.08));
    backdrop-filter: blur(20px);
    border: 3px solid rgba(255, 255, 255, 0.4);
    box-shadow:
        0 10px 40px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.5),
        inset 0 -2px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
    filter: brightness(0) invert(1) contrast(100%);
}

.logo-glow {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #667eea);
    background-size: 400% 400%;
    border-radius: 50%;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.4s ease;
    animation: logoGlow 4s ease-in-out infinite;
    filter: blur(8px);
}

.logo::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #667eea);
    background-size: 400% 400%;
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
    animation: logoGlow 4s ease-in-out infinite;
}

.logo-wrapper:hover .logo-circle {
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow:
        0 25px 60px rgba(0, 0, 0, 0.2),
        inset 0 3px 0 rgba(255, 255, 255, 0.6),
        inset 0 -3px 0 rgba(0, 0, 0, 0.1);
}

.logo-wrapper:hover .ministry-logo {
    transform: scale(1.1) rotate(-3deg);
}

.logo-wrapper:hover .logo-border {
    opacity: 1;
}

.logo-wrapper:hover .logo-glow {
    opacity: 0.8;
}

.logo-wrapper:hover .logo {
    transform: scale(1.15) rotate(8deg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.35), rgba(255, 255, 255, 0.15));
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow:
        0 15px 50px rgba(0, 0, 0, 0.2),
        inset 0 3px 0 rgba(255, 255, 255, 0.7),
        inset 0 -3px 0 rgba(0, 0, 0, 0.15);
}

.logo-wrapper:hover .logo::before {
    opacity: 0.8;
}

@keyframes logoGlow {
    0%, 100% {
        opacity: 0;
        background-position: 0% 50%;
    }
    50% {
        opacity: 0.4;
        background-position: 100% 50%;
    }
}

/* CSS بديل للمتصفحات القديمة */
@supports not (filter: invert(1)) {
    .logo {
        background-color: white;
        mix-blend-mode: difference;
        filter: none;
    }
}

.login-title {
    margin-bottom: 15px;
    position: relative;
}

.title-main {
    background: linear-gradient(135deg, #2c3e50 0%, #667eea 50%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    font-size: 30px;
    font-weight: 700;
    line-height: 1.3;
    display: block;
    animation: titleGradient 3s ease-in-out infinite;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title-underline {
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    margin: 8px auto;
    border-radius: 2px;
    animation: underlineGlow 2s ease-in-out infinite;
}

.login-subtitle {
    color: #6c757d;
    font-size: 17px;
    font-weight: 500;
    margin: 0;
    opacity: 0.95;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.login-subtitle i {
    color: #667eea;
    font-size: 16px;
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes titleGradient {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes underlineGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
        transform: scaleX(1);
    }
    50% {
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.6);
        transform: scaleX(1.1);
    }
}

@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

.form-group {
    margin-bottom: 32px;
    position: relative;
    z-index: 2;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 15px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-label i {
    color: #667eea;
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-toggle {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    z-index: 3;
}

.password-toggle:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.password-toggle i {
    font-size: 16px;
}

.validation-message {
    display: block;
    margin-top: 6px;
    font-size: 14px;
    font-weight: 500;
    animation: fadeInError 0.3s ease;
}

@keyframes fadeInError {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-label {
    display: block;
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-label i {
    margin-left: 8px;
    color: #667eea;
}

.form-control {
    width: 100%;
    padding: 18px 24px;
    padding-left: 50px; /* مساحة للأيقونة */
    border: 2px solid rgba(233, 236, 239, 0.8);
    border-radius: 16px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(248, 249, 250, 0.9);
    backdrop-filter: blur(15px);
    box-shadow:
        0 3px 12px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    position: relative;
}

.password-input-container .form-control {
    padding-left: 50px;
    padding-right: 50px; /* مساحة لزر إظهار كلمة المرور */
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    background: rgba(255, 255, 255, 0.98);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.18),
        0 6px 25px rgba(102, 126, 234, 0.12),
        inset 0 2px 0 rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.form-control::placeholder {
    color: rgba(108, 117, 125, 0.7);
    font-weight: 400;
    transition: color 0.3s ease;
}

.form-control:focus::placeholder {
    color: rgba(102, 126, 234, 0.6);
}

/* أيقونات الحقول */
.form-group::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 18px;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.6;
    transition: all 0.3s ease;
    z-index: 2;
    pointer-events: none;
}

.form-group:has([asp-for="Email"])::before {
    content: '📧';
    font-size: 16px;
}

.form-group:has([asp-for="Password"])::before {
    content: '🔒';
    font-size: 16px;
}

.form-group:has(.form-control:focus)::before {
    opacity: 1;
    color: #667eea;
    transform: translateY(-50%) scale(1.1);
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 5px;
}

.password-toggle:hover {
    color: #667eea;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-check-input {
    width: 18px;
    height: 18px;
    accent-color: #667eea;
}

.form-check-label {
    color: #2c3e50;
    font-weight: 500;
    cursor: pointer;
}

.btn-login {
    width: 100%;
    padding: 18px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 200% 200%;
    color: white;
    border: none;
    border-radius: 15px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-login:hover {
    transform: translateY(-3px);
    background-position: 100% 0;
    box-shadow:
        0 15px 35px rgba(102, 126, 234, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-login:hover::before {
    left: 100%;
}

.btn-login:active {
    transform: translateY(-1px);
}

.btn-login.loading {
    pointer-events: none;
    position: relative;
    color: transparent;
}

.btn-login.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* تحسينات إضافية للتفاعل */
.form-group.focused .form-control {
    border-color: #667eea;
    box-shadow:
        0 0 0 3px rgba(102, 126, 234, 0.15),
        0 4px 20px rgba(102, 126, 234, 0.1);
}

.form-group.has-value .form-control {
    background: rgba(255, 255, 255, 0.95);
}

/* تأثيرات الأخطاء */
.validation-summary {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.validation-summary ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.validation-summary li {
    color: #dc3545;
    font-weight: 500;
    margin-bottom: 5px;
}

.validation-summary li:last-child {
    margin-bottom: 0;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .login-card {
        margin: 20px;
        padding: 35px 25px;
    }

    .logo {
        width: 80px;
        height: 80px;
    }

    .title-main {
        font-size: 26px;
    }

    .login-subtitle {
        font-size: 15px;
    }

    .form-control {
        padding: 16px 20px;
        font-size: 15px;
    }

    .btn-login {
        padding: 16px 20px;
        font-size: 17px;
    }

    .floating-shapes .shape {
        display: none;
    }
}

.btn-login i {
    margin-left: 10px;
}

.login-links {
    text-align: center;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
}

.register-link,
.forgot-password-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.3s ease;
}

.register-link:hover,
.forgot-password-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

.text-danger {
    color: #e74c3c !important;
    font-size: 13px;
    margin-top: 5px;
    display: block;
}

.validation-summary {
    background-color: #fdf2f2;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.validation-summary ul {
    margin: 0;
    padding-right: 20px;
}

.validation-summary li {
    margin-bottom: 5px;
}

@media (max-width: 480px) {
    .login-card {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .login-title {
        font-size: 20px;
    }
    
    .login-subtitle {
        font-size: 14px;
    }
    
    .login-links {
        flex-direction: column;
        text-align: center;
    }
}

/* تحسينات ميزة "تذكرني" */
.remember-me-container {
    margin: 20px 0;
}

.remember-me-wrapper {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px;
    transition: all 0.3s ease;
}

.remember-me-wrapper:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Checkbox مخصص */
.custom-checkbox {
    position: relative;
    margin-bottom: 15px;
}

.custom-checkbox-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.custom-checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    user-select: none;
    transition: all 0.3s ease;
}

.checkbox-indicator {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.checkbox-indicator i {
    color: transparent;
    font-size: 14px;
    transition: all 0.3s ease;
}

.custom-checkbox-input:checked + .custom-checkbox-label .checkbox-indicator {
    background: linear-gradient(135deg, #28a745, #20c997);
    border-color: #28a745;
    transform: scale(1.1);
}

.custom-checkbox-input:checked + .custom-checkbox-label .checkbox-indicator i {
    color: white;
    transform: scale(1.2);
}

.checkbox-content {
    flex: 1;
}

.checkbox-title {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    font-size: 16px;
    display: block;
    margin-bottom: 4px;
}

.checkbox-hint {
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
    font-weight: 400;
}

.custom-checkbox.changing .checkbox-indicator {
    animation: checkboxPulse 0.3s ease;
}

@keyframes checkboxPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* حالات التذكر */
.remember-me-status {
    margin-top: 10px;
}

.remember-me-status > div {
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 13px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.remember-me-status i {
    margin-left: 8px;
    font-size: 14px;
}

.last-login-info {
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: rgba(255, 255, 255, 0.9);
}

.security-warning {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: rgba(255, 255, 255, 0.9);
}

.data-cleared-info {
    background: rgba(23, 162, 184, 0.1);
    border: 1px solid rgba(23, 162, 184, 0.3);
    color: rgba(255, 255, 255, 0.9);
}

.remember-me-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 0;
    font-weight: 500;
    color: #495057;
    transition: color 0.3s ease;
}

.remember-me-label:hover {
    color: #667eea;
}

.remember-me-icon {
    margin-left: 8px;
    color: #667eea;
    font-size: 16px;
    transition: all 0.3s ease;
}

.remember-me-hint {
    font-size: 12px;
    color: #6c757d;
    font-weight: 400;
    margin-right: 5px;
    opacity: 0.8;
}

.last-login-info {
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 6px;
    font-size: 12px;
    color: #667eea;
    display: flex;
    align-items: center;
    animation: fadeIn 0.5s ease-out;
}

.last-login-info i {
    margin-left: 6px;
    font-size: 11px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.security-warning {
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 6px;
    font-size: 11px;
    color: #856404;
    display: flex;
    align-items: center;
    animation: fadeIn 0.5s ease-out;
}

.security-warning i {
    margin-left: 6px;
    font-size: 11px;
    color: #ffc107;
}

.clear-data-btn {
    background: none;
    border: none;
    color: #dc3545;
    font-size: 12px;
    text-decoration: none;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.3s ease;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-data-btn:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: #c82333;
    transform: translateY(-1px);
}

.clear-data-btn i {
    margin-left: 5px;
    font-size: 11px;
}

.form-check-input {
    width: 18px;
    height: 18px;
    margin-left: 10px;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
    transform: scale(1.1);
}

.form-check-input:checked + .remember-me-label .remember-me-icon {
    color: #28a745;
    transform: scale(1.2);
}

.form-check-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تأثير عند تحميل البيانات المحفوظة */
.credentials-loaded {
    animation: credentialsGlow 1s ease-out;
}

@keyframes credentialsGlow {
    0% {
        background-color: rgba(40, 167, 69, 0.1);
        border-color: #28a745;
    }
    100% {
        background-color: transparent;
        border-color: #dee2e6;
    }
}

/* تحسين مظهر الحقول المملوءة مسبقاً */
.form-control.pre-filled {
    background-color: #f8f9fa;
    border-color: #28a745;
}

.form-control.pre-filled:focus {
    background-color: white;
    border-color: #667eea;
}

/* رسالة تأكيد حفظ البيانات */
.save-confirmation {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    z-index: 1000;
    animation: slideInRight 0.5s ease-out;
    font-size: 14px;
    font-weight: 500;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.save-confirmation.fade-out {
    animation: fadeOut 0.5s ease-out forwards;
}

@keyframes fadeOut {
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* تحسين مظهر زر تسجيل الدخول عند وجود بيانات محفوظة */
.btn-login.with-saved-data {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-color: #28a745;
}

.btn-login.with-saved-data:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    border-color: #1e7e34;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
}

/* النموذج المحسن */
.form-section {
    padding: 30px 40px 40px;
}

.modern-login-form {
    position: relative;
    z-index: 2;
}

.validation-summary-modern {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
    border: 1px solid rgba(220, 53, 69, 0.2);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 25px;
    color: #dc3545;
    font-weight: 500;
    animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* مجموعة الحقول المحسنة */
.input-group-modern {
    margin-bottom: 25px;
    position: relative;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #667eea;
    font-size: 18px;
    z-index: 3;
    transition: all 0.3s ease;
}

.form-input-modern {
    width: 100%;
    padding: 20px 60px 20px 55px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    font-size: 16px;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 2;
}

.form-input-modern:focus {
    outline: none;
    border-color: #667eea;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.form-input-modern:focus + .floating-label {
    transform: translateY(-35px) scale(0.85);
    color: #667eea;
    font-weight: 600;
}

.form-input-modern:not(:placeholder-shown) + .floating-label {
    transform: translateY(-35px) scale(0.85);
    color: #2c3e50;
}

.floating-label {
    position: absolute;
    left: 55px;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
    font-size: 16px;
    font-weight: 500;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(to bottom, transparent 40%, rgba(255, 255, 255, 0.9) 40%);
    padding: 0 8px;
    z-index: 3;
}

.input-border {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
}

.form-input-modern:focus ~ .input-border {
    width: 100%;
}

.password-toggle-modern {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #7f8c8d;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    z-index: 3;
}

.password-toggle-modern:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.error-message {
    display: block;
    margin-top: 8px;
    color: #dc3545;
    font-size: 14px;
    font-weight: 500;
    animation: errorFadeIn 0.3s ease;
}

@keyframes errorFadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* زر تسجيل الدخول المحسن */
.login-button-modern {
    width: 100%;
    padding: 18px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 16px;
    color: white;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    margin: 30px 0 25px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-button-modern:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.login-button-modern:active {
    transform: translateY(-1px);
}

.button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.button-loader {
    display: flex;
    align-items: center;
    justify-content: center;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
