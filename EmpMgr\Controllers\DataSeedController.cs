using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EmpMgr.Data;
using EmpMgr.Models;

namespace EmpMgr.Controllers
{
    public class DataSeedController : Controller
    {
        private readonly ApplicationDbContext _context;

        public DataSeedController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public IActionResult Index()
        {
            return View();
        }

        [HttpGet]
        public IActionResult Test()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> SeedBasicData()
        {
            try
            {
                // إضافة معاهد أساسية إذا لم تكن موجودة
                if (!await _context.Institutes.AnyAsync())
                {
                    var institutes = new List<Institute>
                    {
                        new Institute { Name = "معهد الإدارة التقني", Type = InstituteType.Administrative, IsActive = true, CreatedDate = DateTime.Now },
                        new Institute { Name = "المعهد التقني الطبي", Type = InstituteType.Medical, IsActive = true, CreatedDate = DateTime.Now },
                        new Institute { Name = "المعهد التقني الهندسي", Type = InstituteType.Technical, IsActive = true, CreatedDate = DateTime.Now }
                    };

                    _context.Institutes.AddRange(institutes);
                    await _context.SaveChangesAsync();
                }

                // إضافة كليات أساسية للجامعة الأولى
                if (!await _context.Colleges.AnyAsync())
                {
                    var university = await _context.Universities.FirstOrDefaultAsync();
                    if (university != null)
                    {
                        var colleges = new List<College>
                        {
                                new College { Name = "كلية الطب", UniversityId = university.Id, Type = CollegeType.Medical, IsActive = true, CreatedDate = DateTime.Now },
                            new College { Name = "كلية الهندسة", UniversityId = university.Id, Type = CollegeType.Engineering, IsActive = true, CreatedDate = DateTime.Now },
                            new College { Name = "كلية العلوم", UniversityId = university.Id, Type = CollegeType.Scientific, IsActive = true, CreatedDate = DateTime.Now },
                            new College { Name = "كلية الآداب", UniversityId = university.Id, Type = CollegeType.Humanities, IsActive = true, CreatedDate = DateTime.Now },
                            new College { Name = "كلية القانون", UniversityId = university.Id, Type = CollegeType.Legal, IsActive = true, CreatedDate = DateTime.Now },
                            new College { Name = "كلية الإدارة والاقتصاد", UniversityId = university.Id, Type = CollegeType.Administrative, IsActive = true, CreatedDate = DateTime.Now },
                            new College { Name = "كلية التربية", UniversityId = university.Id, Type = CollegeType.Educational, IsActive = true, CreatedDate = DateTime.Now },
                            new College { Name = "كلية الصيدلة", UniversityId = university.Id, Type = CollegeType.Medical, IsActive = true, CreatedDate = DateTime.Now },
                            new College { Name = "كلية طب الأسنان", UniversityId = university.Id, Type = CollegeType.Medical, IsActive = true, CreatedDate = DateTime.Now },
                            new College { Name = "كلية الزراعة", UniversityId = university.Id, Type = CollegeType.Scientific, IsActive = true, CreatedDate = DateTime.Now }
                        };

                        _context.Colleges.AddRange(colleges);
                        await _context.SaveChangesAsync();
                    }
                }

                // التحقق من وجود المعاهد أولاً
                var existingInstitutes = await _context.Institutes.Take(3).ToListAsync();
                if (existingInstitutes.Count >= 3)
                {
                    // إضافة أقسام أساسية للمعاهد
                    if (!await _context.Departments.AnyAsync())
                    {
                        var departments = new List<Department>
                        {
                            // أقسام معهد الإدارة التقني (ID = existingInstitutes[0].Id)
                            new Department { Name = "قسم تقنيات المحاسبة", InstituteId = existingInstitutes[0].Id, Type = DepartmentType.Accounting, IsActive = true, CreatedDate = DateTime.Now },
                            new Department { Name = "قسم تقنيات الإدارة", InstituteId = existingInstitutes[0].Id, Type = DepartmentType.Administrative, IsActive = true, CreatedDate = DateTime.Now },
                            new Department { Name = "قسم تقنيات الحاسوب", InstituteId = existingInstitutes[0].Id, Type = DepartmentType.Computer, IsActive = true, CreatedDate = DateTime.Now },
                            new Department { Name = "قسم التقنيات القانونية", InstituteId = existingInstitutes[0].Id, Type = DepartmentType.Legal, IsActive = true, CreatedDate = DateTime.Now },

                            // أقسام المعهد التقني الطبي (ID = existingInstitutes[1].Id)
                            new Department { Name = "قسم تقنيات التمريض", InstituteId = existingInstitutes[1].Id, Type = DepartmentType.Medical, IsActive = true, CreatedDate = DateTime.Now },
                            new Department { Name = "قسم تقنيات المختبرات الطبية", InstituteId = existingInstitutes[1].Id, Type = DepartmentType.Medical, IsActive = true, CreatedDate = DateTime.Now },
                            new Department { Name = "قسم تقنيات الأشعة", InstituteId = existingInstitutes[1].Id, Type = DepartmentType.Medical, IsActive = true, CreatedDate = DateTime.Now },

                            // أقسام المعهد التقني الهندسي (ID = existingInstitutes[2].Id)
                            new Department { Name = "قسم تقنيات الهندسة المدنية", InstituteId = existingInstitutes[2].Id, Type = DepartmentType.Engineering, IsActive = true, CreatedDate = DateTime.Now },
                            new Department { Name = "قسم تقنيات الهندسة الكهربائية", InstituteId = existingInstitutes[2].Id, Type = DepartmentType.Engineering, IsActive = true, CreatedDate = DateTime.Now },
                            new Department { Name = "قسم تقنيات الهندسة الميكانيكية", InstituteId = existingInstitutes[2].Id, Type = DepartmentType.Engineering, IsActive = true, CreatedDate = DateTime.Now }
                        };

                        _context.Departments.AddRange(departments);
                        await _context.SaveChangesAsync();
                    }
                }



                return Json(new { success = true, message = "تم إضافة البيانات الأساسية بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"حدث خطأ: {ex.Message}" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> CheckData()
        {
            var stats = new
            {
                Colleges = await _context.Colleges.CountAsync(),
                Departments = await _context.Departments.CountAsync(),
                Universities = await _context.Universities.CountAsync(),
                Institutes = await _context.Institutes.CountAsync(),
                Ministries = await _context.Ministries.CountAsync(),
                Agencies = await _context.Agencies.CountAsync(),
                Directorates = await _context.Directorates.CountAsync(),
                GovernmentDepartments = await _context.GovernmentDepartments.CountAsync(),
                Divisions = await _context.Divisions.CountAsync()
            };

            return Json(stats);
        }

        [HttpGet]
        public async Task<IActionResult> CheckDetailedData()
        {
            var universities = await _context.Universities.Select(u => new { u.Id, u.Name }).ToListAsync();
            var institutes = await _context.Institutes.Select(i => new { i.Id, i.Name }).ToListAsync();
            var colleges = await _context.Colleges.Select(c => new { c.Id, c.Name, c.UniversityId }).ToListAsync();
            var departments = await _context.Departments.Select(d => new { d.Id, d.Name, d.InstituteId }).ToListAsync();

            var data = new
            {
                Universities = universities,
                Institutes = institutes,
                Colleges = colleges,
                Departments = departments
            };

            return Json(data);
        }

        [HttpGet]
        public async Task<IActionResult> TestDepartments(int instituteId = 1)
        {
            try
            {
                var departments = await _context.Departments
                    .Where(d => d.InstituteId == instituteId && d.IsActive)
                    .Select(d => new {
                        id = d.Id,
                        name = d.Name,
                        instituteId = d.InstituteId,
                        instituteName = d.Institute != null ? d.Institute.Name : "Unknown"
                    })
                    .ToListAsync();

                var result = new
                {
                    instituteId = instituteId,
                    departmentCount = departments.Count,
                    departments = departments,
                    message = departments.Any() ? "Departments found" : "No departments found for this institute"
                };

                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SeedGovernmentData()
        {
            try
            {
                // إضافة وزارات تجريبية
                if (!await _context.Ministries.AnyAsync())
                {
                    var ministries = new List<Ministry>
                    {
                        new Ministry { Name = "وزارة التربية", Code = "EDU", IsActive = true, CreatedDate = DateTime.Now },
                        new Ministry { Name = "وزارة الصحة", Code = "HEALTH", IsActive = true, CreatedDate = DateTime.Now },
                        new Ministry { Name = "وزارة الداخلية", Code = "INTERIOR", IsActive = true, CreatedDate = DateTime.Now }
                    };

                    _context.Ministries.AddRange(ministries);
                    await _context.SaveChangesAsync();
                }

                // إضافة وكالات تجريبية
                if (!await _context.Agencies.AnyAsync())
                {
                    var ministries = await _context.Ministries.ToListAsync();
                    var agencies = new List<Agency>();

                    foreach (var ministry in ministries)
                    {
                        if (ministry.Name.Contains("التربية"))
                        {
                            agencies.Add(new Agency { Name = "وكالة التعليم العام", MinistryId = ministry.Id, IsActive = true, CreatedDate = DateTime.Now });
                            agencies.Add(new Agency { Name = "وكالة التعليم العالي", MinistryId = ministry.Id, IsActive = true, CreatedDate = DateTime.Now });
                        }
                        else if (ministry.Name.Contains("الصحة"))
                        {
                            agencies.Add(new Agency { Name = "وكالة الخدمات الطبية", MinistryId = ministry.Id, IsActive = true, CreatedDate = DateTime.Now });
                            agencies.Add(new Agency { Name = "وكالة الصحة العامة", MinistryId = ministry.Id, IsActive = true, CreatedDate = DateTime.Now });
                        }
                        else if (ministry.Name.Contains("الداخلية"))
                        {
                            agencies.Add(new Agency { Name = "وكالة الأمن العام", MinistryId = ministry.Id, IsActive = true, CreatedDate = DateTime.Now });
                            agencies.Add(new Agency { Name = "وكالة الجوازات", MinistryId = ministry.Id, IsActive = true, CreatedDate = DateTime.Now });
                        }
                    }

                    _context.Agencies.AddRange(agencies);
                    await _context.SaveChangesAsync();
                }

                // إضافة مديريات تجريبية
                if (!await _context.Directorates.AnyAsync())
                {
                    var agency1 = await _context.Agencies.FirstAsync();
                    var directorates = new List<Directorate>
                    {
                        new Directorate { Name = "مديرية التعليم الابتدائي", AgencyId = agency1.Id, IsActive = true, CreatedDate = DateTime.Now },
                        new Directorate { Name = "مديرية التعليم الثانوي", AgencyId = agency1.Id, IsActive = true, CreatedDate = DateTime.Now }
                    };

                    _context.Directorates.AddRange(directorates);
                    await _context.SaveChangesAsync();
                }

                // إضافة أقسام حكومية تجريبية
                if (!await _context.GovernmentDepartments.AnyAsync())
                {
                    var directorate1 = await _context.Directorates.FirstAsync();
                    var departments = new List<GovernmentDepartment>
                    {
                        new GovernmentDepartment { Name = "قسم المناهج", DirectorateId = directorate1.Id, IsActive = true, CreatedDate = DateTime.Now },
                        new GovernmentDepartment { Name = "قسم الامتحانات", DirectorateId = directorate1.Id, IsActive = true, CreatedDate = DateTime.Now }
                    };

                    _context.GovernmentDepartments.AddRange(departments);
                    await _context.SaveChangesAsync();
                }

                // إضافة شعب تجريبية
                if (!await _context.Divisions.AnyAsync())
                {
                    var department1 = await _context.GovernmentDepartments.FirstAsync();
                    var divisions = new List<Division>
                    {
                        new Division { Name = "شعبة اللغة العربية", GovernmentDepartmentId = department1.Id, IsActive = true, CreatedDate = DateTime.Now },
                        new Division { Name = "شعبة الرياضيات", GovernmentDepartmentId = department1.Id, IsActive = true, CreatedDate = DateTime.Now }
                    };

                    _context.Divisions.AddRange(divisions);
                    await _context.SaveChangesAsync();
                }

                TempData["SuccessMessage"] = "تم إنشاء البيانات الحكومية التجريبية بنجاح";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"حدث خطأ أثناء إنشاء البيانات: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        public async Task<IActionResult> ResetGovernmentData()
        {
            try
            {
                // حذف البيانات الموجودة
                _context.Divisions.RemoveRange(_context.Divisions);
                _context.GovernmentDepartments.RemoveRange(_context.GovernmentDepartments);
                _context.Directorates.RemoveRange(_context.Directorates);
                _context.Agencies.RemoveRange(_context.Agencies);
                _context.Ministries.RemoveRange(_context.Ministries);
                await _context.SaveChangesAsync();

                // إعادة إنشاء البيانات
                return await SeedGovernmentData();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"حدث خطأ أثناء إعادة تعيين البيانات: {ex.Message}";
                return RedirectToAction("Index");
            }
        }
    }
}
