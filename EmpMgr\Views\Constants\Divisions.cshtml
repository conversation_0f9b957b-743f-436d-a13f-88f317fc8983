@model IEnumerable<EmpMgr.Models.Division>
@using EmpMgr.Extensions

@{
    ViewData["Title"] = "إدارة الشعب";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-project-diagram me-2"></i>
                        إدارة الشعب
                    </h4>
                    <div>
                        <a asp-action="CreateDivision" class="btn btn-light btn-sm">
                            <i class="fas fa-plus me-1"></i>
                            إضافة شعبة جديدة
                        </a>
                        <a asp-action="Index" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة للثوابت
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="divisionsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>اسم الشعبة</th>
                                        <th>القسم الحكومي</th>
                                        <th>المديرية</th>
                                        <th>الوكالة</th>
                                        <th>الوزارة</th>
                                        <th>الرمز</th>
                                        <th>عدد الموظفين</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var division in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@division.Name</strong>
                                                @if (!string.IsNullOrEmpty(division.Description))
                                                {
                                                    <br><small class="text-muted">@division.Description</small>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-success">@division.GovernmentDepartment.Name</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">@division.GovernmentDepartment.Directorate.Name</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-dark">@division.GovernmentDepartment.Directorate.Agency.Name</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-danger">@division.GovernmentDepartment.Directorate.Agency.Ministry.Name</span>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(division.Code))
                                                {
                                                    <span class="badge bg-secondary">@division.Code</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-success">@division.Employees.Count</span>
                                            </td>
                                            <td>
                                                @if (division.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="EditDivision" asp-route-id="@division.Id" 
                                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="DeleteDivision" asp-route-id="@division.Id" 
                                                       class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-project-diagram fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد شعب مسجلة</h5>
                            <p class="text-muted">ابدأ بإضافة شعبة جديدة لإدارة البنية الهرمية الحكومية</p>
                            <a asp-action="CreateDivision" class="btn btn-info">
                                <i class="fas fa-plus me-2"></i>
                                إضافة شعبة جديدة
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#divisionsTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
                },
                "responsive": true,
                "order": [[4, "asc"], [3, "asc"], [2, "asc"], [1, "asc"], [0, "asc"]],
                "pageLength": 25
            });
        });
    </script>
}

<style>
    .table th {
        border-top: none;
        font-weight: 600;
    }
    
    .btn-group .btn {
        margin: 0 2px;
    }
    
    .badge {
        font-size: 0.75em;
    }
</style>
