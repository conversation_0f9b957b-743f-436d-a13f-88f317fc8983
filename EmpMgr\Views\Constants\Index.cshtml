@{
    ViewData["Title"] = "إدارة الثوابت";
}

<div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        إحصائيات الثوابت
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stat-item">
                                <div class="stat-icon bg-primary">
                                    <i class="fas fa-medal"></i>
                                </div>
                                <h4 class="stat-number text-primary" id="totalRanks">0</h4>
                                <p class="stat-label">إجمالي الرتب</p>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stat-item">
                                <div class="stat-icon bg-success">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <h4 class="stat-number text-success" id="totalProvinces">0</h4>
                                <p class="stat-label">إجمالي المحافظات</p>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stat-item">
                                <div class="stat-icon bg-info">
                                    <i class="fas fa-university"></i>
                                </div>
                                <h4 class="stat-number text-info" id="totalUniversities">0</h4>
                                <p class="stat-label">إجمالي الجامعات</p>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stat-item">
                                <div class="stat-icon bg-warning">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                <h4 class="stat-number text-warning" id="totalInstitutes">0</h4>
                                <p class="stat-label">إجمالي المعاهد</p>
                            </div>
                        </div>
                    </div>
                    <div class="row text-center">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stat-item">
                                <div class="stat-icon bg-purple">
                                    <i class="fas fa-building"></i>
                                </div>
                                <h4 class="stat-number text-purple" id="totalColleges">0</h4>
                                <p class="stat-label">إجمالي الكليات</p>
                            </div>
                        </div>
                        <div class="col-lg-9 col-md-6 mb-3">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>الترابط الهرمي:</strong> الكليات مرتبطة بالجامعات، مما يوفر تصنيفاً أكثر دقة للموظفين
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        إدارة الثوابت
                    </h4>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- إدارة الرتب -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-medal fa-4x text-primary"></i>
                                    </div>
                                    <h5 class="card-title">إدارة الرتب</h5>
                                    <p class="card-text">إضافة وتعديل وحذف رتب الضباط والمنتسبين</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Ranks" class="btn btn-primary">
                                            <i class="fas fa-list me-2"></i>
                                            عرض الرتب
                                        </a>
                                        <a asp-action="CreateRank" class="btn btn-outline-primary">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة رتبة جديدة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- إدارة المحافظات -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-map-marker-alt fa-4x text-success"></i>
                                    </div>
                                    <h5 class="card-title">إدارة المحافظات</h5>
                                    <p class="card-text">إضافة وتعديل وحذف المحافظات والمناطق الجغرافية</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Provinces" class="btn btn-success">
                                            <i class="fas fa-list me-2"></i>
                                            عرض المحافظات
                                        </a>
                                        <a asp-action="CreateProvince" class="btn btn-outline-success">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة محافظة جديدة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة الجامعات -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-info">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-university fa-4x text-info"></i>
                                    </div>
                                    <h5 class="card-title">إدارة الجامعات</h5>
                                    <p class="card-text">إضافة وتعديل وحذف الجامعات والمؤسسات التعليمية العليا</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Universities" class="btn btn-info">
                                            <i class="fas fa-list me-2"></i>
                                            عرض الجامعات
                                        </a>
                                        <a asp-action="CreateUniversity" class="btn btn-outline-info">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة جامعة جديدة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة المعاهد -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-warning">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-graduation-cap fa-4x text-warning"></i>
                                    </div>
                                    <h5 class="card-title">إدارة المعاهد</h5>
                                    <p class="card-text">إضافة وتعديل وحذف المعاهد التقنية والمهنية والطبية</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Institutes" class="btn btn-warning">
                                            <i class="fas fa-list me-2"></i>
                                            عرض المعاهد
                                        </a>
                                        <a asp-action="CreateInstitute" class="btn btn-outline-warning">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة معهد جديد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة الكليات -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-purple">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-building fa-4x text-purple"></i>
                                    </div>
                                    <h5 class="card-title">إدارة الكليات</h5>
                                    <p class="card-text">إضافة وتعديل وحذف الكليات المرتبطة بالجامعات</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Colleges" class="btn btn-purple">
                                            <i class="fas fa-list me-2"></i>
                                            عرض الكليات
                                        </a>
                                        <a asp-action="CreateCollege" class="btn btn-outline-purple">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة كلية جديدة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة الأقسام -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-teal">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-sitemap fa-4x text-teal"></i>
                                    </div>
                                    <h5 class="card-title">إدارة الأقسام</h5>
                                    <p class="card-text">إضافة وتعديل وحذف الأقسام المرتبطة بالمعاهد</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Departments" class="btn btn-teal">
                                            <i class="fas fa-list me-2"></i>
                                            عرض الأقسام
                                        </a>
                                        <a asp-action="CreateDepartment" class="btn btn-outline-teal">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة قسم جديد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة الوزارات -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-danger">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-landmark fa-4x text-danger"></i>
                                    </div>
                                    <h5 class="card-title">إدارة الوزارات</h5>
                                    <p class="card-text">إضافة وتعديل وحذف الوزارات والمؤسسات الحكومية</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Ministries" class="btn btn-danger">
                                            <i class="fas fa-list me-2"></i>
                                            عرض الوزارات
                                        </a>
                                        <a asp-action="CreateMinistry" class="btn btn-outline-danger">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة وزارة جديدة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة الوكالات -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-dark">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-building fa-4x text-dark"></i>
                                    </div>
                                    <h5 class="card-title">إدارة الوكالات</h5>
                                    <p class="card-text">إضافة وتعديل وحذف الوكالات التابعة للوزارات</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Agencies" class="btn btn-dark">
                                            <i class="fas fa-list me-2"></i>
                                            عرض الوكالات
                                        </a>
                                        <a asp-action="CreateAgency" class="btn btn-outline-dark">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة وكالة جديدة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة المديريات -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-city fa-4x text-primary"></i>
                                    </div>
                                    <h5 class="card-title">إدارة المديريات</h5>
                                    <p class="card-text">إضافة وتعديل وحذف المديريات التابعة للوكالات</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Directorates" class="btn btn-primary">
                                            <i class="fas fa-list me-2"></i>
                                            عرض المديريات
                                        </a>
                                        <a asp-action="CreateDirectorate" class="btn btn-outline-primary">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة مديرية جديدة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة الأقسام الحكومية -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-layer-group fa-4x text-success"></i>
                                    </div>
                                    <h5 class="card-title">إدارة الأقسام الحكومية</h5>
                                    <p class="card-text">إضافة وتعديل وحذف الأقسام التابعة للمديريات</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="GovernmentDepartments" class="btn btn-success">
                                            <i class="fas fa-list me-2"></i>
                                            عرض الأقسام الحكومية
                                        </a>
                                        <a asp-action="CreateGovernmentDepartment" class="btn btn-outline-success">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة قسم حكومي جديد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة الشعب -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-info">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-project-diagram fa-4x text-info"></i>
                                    </div>
                                    <h5 class="card-title">إدارة الشعب</h5>
                                    <p class="card-text">إضافة وتعديل وحذف الشعب التابعة للأقسام الحكومية</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Divisions" class="btn btn-info">
                                            <i class="fas fa-list me-2"></i>
                                            عرض الشعب
                                        </a>
                                        <a asp-action="CreateDivision" class="btn btn-outline-info">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة شعبة جديدة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات النظام -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-secondary">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-tools fa-4x text-secondary"></i>
                                    </div>
                                    <h5 class="card-title">إعدادات النظام</h5>
                                    <p class="card-text">إدارة إعدادات النظام العامة والمتقدمة</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="SystemSettings" class="btn btn-secondary">
                                            <i class="fas fa-cog me-2"></i>
                                            الإعدادات
                                        </a>
                                        <a asp-action="AuditLogs" class="btn btn-outline-secondary">
                                            <i class="fas fa-history me-2"></i>
                                            سجل العمليات
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- النسخ الاحتياطي -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-dark">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-database fa-4x text-dark"></i>
                                    </div>
                                    <h5 class="card-title">النسخ الاحتياطي</h5>
                                    <p class="card-text">إنشاء واستعادة النسخ الاحتياطية لقاعدة البيانات</p>
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-dark" onclick="createBackup()">
                                            <i class="fas fa-download me-2"></i>
                                            إنشاء نسخة احتياطية
                                        </button>
                                        <button type="button" class="btn btn-outline-dark" onclick="restoreBackup()">
                                            <i class="fas fa-upload me-2"></i>
                                            استعادة نسخة احتياطية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إحصائيات سريعة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        إحصائيات سريعة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <div class="border-end">
                                                <h4 class="text-primary mb-1">16</h4>
                                                <small class="text-muted">الرتب المتاحة</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="border-end">
                                                <h4 class="text-success mb-1">18</h4>
                                                <small class="text-muted">المحافظات</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="border-end">
                                                <h4 class="text-info mb-1">7</h4>
                                                <small class="text-muted">الجامعات</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <h4 class="text-warning mb-1">4</h4>
                                            <small class="text-muted">المعاهد</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // تحميل الإحصائيات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadConstantsStatistics();
        });

        async function loadConstantsStatistics() {
            try {
                // تحميل إحصائيات الرتب
                const ranksResponse = await fetch('/Constants/GetRanks');
                if (ranksResponse.ok) {
                    const ranks = await ranksResponse.json();
                    animateCounter('totalRanks', ranks.length);
                }

                // تحميل إحصائيات المحافظات
                const provincesResponse = await fetch('/Constants/GetProvinces');
                if (provincesResponse.ok) {
                    const provinces = await provincesResponse.json();
                    animateCounter('totalProvinces', provinces.length);
                }

                // تحميل إحصائيات الجامعات
                const universitiesResponse = await fetch('/Constants/GetUniversities');
                if (universitiesResponse.ok) {
                    const universities = await universitiesResponse.json();
                    animateCounter('totalUniversities', universities.length);
                }

                // تحميل إحصائيات المعاهد
                const institutesResponse = await fetch('/Constants/GetInstitutes');
                if (institutesResponse.ok) {
                    const institutes = await institutesResponse.json();
                    animateCounter('totalInstitutes', institutes.length);
                }

                // تحميل إحصائيات الكليات
                const collegesResponse = await fetch('/Constants/GetColleges');
                if (collegesResponse.ok) {
                    const colleges = await collegesResponse.json();
                    animateCounter('totalColleges', colleges.length);
                }
            } catch (error) {
                console.warn('لا يمكن تحميل الإحصائيات:', error);
                // عرض قيم افتراضية في حالة الخطأ
                document.getElementById('totalRanks').textContent = '0';
                document.getElementById('totalProvinces').textContent = '0';
                document.getElementById('totalUniversities').textContent = '0';
                document.getElementById('totalInstitutes').textContent = '0';
            }
        }

        function animateCounter(elementId, targetValue) {
            const element = document.getElementById(elementId);
            let currentValue = 0;
            const increment = targetValue > 0 ? targetValue / 30 : 0;

            if (targetValue === 0) {
                element.textContent = '0';
                return;
            }

            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= targetValue) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(currentValue);
            }, 50);
        }

        function createBackup() {
            if (confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')) {
                // سيتم تطوير هذه الوظيفة لاحقاً
                alert('سيتم تطوير وظيفة النسخ الاحتياطي قريباً');
            }
        }

        function restoreBackup() {
            if (confirm('هل تريد استعادة نسخة احتياطية؟ سيتم استبدال البيانات الحالية!')) {
                // سيتم تطوير هذه الوظيفة لاحقاً
                alert('سيتم تطوير وظيفة استعادة النسخ الاحتياطية قريباً');
            }
        }
    </script>
}

@section Styles {
    <style>
        .stat-item {
            padding: 20px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 1.5rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .card {
            transition: transform 0.2s ease-in-out;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        /* ألوان بنفسجية للكليات */
        .bg-purple {
            background-color: #6f42c1 !important;
        }

        .text-purple {
            color: #6f42c1 !important;
        }

        .border-purple {
            border-color: #6f42c1 !important;
        }

        .btn-purple {
            background-color: #6f42c1;
            border-color: #6f42c1;
            color: white;
        }

        .btn-purple:hover {
            background-color: #5a359a;
            border-color: #5a359a;
            color: white;
        }

        .btn-outline-purple {
            border-color: #6f42c1;
            color: #6f42c1;
        }

        .btn-outline-purple:hover {
            background-color: #6f42c1;
            border-color: #6f42c1;
            color: white;
        }
    </style>
}
