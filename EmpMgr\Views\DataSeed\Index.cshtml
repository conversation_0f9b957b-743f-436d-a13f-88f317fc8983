@{
    ViewData["Title"] = "إضافة البيانات الأساسية";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-database me-2"></i>
                        إضافة البيانات الأساسية
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        هذه الصفحة تساعدك على إضافة البيانات الأساسية للكليات والأقسام والتخصصات
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>إضافة البيانات</h5>
                                </div>
                                <div class="card-body">
                                    <button type="button" class="btn btn-primary btn-lg w-100 mb-3" onclick="addBasicData()">
                                        <i class="fas fa-plus me-2"></i>
                                        إضافة البيانات الأساسية
                                    </button>

                                    <button type="button" class="btn btn-success btn-lg w-100 mb-3" onclick="addGovernmentData()">
                                        <i class="fas fa-building me-2"></i>
                                        إضافة البيانات الحكومية
                                    </button>

                                    <button type="button" class="btn btn-warning btn-lg w-100 mb-3" onclick="resetGovernmentData()">
                                        <i class="fas fa-redo me-2"></i>
                                        إعادة تعيين البيانات الحكومية
                                    </button>

                                    <button type="button" class="btn btn-info btn-lg w-100" onclick="checkData()">
                                        <i class="fas fa-search me-2"></i>
                                        فحص البيانات الموجودة
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>النتائج</h5>
                                </div>
                                <div class="card-body">
                                    <div id="results">
                                        <p class="text-muted">انقر على الأزرار لرؤية النتائج</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>البيانات التي سيتم إضافتها</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <h6>الكليات (5 كليات):</h6>
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-check text-success me-2"></i>كلية الطب</li>
                                                <li><i class="fas fa-check text-success me-2"></i>كلية الهندسة</li>
                                                <li><i class="fas fa-check text-success me-2"></i>كلية العلوم</li>
                                                <li><i class="fas fa-check text-success me-2"></i>كلية الآداب</li>
                                                <li><i class="fas fa-check text-success me-2"></i>كلية القانون</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-4">
                                            <h6>الأقسام (3 أقسام):</h6>
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-check text-success me-2"></i>قسم تقنيات المحاسبة</li>
                                                <li><i class="fas fa-check text-success me-2"></i>قسم تقنيات الإدارة</li>
                                                <li><i class="fas fa-check text-success me-2"></i>قسم تقنيات الحاسوب</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-4">
                                            <h6>التخصصات:</h6>
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-check text-success me-2"></i>تخصصات الكليات</li>
                                                <li><i class="fas fa-check text-success me-2"></i>تخصصات الأقسام</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    async function addBasicData() {
        const button = event.target;
        const originalText = button.innerHTML;
        
        // تغيير النص لإظهار التحميل
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';
        button.disabled = true;
        
        try {
            const response = await fetch('/DataSeed/SeedBasicData', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                document.getElementById('results').innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        ${result.message}
                    </div>
                `;
            } else {
                document.getElementById('results').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        ${result.message}
                    </div>
                `;
            }
        } catch (error) {
            document.getElementById('results').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    حدث خطأ: ${error.message}
                </div>
            `;
        } finally {
            // إعادة النص الأصلي
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    async function addGovernmentData() {
        const button = event.target;
        const originalText = button.innerHTML;

        // تغيير النص لإظهار التحميل
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';
        button.disabled = true;

        try {
            const response = await fetch('/DataSeed/SeedGovernmentData', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                document.getElementById('results').innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        تم إنشاء البيانات الحكومية بنجاح
                    </div>
                `;
            } else {
                document.getElementById('results').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        حدث خطأ أثناء إنشاء البيانات الحكومية
                    </div>
                `;
            }
        } catch (error) {
            document.getElementById('results').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    حدث خطأ: ${error.message}
                </div>
            `;
        } finally {
            // إعادة النص الأصلي
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    async function resetGovernmentData() {
        if (!confirm('هل أنت متأكد من رغبتك في إعادة تعيين جميع البيانات الحكومية؟ سيتم حذف جميع البيانات الموجودة وإعادة إنشائها.')) {
            return;
        }

        const button = event.target;
        const originalText = button.innerHTML;

        // تغيير النص لإظهار التحميل
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري إعادة التعيين...';
        button.disabled = true;

        try {
            const response = await fetch('/DataSeed/ResetGovernmentData', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                document.getElementById('results').innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        تم إعادة تعيين البيانات الحكومية بنجاح
                    </div>
                `;
            } else {
                document.getElementById('results').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        حدث خطأ أثناء إعادة تعيين البيانات الحكومية
                    </div>
                `;
            }
        } catch (error) {
            document.getElementById('results').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    حدث خطأ: ${error.message}
                </div>
            `;
        } finally {
            // إعادة النص الأصلي
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    async function checkData() {
        const button = event.target;
        const originalText = button.innerHTML;
        
        // تغيير النص لإظهار التحميل
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الفحص...';
        button.disabled = true;
        
        try {
            const response = await fetch('/DataSeed/CheckData');
            const data = await response.json();
            
            document.getElementById('results').innerHTML = `
                <div class="alert alert-info">
                    <h6>البيانات الموجودة:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">البيانات التعليمية:</h6>
                            <ul class="list-unstyled mb-0">
                                <li><strong>الجامعات:</strong> ${data.universities}</li>
                                <li><strong>المعاهد:</strong> ${data.institutes}</li>
                                <li><strong>الكليات:</strong> ${data.colleges}</li>
                                <li><strong>الأقسام:</strong> ${data.departments}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">البيانات الحكومية:</h6>
                            <ul class="list-unstyled mb-0">
                                <li><strong>الوزارات:</strong> ${data.ministries}</li>
                                <li><strong>الوكالات:</strong> ${data.agencies}</li>
                                <li><strong>المديريات:</strong> ${data.directorates}</li>
                                <li><strong>الأقسام الحكومية:</strong> ${data.governmentDepartments}</li>
                                <li><strong>الشعب:</strong> ${data.divisions}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            document.getElementById('results').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    حدث خطأ: ${error.message}
                </div>
            `;
        } finally {
            // إعادة النص الأصلي
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }
</script>
