using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using EmpMgr.Models;

namespace EmpMgr.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        // DbSets للجداول
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Rank> Ranks { get; set; }
        public DbSet<Province> Provinces { get; set; }
        public DbSet<University> Universities { get; set; }
        public DbSet<Institute> Institutes { get; set; }
        public DbSet<College> Colleges { get; set; }
        public DbSet<AcademicDepartment> AcademicDepartments { get; set; }
        public DbSet<Department> Departments { get; set; }

        // البنية الهرمية الحكومية
        public DbSet<Ministry> Ministries { get; set; }
        public DbSet<Agency> Agencies { get; set; }
        public DbSet<Directorate> Directorates { get; set; }
        public DbSet<GovernmentDepartment> GovernmentDepartments { get; set; }
        public DbSet<Division> Divisions { get; set; }

        // إعدادات النظام
        public DbSet<SystemSettings> SystemSettings { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // إعدادات الجداول
            builder.Entity<Employee>(entity =>
            {
                entity.HasIndex(e => e.EmployeeNumber).IsUnique();
                entity.HasIndex(e => e.StatisticalNumber).IsUnique();
                entity.HasIndex(e => new { e.FirstName, e.FatherName, e.GrandFatherName, e.GreatGrandFatherName, e.LastName }).IsUnique();
                
                entity.Property(e => e.EmployeeNumber).HasMaxLength(20);
                entity.Property(e => e.StatisticalNumber).HasMaxLength(20);
                entity.Property(e => e.FirstName).HasMaxLength(50);
                entity.Property(e => e.FatherName).HasMaxLength(50);
                entity.Property(e => e.GrandFatherName).HasMaxLength(50);
                entity.Property(e => e.GreatGrandFatherName).HasMaxLength(50);
                entity.Property(e => e.LastName).HasMaxLength(50);

                
                // إعدادات العنوان
                entity.Property(e => e.District).HasMaxLength(200);
                entity.Property(e => e.Subdistrict).HasMaxLength(200);
                entity.Property(e => e.Village).HasMaxLength(200);
                entity.Property(e => e.Neighborhood).HasMaxLength(200);
                entity.Property(e => e.Quarter).HasMaxLength(200);
                entity.Property(e => e.Alley).HasMaxLength(200);
                entity.Property(e => e.House).HasMaxLength(200);
                entity.Property(e => e.NearestLandmark).HasMaxLength(500);

                // العلاقات للترقيات
                entity.HasOne(e => e.CurrentRank)
                    .WithMany()
                    .HasForeignKey(e => e.CurrentRankId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            builder.Entity<Rank>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Name).HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
            });

            builder.Entity<Province>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Name).HasMaxLength(100);
                entity.Property(e => e.Code).HasMaxLength(10);
            });

            builder.Entity<University>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Name).HasMaxLength(200);
                entity.Property(e => e.Location).HasMaxLength(200);
            });

            builder.Entity<Institute>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Name).HasMaxLength(200);
                entity.Property(e => e.Location).HasMaxLength(200);
            });

            builder.Entity<College>(entity =>
            {
                entity.HasIndex(e => new { e.Name, e.UniversityId }).IsUnique();
                entity.Property(e => e.Name).HasMaxLength(200);
                entity.Property(e => e.Code).HasMaxLength(10);
                entity.Property(e => e.Description).HasMaxLength(500);

                // العلاقة مع الجامعة
                entity.HasOne(c => c.University)
                      .WithMany(u => u.Colleges)
                      .HasForeignKey(c => c.UniversityId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // إعدادات الأقسام الأكاديمية
            builder.Entity<AcademicDepartment>(entity =>
            {
                entity.HasIndex(e => new { e.Name, e.CollegeId }).IsUnique();
                entity.Property(e => e.Name).HasMaxLength(200);
                entity.Property(e => e.Code).HasMaxLength(10);
                entity.Property(e => e.Description).HasMaxLength(500);

                // العلاقة مع الكلية
                entity.HasOne(ad => ad.College)
                      .WithMany(c => c.AcademicDepartments)
                      .HasForeignKey(ad => ad.CollegeId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // تكوين جدول إعدادات النظام
            builder.Entity<SystemSettings>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.SystemName).HasMaxLength(200).IsRequired();
                entity.Property(e => e.OrganizationName).HasMaxLength(200).IsRequired();
                entity.Property(e => e.LogoPath).HasMaxLength(500);
                entity.Property(e => e.FaviconPath).HasMaxLength(500);
                entity.Property(e => e.PrimaryColor).HasMaxLength(7).IsRequired();
                entity.Property(e => e.SecondaryColor).HasMaxLength(7).IsRequired();
                entity.Property(e => e.SystemDescription).HasMaxLength(1000);
                entity.Property(e => e.SystemVersion).HasMaxLength(20).IsRequired();
                entity.Property(e => e.SupportEmail).HasMaxLength(200);
                entity.Property(e => e.SupportPhone).HasMaxLength(20);
                entity.Property(e => e.OrganizationAddress).HasMaxLength(500);
                entity.Property(e => e.OrganizationWebsite).HasMaxLength(200);
                entity.Property(e => e.LastUpdatedBy).HasMaxLength(200);
                entity.Property(e => e.DefaultLanguage).HasMaxLength(10).IsRequired();
                entity.Property(e => e.TimeZone).HasMaxLength(100).IsRequired();
            });

            builder.Entity<AuditLog>(entity =>
            {
                entity.Property(e => e.Action).HasMaxLength(50);
                entity.Property(e => e.TableName).HasMaxLength(100);
                entity.Property(e => e.RecordId).HasMaxLength(50);
                entity.Property(e => e.UserId).HasMaxLength(450);
                entity.Property(e => e.IpAddress).HasMaxLength(45);
            });

            // تم حذف البيانات الافتراضية للرتب
            // يمكن إضافة الرتب من خلال واجهة الإدارة

            // تم حذف البيانات الافتراضية للمحافظات
            // يمكن إضافة المحافظات من خلال واجهة الإدارة

            // تم حذف البيانات الافتراضية للجامعات
            // يمكن إضافة الجامعات من خلال واجهة الإدارة

            // تم حذف البيانات الافتراضية للمعاهد
            // يمكن إضافة المعاهد من خلال واجهة الإدارة

            // إعدادات الوزارات
            builder.Entity<Ministry>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Name).HasMaxLength(200);
                entity.Property(e => e.Code).HasMaxLength(10);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Location).HasMaxLength(200);
            });

            // إعدادات الوكالات
            builder.Entity<Agency>(entity =>
            {
                // فهرس فريد مركب على (اسم الوكالة، الوزارة) لمنع التكرار داخل نفس الوزارة
                entity.HasIndex(e => new { e.Name, e.MinistryId }).IsUnique();
                entity.Property(e => e.Name).HasMaxLength(200);
                entity.Property(e => e.Code).HasMaxLength(10);
                entity.Property(e => e.Description).HasMaxLength(500);

                // العلاقة مع الوزارة
                entity.HasOne(a => a.Ministry)
                      .WithMany(m => m.Agencies)
                      .HasForeignKey(a => a.MinistryId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // إعدادات المديريات
            builder.Entity<Directorate>(entity =>
            {
                entity.HasIndex(e => new { e.Name, e.AgencyId }).IsUnique();
                entity.Property(e => e.Name).HasMaxLength(200);
                entity.Property(e => e.Code).HasMaxLength(10);
                entity.Property(e => e.Description).HasMaxLength(500);

                // العلاقة مع الوكالة
                entity.HasOne(d => d.Agency)
                      .WithMany(a => a.Directorates)
                      .HasForeignKey(d => d.AgencyId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // إعدادات الأقسام الحكومية
            builder.Entity<GovernmentDepartment>(entity =>
            {
                entity.HasIndex(e => new { e.Name, e.DirectorateId }).IsUnique();
                entity.Property(e => e.Name).HasMaxLength(200);
                entity.Property(e => e.Code).HasMaxLength(10);
                entity.Property(e => e.Description).HasMaxLength(500);

                // العلاقة مع المديرية
                entity.HasOne(gd => gd.Directorate)
                      .WithMany(d => d.GovernmentDepartments)
                      .HasForeignKey(gd => gd.DirectorateId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // إعدادات الشعب
            builder.Entity<Division>(entity =>
            {
                entity.HasIndex(e => new { e.Name, e.GovernmentDepartmentId }).IsUnique();
                entity.Property(e => e.Name).HasMaxLength(200);
                entity.Property(e => e.Code).HasMaxLength(10);
                entity.Property(e => e.Description).HasMaxLength(500);

                // العلاقة مع القسم الحكومي
                entity.HasOne(div => div.GovernmentDepartment)
                      .WithMany(gd => gd.Divisions)
                      .HasForeignKey(div => div.GovernmentDepartmentId)
                      .OnDelete(DeleteBehavior.Restrict);
            });
        }
    }
}
