class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.maxNotifications = 5;
        this.defaultDuration = 5000;
        this.sounds = {
            success: '/sounds/success.mp3',
            error: '/sounds/error.mp3',
            warning: '/sounds/warning.mp3',
            info: '/sounds/info.mp3'
        };
        this.init();
    }

    // تهيئة النظام
    init() {
        this.createContainer();
        this.requestNotificationPermission();
        this.loadSounds();
    }

    // إنشاء حاوي الإشعارات
    createContainer() {
        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.className = 'notification-container';
        this.container.innerHTML = `
            <style>
                .notification-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    max-width: 400px;
                    pointer-events: none;
                }
                
                .notification {
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
                    margin-bottom: 15px;
                    padding: 20px;
                    transform: translateX(100%);
                    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
                    pointer-events: auto;
                    border-left: 5px solid;
                    position: relative;
                    overflow: hidden;
                }
                
                .notification.show {
                    transform: translateX(0);
                }
                
                .notification.success {
                    border-left-color: #28a745;
                    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                }
                
                .notification.error {
                    border-left-color: #dc3545;
                    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
                }
                
                .notification.warning {
                    border-left-color: #ffc107;
                    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                }
                
                .notification.info {
                    border-left-color: #17a2b8;
                    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
                }
                
                .notification-header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                }
                
                .notification-icon {
                    font-size: 24px;
                    margin-left: 15px;
                    margin-right: 0;
                }
                
                .notification-title {
                    font-weight: 700;
                    font-size: 16px;
                    margin: 0;
                    flex-grow: 1;
                }
                
                .notification-close {
                    background: none;
                    border: none;
                    font-size: 20px;
                    cursor: pointer;
                    opacity: 0.7;
                    transition: opacity 0.3s ease;
                }
                
                .notification-close:hover {
                    opacity: 1;
                }
                
                .notification-message {
                    font-size: 14px;
                    line-height: 1.5;
                    margin: 0;
                }
                
                .notification-actions {
                    margin-top: 15px;
                    display: flex;
                    gap: 10px;
                }
                
                .notification-btn {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 8px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }
                
                .notification-btn.primary {
                    background: #667eea;
                    color: white;
                }
                
                .notification-btn.secondary {
                    background: #6c757d;
                    color: white;
                }
                
                .notification-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                }
                
                .notification-progress {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    height: 3px;
                    background: rgba(0, 0, 0, 0.2);
                    transition: width linear;
                }
                
                .notification.success .notification-progress {
                    background: #28a745;
                }
                
                .notification.error .notification-progress {
                    background: #dc3545;
                }
                
                .notification.warning .notification-progress {
                    background: #ffc107;
                }
                
                .notification.info .notification-progress {
                    background: #17a2b8;
                }
                
                @media (max-width: 768px) {
                    .notification-container {
                        right: 10px;
                        left: 10px;
                        max-width: none;
                    }
                    
                    .notification {
                        margin-bottom: 10px;
                        padding: 15px;
                    }
                }
            </style>
        `;
        document.body.appendChild(this.container);
    }

    // طلب إذن الإشعارات
    async requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            try {
                const permission = await Notification.requestPermission();
                if (permission === 'granted') {
                    this.show('تم تفعيل الإشعارات', 'يمكنك الآن تلقي إشعارات من النظام', 'success');
                }
            } catch (error) {
                console.warn('لا يمكن طلب إذن الإشعارات:', error);
            }
        }
    }

    // تحميل الأصوات
    loadSounds() {
        Object.keys(this.sounds).forEach(type => {
            const audio = new Audio();
            audio.preload = 'auto';
            audio.src = this.sounds[type];
            this.sounds[type] = audio;
        });
    }

    // عرض إشعار
    show(title, message, type = 'info', options = {}) {
        const notification = this.createNotification(title, message, type, options);
        this.addNotification(notification);
        
        // تشغيل الصوت
        if (options.sound !== false) {
            this.playSound(type);
        }
        
        // إشعار المتصفح
        if (options.browserNotification && 'Notification' in window && Notification.permission === 'granted') {
            this.showBrowserNotification(title, message, type);
        }
        
        return notification.id;
    }

    // إنشاء إشعار
    createNotification(title, message, type, options) {
        const id = 'notification_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const duration = options.duration || this.defaultDuration;
        const persistent = options.persistent || false;
        
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        const notification = {
            id,
            title,
            message,
            type,
            duration,
            persistent,
            actions: options.actions || [],
            element: null,
            timer: null
        };

        // إنشاء العنصر
        const element = document.createElement('div');
        element.className = `notification ${type}`;
        element.id = id;
        
        let actionsHtml = '';
        if (notification.actions.length > 0) {
            actionsHtml = `
                <div class="notification-actions">
                    ${notification.actions.map(action => `
                        <button class="notification-btn ${action.style || 'primary'}" 
                                onclick="notificationSystem.handleAction('${id}', '${action.id}')">
                            ${action.text}
                        </button>
                    `).join('')}
                </div>
            `;
        }

        element.innerHTML = `
            <div class="notification-header">
                <i class="notification-icon ${icons[type]}"></i>
                <h6 class="notification-title">${title}</h6>
                <button class="notification-close" onclick="notificationSystem.hide('${id}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <p class="notification-message">${message}</p>
            ${actionsHtml}
            ${!persistent ? `<div class="notification-progress" style="width: 100%;"></div>` : ''}
        `;

        notification.element = element;
        return notification;
    }

    // إضافة إشعار للحاوي
    addNotification(notification) {
        // إزالة الإشعارات الزائدة
        while (this.notifications.length >= this.maxNotifications) {
            const oldest = this.notifications.shift();
            this.removeNotification(oldest);
        }

        this.notifications.push(notification);
        this.container.appendChild(notification.element);

        // تأثير الظهور
        setTimeout(() => {
            notification.element.classList.add('show');
        }, 100);

        // تشغيل العداد التنازلي
        if (!notification.persistent) {
            this.startProgressTimer(notification);
        }
    }

    // بدء العداد التنازلي
    startProgressTimer(notification) {
        const progressBar = notification.element.querySelector('.notification-progress');
        if (!progressBar) return;

        let width = 100;
        const interval = 50;
        const decrement = (interval / notification.duration) * 100;

        notification.timer = setInterval(() => {
            width -= decrement;
            progressBar.style.width = width + '%';

            if (width <= 0) {
                this.hide(notification.id);
            }
        }, interval);
    }

    // إخفاء إشعار
    hide(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (!notification) return;

        // إيقاف العداد
        if (notification.timer) {
            clearInterval(notification.timer);
        }

        // تأثير الاختفاء
        notification.element.classList.remove('show');
        
        setTimeout(() => {
            this.removeNotification(notification);
        }, 400);
    }

    // إزالة إشعار
    removeNotification(notification) {
        const index = this.notifications.indexOf(notification);
        if (index > -1) {
            this.notifications.splice(index, 1);
        }

        if (notification.element && notification.element.parentNode) {
            notification.element.parentNode.removeChild(notification.element);
        }

        if (notification.timer) {
            clearInterval(notification.timer);
        }
    }

    // معالجة إجراءات الإشعار
    handleAction(notificationId, actionId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (!notification) return;

        const action = notification.actions.find(a => a.id === actionId);
        if (action && action.callback) {
            action.callback();
        }

        // إخفاء الإشعار بعد تنفيذ الإجراء
        this.hide(notificationId);
    }

    // تشغيل الصوت
    playSound(type) {
        try {
            const audio = this.sounds[type];
            if (audio && typeof audio.play === 'function') {
                audio.currentTime = 0;
                audio.play().catch(e => console.warn('لا يمكن تشغيل الصوت:', e));
            }
        } catch (error) {
            console.warn('خطأ في تشغيل الصوت:', error);
        }
    }

    // إشعار المتصفح
    showBrowserNotification(title, message, type) {
        try {
            const notification = new Notification(title, {
                body: message,
                icon: `/images/icons/${type}.png`,
                badge: '/images/logo-small.png',
                tag: 'emp-mgr-notification',
                requireInteraction: false,
                silent: false
            });

            notification.onclick = () => {
                window.focus();
                notification.close();
            };

            // إغلاق تلقائي بعد 5 ثوان
            setTimeout(() => notification.close(), 5000);
        } catch (error) {
            console.warn('خطأ في إشعار المتصفح:', error);
        }
    }

    // إشعارات سريعة
    success(title, message, options = {}) {
        return this.show(title, message, 'success', options);
    }

    error(title, message, options = {}) {
        return this.show(title, message, 'error', { ...options, duration: 8000 });
    }

    warning(title, message, options = {}) {
        return this.show(title, message, 'warning', options);
    }

    info(title, message, options = {}) {
        return this.show(title, message, 'info', options);
    }

    // إشعار مع إجراءات
    confirm(title, message, onConfirm, onCancel) {
        return this.show(title, message, 'warning', {
            persistent: true,
            actions: [
                {
                    id: 'confirm',
                    text: 'تأكيد',
                    style: 'primary',
                    callback: onConfirm
                },
                {
                    id: 'cancel',
                    text: 'إلغاء',
                    style: 'secondary',
                    callback: onCancel
                }
            ]
        });
    }

    // مسح جميع الإشعارات
    clearAll() {
        this.notifications.forEach(notification => {
            this.removeNotification(notification);
        });
        this.notifications = [];
    }

    // تحديث إشعار موجود
    update(id, newTitle, newMessage) {
        const notification = this.notifications.find(n => n.id === id);
        if (!notification) return;

        const titleElement = notification.element.querySelector('.notification-title');
        const messageElement = notification.element.querySelector('.notification-message');

        if (titleElement) titleElement.textContent = newTitle;
        if (messageElement) messageElement.textContent = newMessage;
    }
}

// إنشاء مثيل عام
const notificationSystem = new NotificationSystem();

// دوال مساعدة عامة
window.showNotification = (title, message, type, options) => {
    return notificationSystem.show(title, message, type, options);
};

window.showSuccess = (title, message, options) => {
    return notificationSystem.success(title, message, options);
};

window.showError = (title, message, options) => {
    return notificationSystem.error(title, message, options);
};

window.showWarning = (title, message, options) => {
    return notificationSystem.warning(title, message, options);
};

window.showInfo = (title, message, options) => {
    return notificationSystem.info(title, message, options);
};

window.showConfirm = (title, message, onConfirm, onCancel) => {
    return notificationSystem.confirm(title, message, onConfirm, onCancel);
};
