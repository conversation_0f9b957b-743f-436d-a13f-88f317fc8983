@model EmpMgr.Models.Institute

@{
    ViewData["Title"] = "إضافة معهد جديد";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-plus-circle me-2"></i>
                    إضافة معهد جديد
                </h2>
                <a asp-action="Institutes" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمعاهد
                </a>
            </div>

            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>
                        بيانات المعهد
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="CreateInstitute" method="post" id="instituteForm" novalidate>
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label asp-for="Name" class="form-label required"></label>
                                <input asp-for="Name" class="form-control" placeholder="أدخل اسم المعهد" required />
                                <span asp-validation-for="Name" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    أدخل الاسم الكامل للمعهد (مثال: المعهد التقني، معهد الإدارة)
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label asp-for="Type" class="form-label required"></label>
                                <select asp-for="Type" class="form-select" required>
                                    <option value="">اختر نوع المعهد</option>
                                    <option value="@((int)EmpMgr.Models.InstituteType.Technical)">تقني</option>
                                    <option value="@((int)EmpMgr.Models.InstituteType.Vocational)">مهني</option>
                                    <option value="@((int)EmpMgr.Models.InstituteType.Medical)">طبي</option>
                                    <option value="@((int)EmpMgr.Models.InstituteType.Administrative)">إداري</option>
                                </select>
                                <span asp-validation-for="Type" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    حدد نوع المعهد
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label asp-for="Location" class="form-label"></label>
                                <input asp-for="Location" class="form-control" placeholder="موقع المعهد (اختياري)" />
                                <span asp-validation-for="Location" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    أدخل موقع المعهد أو المحافظة (اختياري)
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="IsActive" class="form-check-label">
                                        المعهد نشط
                                    </label>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    المعاهد النشطة فقط تظهر في قوائم الاختيار
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ المعهد
                                </button>
                                <button type="reset" class="btn btn-outline-secondary btn-lg ms-2">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                            <a asp-action="Institutes" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-list me-2"></i>
                                عرض جميع المعاهد
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card mt-4 border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح مهمة
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تأكد من كتابة اسم المعهد بشكل صحيح ومطابق للتسمية الرسمية
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            حدد نوع المعهد بدقة (تقني، مهني، طبي، أو إداري)
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            يمكنك إضافة موقع المعهد لتسهيل التمييز بين المعاهد
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            المعاهد غير النشطة لن تظهر في قوائم إضافة الموظفين الجدد
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            يمكنك تعديل بيانات المعهد لاحقاً من قائمة المعاهد
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من صحة النموذج
            const form = document.getElementById('instituteForm');
            
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });

            // تنظيف المدخلات
            const nameInput = document.getElementById('Name');
            const locationInput = document.getElementById('Location');

            // تنظيف حقول النص يتم بواسطة form-fixes.js العام

            // إصلاح زر المسافة يتم بواسطة form-fixes.js العام

            // التركيز على حقل الاسم
            nameInput.focus();
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: #dc3545;
        }

        .form-control:focus, .form-select:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .card {
            border: none;
            border-radius: 15px;
        }

        .card-header {
            border-radius: 15px 15px 0 0 !important;
        }

        .btn {
            border-radius: 10px;
        }

        .form-check-input:checked {
            background-color: #28a745;
            border-color: #28a745;
        }
    </style>
}
