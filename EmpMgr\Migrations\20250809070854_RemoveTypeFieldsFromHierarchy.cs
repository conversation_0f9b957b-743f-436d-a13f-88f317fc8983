﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmpMgr.Migrations
{
    /// <inheritdoc />
    public partial class RemoveTypeFieldsFromHierarchy : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Type",
                table: "GovernmentDepartments");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "Divisions");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "Directorates");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "Agencies");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "GovernmentDepartments",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "Divisions",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "Directorates",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "Agencies",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }
    }
}
