using EmpMgr.Models;

namespace EmpMgr.Services
{
    public interface IReportService
    {
        Task<byte[]> GenerateEmployeeReportAsync(List<Employee> employees, string reportType = "pdf");
        Task<byte[]> GenerateStatisticsReportAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<byte[]> GenerateRankDistributionReportAsync();
        Task<byte[]> GenerateProvinceDistributionReportAsync();
        Task<byte[]> GenerateEducationReportAsync();
        Task<byte[]> GenerateGenderDistributionReportAsync();
        Task<ReportStatistics> GetReportStatisticsAsync();
    }

    public class ReportStatistics
    {
        public int TotalEmployees { get; set; }
        public int MaleEmployees { get; set; }
        public int FemaleEmployees { get; set; }
        public Dictionary<string, int> RankDistribution { get; set; } = new();
        public Dictionary<string, int> ProvinceDistribution { get; set; } = new();
        public Dictionary<string, int> EducationDistribution { get; set; } = new();
        public Dictionary<string, int> MonthlyRegistrations { get; set; } = new();
    }
}
