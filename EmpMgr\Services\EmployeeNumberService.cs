using EmpMgr.Data;
using Microsoft.EntityFrameworkCore;

namespace EmpMgr.Services
{
    public interface IEmployeeNumberService
    {
        Task<string> GenerateEmployeeNumberAsync();
        Task<bool> IsStatisticalNumberUniqueAsync(string statisticalNumber, int? excludeEmployeeId = null);
        Task<bool> IsEmployeeNameUniqueAsync(string firstName, string fatherName, string grandFatherName, 
            string greatGrandFatherName, string lastName, int? excludeEmployeeId = null);
    }

    public class EmployeeNumberService : IEmployeeNumberService
    {
        private readonly ApplicationDbContext _context;

        public EmployeeNumberService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<string> GenerateEmployeeNumberAsync()
        {
            var currentYear = DateTime.Now.Year;
            var prefix = "MOI";
            
            // البحث عن آخر رقم موظف في السنة الحالية
            var lastEmployeeNumber = await _context.Employees
                .Where(e => e.EmployeeNumber.StartsWith($"{prefix}-{currentYear}-"))
                .OrderByDescending(e => e.EmployeeNumber)
                .Select(e => e.EmployeeNumber)
                .FirstOrDefaultAsync();

            int nextSequence = 1;

            if (!string.IsNullOrEmpty(lastEmployeeNumber))
            {
                // استخراج الرقم التسلسلي من آخر رقم موظف
                var parts = lastEmployeeNumber.Split('-');
                if (parts.Length == 3 && int.TryParse(parts[2], out int lastSequence))
                {
                    nextSequence = lastSequence + 1;
                }
            }

            // تنسيق الرقم التسلسلي بـ 6 أرقام
            var sequenceFormatted = nextSequence.ToString("D6");
            
            return $"{prefix}-{currentYear}-{sequenceFormatted}";
        }

        public async Task<bool> IsStatisticalNumberUniqueAsync(string statisticalNumber, int? excludeEmployeeId = null)
        {
            var query = _context.Employees.Where(e => e.StatisticalNumber == statisticalNumber);
            
            if (excludeEmployeeId.HasValue)
            {
                query = query.Where(e => e.Id != excludeEmployeeId.Value);
            }

            return !await query.AnyAsync();
        }

        public async Task<bool> IsEmployeeNameUniqueAsync(string firstName, string fatherName, string grandFatherName, 
            string greatGrandFatherName, string lastName, int? excludeEmployeeId = null)
        {
            var query = _context.Employees.Where(e => 
                e.FirstName == firstName && 
                e.FatherName == fatherName && 
                e.GrandFatherName == grandFatherName && 
                e.GreatGrandFatherName == greatGrandFatherName && 
                e.LastName == lastName);
            
            if (excludeEmployeeId.HasValue)
            {
                query = query.Where(e => e.Id != excludeEmployeeId.Value);
            }

            return !await query.AnyAsync();
        }
    }
}
