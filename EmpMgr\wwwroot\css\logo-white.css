/* ملف CSS خاص لجعل الشعار أبيض اللون */

/* الشعار الأبيض - الطريقة الأساسية */
.navbar-brand img,
.logo,
img[alt*="شعار"],
img[src*="logo"],
img[src*="moi-logo"] {
    filter: brightness(0) invert(1) !important;
    -webkit-filter: brightness(0) invert(1) !important;
    -moz-filter: brightness(0) invert(1) !important;
    -o-filter: brightness(0) invert(1) !important;
    -ms-filter: brightness(0) invert(1) !important;
}

/* تحسينات إضافية للشعار */
.navbar-brand img {
    transition: all 0.3s ease;
    border-radius: 6px;
    padding: 2px;
    background: rgba(255, 255, 255, 0.05);
}

.navbar-brand img:hover {
    filter: brightness(0) invert(1) drop-shadow(0 0 5px rgba(255, 255, 255, 0.5)) !important;
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.02);
}

/* شعار صفحة تسجيل الدخول */
.logo {
    transition: all 0.3s ease;
    border-radius: 50%;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.logo:hover {
    filter: brightness(0) invert(1) drop-shadow(0 0 10px rgba(255, 255, 255, 0.4)) !important;
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.05);
}

/* CSS بديل للمتصفحات التي لا تدعم filter */
@supports not (filter: invert(1)) {
    .navbar-brand img,
    .logo,
    img[alt*="شعار"],
    img[src*="logo"],
    img[src*="moi-logo"] {
        background-color: white !important;
        mix-blend-mode: difference;
        filter: none !important;
    }
}

/* تأكيد أن الشعار أبيض في جميع الحالات */
.bg-primary .navbar-brand img,
.bg-dark .navbar-brand img,
.navbar-dark .navbar-brand img,
.bg-secondary .navbar-brand img {
    filter: brightness(0) invert(1) !important;
}

/* للخلفيات الفاتحة - شعار داكن */
.bg-light .navbar-brand img,
.navbar-light .navbar-brand img,
.bg-white .navbar-brand img {
    filter: none !important;
}

/* تحسينات للشعار في الطباعة */
@media print {
    .navbar-brand img,
    .logo {
        filter: none !important;
        background: none !important;
        border: none !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .navbar-brand img {
        width: 35px;
        height: 35px;
    }
    
    .logo {
        width: 70px;
        height: 70px;
    }
}

/* تأثيرات إضافية للشعار */
.navbar-brand img {
    animation: logoGlow 3s ease-in-out infinite alternate;
}

@keyframes logoGlow {
    0% {
        filter: brightness(0) invert(1);
    }
    100% {
        filter: brightness(0) invert(1) drop-shadow(0 0 3px rgba(255, 255, 255, 0.3));
    }
}

/* إيقاف التأثير عند hover */
.navbar-brand:hover img {
    animation: none;
}

/* تحسين الوضوح */
.navbar-brand img,
.logo {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
}

/* تأكيد الشعار الأبيض بطريقة CSS متقدمة */
.white-logo,
.navbar-brand img.white-logo,
.logo.white-logo {
    filter: 
        brightness(0)
        saturate(100%)
        invert(100%)
        sepia(0%)
        saturate(0%)
        hue-rotate(0deg)
        brightness(100%)
        contrast(100%) !important;
}
