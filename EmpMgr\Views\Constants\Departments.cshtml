@model IEnumerable<EmpMgr.Models.Department>

@{
    ViewData["Title"] = "إدارة الأقسام";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-sitemap me-2"></i>
                    إدارة الأقسام
                </h2>
                <div>
                    <a asp-action="CreateDepartment" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة قسم جديد
                    </a>
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للثوابت
                    </a>
                </div>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>@Model.Count()</h4>
                                    <p class="mb-0">إجمالي الأقسام</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-sitemap fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>@Model.Count(d => d.IsActive)</h4>
                                    <p class="mb-0">الأقسام النشطة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>@Model.Sum(d => d.Employees.Count)</h4>
                                    <p class="mb-0">إجمالي الموظفين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>@Model.GroupBy(d => d.InstituteId).Count()</h4>
                                    <p class="mb-0">المعاهد المرتبطة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-graduation-cap fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الأقسام -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الأقسام
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>اسم القسم</th>
                                        <th>المعهد</th>
                                        <th>الكود</th>
                                        <th>النوع</th>
                                        <th>سنة التأسيس</th>
                                        <th>عدد الموظفين</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var department in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@department.Name</strong>
                                                @if (!string.IsNullOrEmpty(department.Description))
                                                {
                                                    <br><small class="text-muted">@department.Description</small>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-graduation-cap me-1"></i>
                                                    @department.Institute?.Name
                                                </span>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(department.Code))
                                                {
                                                    <span class="badge bg-secondary">@department.Code</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    @Html.DisplayFor(modelItem => department.Type)
                                                </span>
                                            </td>
                                            <td>
                                                @if (department.EstablishedYear.HasValue)
                                                {
                                                    @department.EstablishedYear.Value
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    @department.Employees.Count
                                                </span>
                                            </td>
                                            <td>
                                                @if (department.IsActive)
                                                {
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>
                                                        نشط
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-times me-1"></i>
                                                        غير نشط
                                                    </span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="EditDepartment" asp-route-id="@department.Id" 
                                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="DeleteDepartment" asp-route-id="@department.Id" 
                                                       class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أقسام مسجلة</h5>
                            <p class="text-muted">ابدأ بإضافة أول قسم في النظام</p>
                            <a asp-action="CreateDepartment" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة قسم جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // تأكيد الحذف
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('a[href*="DeleteDepartment"]');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (confirm('هل أنت متأكد من حذف هذا القسم؟')) {
                        window.location.href = this.href;
                    }
                });
            });
        });
    </script>
}
