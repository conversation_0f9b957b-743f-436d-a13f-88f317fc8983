@model EmpMgr.Models.Rank
@{
    ViewData["Title"] = "تعديل الرتبة";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الرتبة: @Model.Name
                    </h4>
                </div>
                
                <div class="card-body">
                    <form asp-action="EditRank" method="post">
                        <input asp-for="Id" type="hidden" />
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Name" class="form-label">
                                    <i class="fas fa-medal me-2 text-primary"></i>
                                    @Html.DisplayNameFor(m => m.Name)
                                </label>
                                <input asp-for="Name" class="form-control" placeholder="أدخل اسم الرتبة" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="Order" class="form-label">
                                    <i class="fas fa-sort-numeric-down me-2 text-primary"></i>
                                    @Html.DisplayNameFor(m => m.Order)
                                </label>
                                <input asp-for="Order" class="form-control" type="number" min="1" placeholder="أدخل ترتيب الرتبة" />
                                <span asp-validation-for="Order" class="text-danger"></span>
                                <div class="form-text">الترتيب يحدد أولوية الرتبة (1 = أعلى رتبة)</div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Description" class="form-label">
                                <i class="fas fa-align-left me-2 text-primary"></i>
                                @Html.DisplayNameFor(m => m.Description)
                            </label>
                            <textarea asp-for="Description" class="form-control" rows="3"
                                      placeholder="أدخل وصف الرتبة (اختياري)"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <!-- قسم إعدادات الترقية -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-line me-2 text-success"></i>
                                    إعدادات الترقية
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label asp-for="RankDurationYears" class="form-label">
                                            <i class="fas fa-clock me-2 text-warning"></i>
                                            @Html.DisplayNameFor(m => m.RankDurationYears)
                                        </label>
                                        <div class="input-group">
                                            <input asp-for="RankDurationYears" class="form-control" type="number"
                                                   min="0" max="50" placeholder="3" />
                                            <span class="input-group-text">سنة</span>
                                        </div>
                                        <span asp-validation-for="RankDurationYears" class="text-danger"></span>
                                        <div class="form-text">المدة المطلوبة في هذه الرتبة للترقية للرتبة التالية</div>
                                    </div>


                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label asp-for="MinAgeForPromotion" class="form-label">
                                            <i class="fas fa-calendar-plus me-2 text-primary"></i>
                                            @Html.DisplayNameFor(m => m.MinAgeForPromotion)
                                        </label>
                                        <div class="input-group">
                                            <input asp-for="MinAgeForPromotion" class="form-control" type="number"
                                                   min="18" max="70" placeholder="25" />
                                            <span class="input-group-text">سنة</span>
                                        </div>
                                        <span asp-validation-for="MinAgeForPromotion" class="text-danger"></span>
                                        <div class="form-text">الحد الأدنى للعمر للترقية (اختياري)</div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label asp-for="MaxAgeForPromotion" class="form-label">
                                            <i class="fas fa-calendar-minus me-2 text-danger"></i>
                                            @Html.DisplayNameFor(m => m.MaxAgeForPromotion)
                                        </label>
                                        <div class="input-group">
                                            <input asp-for="MaxAgeForPromotion" class="form-control" type="number"
                                                   min="18" max="70" placeholder="60" />
                                            <span class="input-group-text">سنة</span>
                                        </div>
                                        <span asp-validation-for="MaxAgeForPromotion" class="text-danger"></span>
                                        <div class="form-text">الحد الأقصى للعمر للترقية (اختياري)</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- قسم متطلبات إضافية -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-tasks me-2 text-info"></i>
                                    متطلبات إضافية
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check mb-2">
                                            <input asp-for="RequiresTrainingCourses" class="form-check-input" />
                                            <label asp-for="RequiresTrainingCourses" class="form-check-label">
                                                <i class="fas fa-graduation-cap me-2 text-success"></i>
                                                @Html.DisplayNameFor(m => m.RequiresTrainingCourses)
                                            </label>
                                        </div>
                                        <div class="ms-4">
                                            <label asp-for="RequiredCoursesCount" class="form-label">
                                                @Html.DisplayNameFor(m => m.RequiredCoursesCount)
                                            </label>
                                            <input asp-for="RequiredCoursesCount" class="form-control" type="number"
                                                   min="0" max="20" placeholder="0" />
                                            <span asp-validation-for="RequiredCoursesCount" class="text-danger"></span>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="form-check mb-2">
                                            <input asp-for="RequiresPerformanceEvaluation" class="form-check-input" />
                                            <label asp-for="RequiresPerformanceEvaluation" class="form-check-label">
                                                <i class="fas fa-star me-2 text-warning"></i>
                                                @Html.DisplayNameFor(m => m.RequiresPerformanceEvaluation)
                                            </label>
                                        </div>
                                        <div class="ms-4">
                                            <label asp-for="MinPerformanceScore" class="form-label">
                                                @Html.DisplayNameFor(m => m.MinPerformanceScore)
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="MinPerformanceScore" class="form-control" type="number"
                                                       min="0" max="100" step="0.1" placeholder="75" />
                                                <span class="input-group-text">%</span>
                                            </div>
                                            <span asp-validation-for="MinPerformanceScore" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="PromotionNotes" class="form-label">
                                        <i class="fas fa-sticky-note me-2 text-secondary"></i>
                                        @Html.DisplayNameFor(m => m.PromotionNotes)
                                    </label>
                                    <textarea asp-for="PromotionNotes" class="form-control" rows="2"
                                              placeholder="ملاحظات إضافية حول متطلبات الترقية (اختياري)"></textarea>
                                    <span asp-validation-for="PromotionNotes" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" />
                                <label asp-for="IsActive" class="form-check-label">
                                    <i class="fas fa-toggle-on me-2 text-success"></i>
                                    @Html.DisplayNameFor(m => m.IsActive)
                                </label>
                            </div>
                            <div class="form-text">الرتب النشطة فقط تظهر في قوائم الاختيار</div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a asp-action="Ranks" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>
                                تحديث الرتبة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- نصائح مفيدة -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح مفيدة
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>استخدم أسماء واضحة ومفهومة للرتب</li>
                        <li>رقم الترتيب يحدد التسلسل الهرمي للرتب (1 = أعلى رتبة)</li>
                        <li>يمكنك إلغاء تفعيل الرتبة بدلاً من حذفها إذا كانت مرتبطة بموظفين</li>
                        <li>الوصف يساعد في توضيح مهام ومسؤوليات الرتبة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // التحقق من صحة النموذج
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
            
            // منع تداخل الأحداث مع زر المسافة في حقول النص
            const textInputs = form.querySelectorAll('input[type="text"], textarea');
            textInputs.forEach(function(input) {
                input.addEventListener('keydown', function(e) {
                    // السماح بزر المسافة في حقول النص
                    if (e.code === 'Space') {
                        e.stopPropagation();
                    }
                });
            });

            // التركيز على حقل الاسم
            document.getElementById('Name').focus();

            // تحسين تفاعل متطلبات الترقية
            const trainingCheckbox = document.getElementById('RequiresTrainingCourses');
            const coursesCountInput = document.getElementById('RequiredCoursesCount');
            const performanceCheckbox = document.getElementById('RequiresPerformanceEvaluation');
            const performanceScoreInput = document.getElementById('MinPerformanceScore');

            // تفعيل/إلغاء تفعيل حقل عدد الدورات
            function toggleCoursesCount() {
                coursesCountInput.disabled = !trainingCheckbox.checked;
                if (!trainingCheckbox.checked) {
                    coursesCountInput.value = '0';
                }
            }

            // تفعيل/إلغاء تفعيل حقل تقييم الأداء
            function togglePerformanceScore() {
                performanceScoreInput.disabled = !performanceCheckbox.checked;
                if (!performanceCheckbox.checked) {
                    performanceScoreInput.value = '';
                }
            }

            trainingCheckbox.addEventListener('change', toggleCoursesCount);
            performanceCheckbox.addEventListener('change', togglePerformanceScore);

            // تطبيق الحالة الأولية
            toggleCoursesCount();
            togglePerformanceScore();
        });
    </script>
}
