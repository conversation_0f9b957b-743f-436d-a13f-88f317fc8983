@model IEnumerable<EmpMgr.Models.University>

@{
    ViewData["Title"] = "إدارة الجامعات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-university me-2"></i>
                    إدارة الجامعات
                </h2>
                <div>
                    <a asp-action="CreateUniversity" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة جامعة جديدة
                    </a>
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للثوابت
                    </a>
                </div>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الجامعات
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الرقم</th>
                                        <th>اسم الجامعة</th>
                                        <th>الموقع</th>
                                        <th>النوع</th>
                                        <th>الحالة</th>
                                        <th>عدد الموظفين</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @{int counter = 1;}
                                    @foreach (var university in Model)
                                    {
                                        <tr>
                                            <td>@counter</td>
                                            <td>
                                                <strong>@university.Name</strong>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(university.Location))
                                                {
                                                    <span class="text-muted">@university.Location</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                            <td>
                                                @switch (university.Type)
                                                {
                                                    case EmpMgr.Models.UniversityType.Government:
                                                        <span class="badge bg-success">حكومية</span>
                                                        break;
                                                    case EmpMgr.Models.UniversityType.Private:
                                                        <span class="badge bg-warning text-dark">أهلية</span>
                                                        break;
                                                    case EmpMgr.Models.UniversityType.Technical:
                                                        <span class="badge bg-info">تقنية</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">غير محدد</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                @if (university.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@university.Employees.Count</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="EditUniversity" asp-route-id="@university.Id" 
                                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @if (university.Employees.Count == 0)
                                                    {
                                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                onclick="confirmDelete(@university.Id, '@university.Name')" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    }
                                                    else
                                                    {
                                                        <button type="button" class="btn btn-sm btn-outline-secondary" 
                                                                disabled title="لا يمكن الحذف - يوجد موظفين مرتبطين">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                        counter++;
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-university fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد جامعات مضافة</h5>
                            <p class="text-muted">ابدأ بإضافة الجامعات لتتمكن من إدارة بيانات الموظفين التعليمية</p>
                            <a asp-action="CreateUniversity" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>
                                إضافة أول جامعة
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للتأكيد من الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الجامعة <strong id="universityName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    هذا الإجراء لا يمكن التراجع عنه
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" class="d-inline">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(universityId, universityName) {
            document.getElementById('universityName').textContent = universityName;
            document.getElementById('deleteForm').action = '@Url.Action("DeleteUniversity")/' + universityId;
            
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        // إخفاء الرسائل تلقائياً بعد 5 ثوان
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
}
