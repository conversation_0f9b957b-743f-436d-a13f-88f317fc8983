@model EmpMgr.Models.Employee
@{
    ViewData["Title"] = "حذف الموظف";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد حذف الموظف
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-warning me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>

                    <h5 class="text-danger mb-4">هل أنت متأكد من حذف بيانات الموظف التالي؟</h5>

                    <!-- معلومات الموظف -->
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mb-3"
                                 style="width: 150px; height: 150px; margin: 0 auto;">
                                <i class="fas fa-user fa-3x text-muted"></i>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted">الاسم الكامل:</td>
                                    <td>@Model.FullName</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">رقم الموظف:</td>
                                    <td>@Model.EmployeeNumber</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">الرتبة:</td>
                                    <td>@Model.Rank?.Name</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">المحافظة:</td>
                                    <td>@Model.Province?.Name</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">الجامعة:</td>
                                    <td>@Model.University?.Name</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">المعهد:</td>
                                    <td>@Model.Institute?.Name</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">تاريخ الإضافة:</td>
                                    <td>@Model.CreatedDate.ToString("dd/MM/yyyy")</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            العودة للقائمة
                        </a>
                        <div>
                            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info me-2">
                                <i class="fas fa-eye me-2"></i>
                                عرض التفاصيل
                            </a>
                            <form asp-action="DeleteConfirmed" method="post" style="display: inline;">
                                <input type="hidden" asp-for="Id" />
                                @Html.AntiForgeryToken()
                                <button type="submit" class="btn btn-danger"
                                        onclick="return confirm('هل أنت متأكد من حذف الموظف @Model.FullName؟\n\nهذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بيانات الموظف نهائياً!')">
                                    <i class="fas fa-trash me-2"></i>
                                    تأكيد الحذف
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .card-header.bg-danger {
            border-bottom: 3px solid #dc3545;
        }
        
        .table td {
            padding: 0.5rem 0.75rem;
            border: none;
        }
        
        .table td:first-child {
            width: 30%;
        }
        
        .alert-danger {
            border-left: 4px solid #dc3545;
        }
    </style>
}
