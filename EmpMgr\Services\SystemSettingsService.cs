using EmpMgr.Data;
using EmpMgr.Models;
using Microsoft.EntityFrameworkCore;

namespace EmpMgr.Services
{
    public class SystemSettingsService : ISystemSettingsService
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<SystemSettingsService> _logger;

        public SystemSettingsService(
            ApplicationDbContext context,
            IWebHostEnvironment environment,
            ILogger<SystemSettingsService> logger)
        {
            _context = context;
            _environment = environment;
            _logger = logger;
        }

        public async Task<SystemSettings> GetSettingsAsync()
        {
            var settings = await _context.SystemSettings.FirstOrDefaultAsync();
            
            if (settings == null)
            {
                await InitializeDefaultSettingsAsync();
                settings = await _context.SystemSettings.FirstOrDefaultAsync();
            }

            return settings!;
        }

        public async Task<SystemSettings> UpdateSettingsAsync(SystemSettings settings, string updatedBy)
        {
            try
            {
                var existingSettings = await _context.SystemSettings.FirstOrDefaultAsync();
                
                if (existingSettings == null)
                {
                    settings.LastUpdated = DateTime.Now;
                    settings.LastUpdatedBy = updatedBy;
                    _context.SystemSettings.Add(settings);
                }
                else
                {
                    existingSettings.SystemName = settings.SystemName;
                    existingSettings.OrganizationName = settings.OrganizationName;
                    existingSettings.LogoPath = settings.LogoPath;
                    existingSettings.FaviconPath = settings.FaviconPath;
                    existingSettings.PrimaryColor = settings.PrimaryColor;
                    existingSettings.SecondaryColor = settings.SecondaryColor;
                    existingSettings.SystemDescription = settings.SystemDescription;
                    existingSettings.SystemVersion = settings.SystemVersion;
                    existingSettings.SupportEmail = settings.SupportEmail;
                    existingSettings.SupportPhone = settings.SupportPhone;
                    existingSettings.OrganizationAddress = settings.OrganizationAddress;
                    existingSettings.OrganizationWebsite = settings.OrganizationWebsite;
                    existingSettings.AllowRegistration = settings.AllowRegistration;
                    existingSettings.AllowRememberMe = settings.AllowRememberMe;
                    existingSettings.SessionTimeoutMinutes = settings.SessionTimeoutMinutes;
                    existingSettings.MaxLoginAttempts = settings.MaxLoginAttempts;
                    existingSettings.LockoutDurationMinutes = settings.LockoutDurationMinutes;
                    existingSettings.DarkModeEnabled = settings.DarkModeEnabled;
                    existingSettings.DefaultLanguage = settings.DefaultLanguage;
                    existingSettings.TimeZone = settings.TimeZone;
                    existingSettings.AutoBackupEnabled = settings.AutoBackupEnabled;
                    existingSettings.BackupIntervalHours = settings.BackupIntervalHours;
                    existingSettings.LastUpdated = DateTime.Now;
                    existingSettings.LastUpdatedBy = updatedBy;
                    
                    settings = existingSettings;
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation("تم تحديث إعدادات النظام بواسطة {User}", updatedBy);
                
                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعدادات النظام");
                throw;
            }
        }

        public async Task<string> UploadLogoAsync(IFormFile logoFile)
        {
            if (logoFile == null || logoFile.Length == 0)
                throw new ArgumentException("لم يتم اختيار ملف الشعار");

            var allowedExtensions = new[] { ".png", ".jpg", ".jpeg", ".gif", ".svg" };
            var fileExtension = Path.GetExtension(logoFile.FileName).ToLowerInvariant();
            
            if (!allowedExtensions.Contains(fileExtension))
                throw new ArgumentException("نوع الملف غير مدعوم. الأنواع المدعومة: PNG, JPG, JPEG, GIF, SVG");

            if (logoFile.Length > 5 * 1024 * 1024) // 5MB
                throw new ArgumentException("حجم الملف يجب أن يكون أقل من 5 ميجابايت");

            var uploadsFolder = Path.Combine(_environment.WebRootPath, "images", "system");
            Directory.CreateDirectory(uploadsFolder);

            var fileName = $"logo_{DateTime.Now:yyyyMMddHHmmss}{fileExtension}";
            var filePath = Path.Combine(uploadsFolder, fileName);

            using (var fileStream = new FileStream(filePath, FileMode.Create))
            {
                await logoFile.CopyToAsync(fileStream);
            }

            return $"/images/system/{fileName}";
        }

        public async Task<string> UploadFaviconAsync(IFormFile faviconFile)
        {
            if (faviconFile == null || faviconFile.Length == 0)
                throw new ArgumentException("لم يتم اختيار ملف الأيقونة");

            var allowedExtensions = new[] { ".ico", ".png", ".jpg", ".jpeg" };
            var fileExtension = Path.GetExtension(faviconFile.FileName).ToLowerInvariant();
            
            if (!allowedExtensions.Contains(fileExtension))
                throw new ArgumentException("نوع الملف غير مدعوم. الأنواع المدعومة: ICO, PNG, JPG, JPEG");

            if (faviconFile.Length > 1 * 1024 * 1024) // 1MB
                throw new ArgumentException("حجم الملف يجب أن يكون أقل من 1 ميجابايت");

            var uploadsFolder = Path.Combine(_environment.WebRootPath, "images", "system");
            Directory.CreateDirectory(uploadsFolder);

            var fileName = $"favicon_{DateTime.Now:yyyyMMddHHmmss}{fileExtension}";
            var filePath = Path.Combine(uploadsFolder, fileName);

            using (var fileStream = new FileStream(filePath, FileMode.Create))
            {
                await faviconFile.CopyToAsync(fileStream);
            }

            return $"/images/system/{fileName}";
        }

        public Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                    return Task.FromResult(false);

                var fullPath = Path.Combine(_environment.WebRootPath, filePath.TrimStart('/'));
                
                if (File.Exists(fullPath))
                {
                    File.Delete(fullPath);
                    _logger.LogInformation("تم حذف الملف: {FilePath}", filePath);
                    return Task.FromResult(true);
                }
                
                return Task.FromResult(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الملف: {FilePath}", filePath);
                return Task.FromResult(false);
            }
        }

        public async Task InitializeDefaultSettingsAsync()
        {
            try
            {
                var existingSettings = await _context.SystemSettings.AnyAsync();
                
                if (!existingSettings)
                {
                    var defaultSettings = new SystemSettings
                    {
                        SystemName = "نظام إدارة الضباط والمنتسبين",
                        OrganizationName = "وزارة الداخلية - جمهورية العراق",
                        LogoPath = "/images/moi-logo.png",
                        FaviconPath = "/images/favicon.ico",
                        PrimaryColor = "#667eea",
                        SecondaryColor = "#764ba2",
                        SystemDescription = "نظام شامل لإدارة بيانات الضباط والمنتسبين",
                        SystemVersion = "1.0.0",
                        AllowRegistration = false,
                        AllowRememberMe = true,
                        SessionTimeoutMinutes = 60,
                        MaxLoginAttempts = 5,
                        LockoutDurationMinutes = 15,
                        DarkModeEnabled = false,
                        DefaultLanguage = "ar",
                        TimeZone = "Asia/Baghdad",
                        AutoBackupEnabled = true,
                        BackupIntervalHours = 24,
                        LastUpdated = DateTime.Now,
                        LastUpdatedBy = "System"
                    };

                    _context.SystemSettings.Add(defaultSettings);
                    await _context.SaveChangesAsync();
                    
                    _logger.LogInformation("تم إنشاء الإعدادات الافتراضية للنظام");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الإعدادات الافتراضية");
                throw;
            }
        }
    }
}
