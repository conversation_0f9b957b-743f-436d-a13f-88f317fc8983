@model EmpMgr.Models.College

@{
    ViewData["Title"] = "تعديل الكلية";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الكلية
                </h2>
                <a asp-action="Colleges" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للكليات
                </a>
            </div>

            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        تعديل بيانات الكلية: @Model.Name
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="EditCollege" method="post" id="collegeForm" novalidate>
                        <input asp-for="Id" type="hidden" />
                        <input asp-for="CreatedDate" type="hidden" />
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="UniversityId" class="form-label required">الجامعة</label>
                                <select asp-for="UniversityId" class="form-select" required>
                                    <option value="">اختر الجامعة</option>
                                    @foreach (var university in (ViewBag.Universities as List<EmpMgr.Models.University>) ?? new List<EmpMgr.Models.University>())
                                    {
                                        <option value="@university.Id">@university.Name</option>
                                    }
                                </select>
                                <span asp-validation-for="UniversityId" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    اختر الجامعة التي تتبع لها الكلية
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="Name" class="form-label required"></label>
                                <input asp-for="Name" class="form-control" placeholder="أدخل اسم الكلية" required />
                                <span asp-validation-for="Name" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    أدخل الاسم الكامل للكلية
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="Code" class="form-label"></label>
                                <input asp-for="Code" class="form-control" placeholder="كود الكلية (اختياري)" maxlength="10" />
                                <span asp-validation-for="Code" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    كود مختصر للكلية (اختياري)
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label asp-for="Type" class="form-label required"></label>
                                <select asp-for="Type" class="form-select" required>
                                    <option value="">اختر نوع الكلية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Scientific)">علمية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Humanities)">إنسانية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Medical)">طبية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Engineering)">هندسية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Technical)">تقنية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Administrative)">إدارية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Legal)">قانونية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Educational)">تربوية</option>
                                    <option value="@((int)EmpMgr.Models.CollegeType.Arts)">فنية</option>
                                </select>
                                <span asp-validation-for="Type" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    حدد نوع الكلية
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label asp-for="EstablishedYear" class="form-label"></label>
                                <input asp-for="EstablishedYear" type="number" class="form-control" 
                                       placeholder="سنة التأسيس" min="1900" max="@DateTime.Now.Year" />
                                <span asp-validation-for="EstablishedYear" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    سنة تأسيس الكلية (اختياري)
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label asp-for="Description" class="form-label"></label>
                                <textarea asp-for="Description" class="form-control" rows="3" 
                                          placeholder="وصف مختصر عن الكلية (اختياري)"></textarea>
                                <span asp-validation-for="Description" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    وصف مختصر عن الكلية وتخصصاتها
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsActive" class="form-check-label">
                                        الكلية نشطة
                                    </label>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    الكليات النشطة فقط تظهر في قوائم الاختيار
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية عن الكلية -->
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>
                                        معلومات الكلية
                                    </h6>
                                    <p class="mb-1">
                                        <strong>الجامعة الحالية:</strong> 
                                        <span class="badge bg-info">@Model.University?.Name</span>
                                    </p>
                                    <p class="mb-1">
                                        <strong>عدد الموظفين المرتبطين:</strong> 
                                        <span class="badge bg-primary">@Model.Employees.Count</span>
                                    </p>
                                    <p class="mb-1">
                                        <strong>تاريخ الإنشاء:</strong> 
                                        <span class="text-muted">@Model.CreatedDate.ToString("dd/MM/yyyy")</span>
                                    </p>
                                    @if (Model.Employees.Count > 0)
                                    {
                                        <p class="mb-0 text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            تنبيه: يوجد موظفين مرتبطين بهذه الكلية. تأكد من صحة التعديلات.
                                        </p>
                                    }
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-warning btn-lg text-dark">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التعديلات
                                </button>
                                <a asp-action="Colleges" class="btn btn-outline-secondary btn-lg ms-2">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                            <div>
                                @if (Model.Employees.Count == 0)
                                {
                                    <button type="button" class="btn btn-outline-danger btn-lg" 
                                            onclick="confirmDelete(@Model.Id, '@Model.Name')">
                                        <i class="fas fa-trash me-2"></i>
                                        حذف الكلية
                                    </button>
                                }
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- نصائح -->
            <div class="card mt-4 border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح التعديل
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تأكد من صحة اسم الكلية ونوعها قبل الحفظ
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تغيير الجامعة سيؤثر على التصنيف الهرمي للكلية
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            إلغاء تفعيل الكلية سيخفيها من قوائم الاختيار للموظفين الجدد
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            لا يمكن حذف الكلية إذا كان هناك موظفين مرتبطين بها
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للتأكيد من الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الكلية <strong id="collegeName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    هذا الإجراء لا يمكن التراجع عنه
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" class="d-inline">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من صحة النموذج
            const form = document.getElementById('collegeForm');
            
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });

            // تنظيف المدخلات
            const nameInput = document.getElementById('Name');
            const codeInput = document.getElementById('Code');
            const descriptionInput = document.getElementById('Description');

            // تنظيف حقل الاسم يتم بواسطة form-fixes.js العام

            if (codeInput) {
                codeInput.addEventListener('input', function() {
                    this.value = this.value.toUpperCase().replace(/\s/g, '');
                });
            }

            // تنظيف حقل الوصف يتم بواسطة form-fixes.js العام

            // إصلاح زر المسافة يتم بواسطة form-fixes.js العام
        });

        function confirmDelete(collegeId, collegeName) {
            document.getElementById('collegeName').textContent = collegeName;
            document.getElementById('deleteForm').action = '@Url.Action("DeleteCollege")/' + collegeId;
            
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: #dc3545;
        }

        .form-control:focus, .form-select:focus {
            border-color: #ffc107;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }

        .card {
            border: none;
            border-radius: 15px;
        }

        .card-header {
            border-radius: 15px 15px 0 0 !important;
        }

        .btn {
            border-radius: 10px;
        }

        .form-check-input:checked {
            background-color: #ffc107;
            border-color: #ffc107;
        }
    </style>
}
