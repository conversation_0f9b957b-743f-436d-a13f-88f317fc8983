@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

/* تطبيق خط Cairo على جميع العناصر */
*, *::before, *::after {
  font-family: 'Cairo', sans-serif !important;
}

body, html {
  font-family: 'Cairo', sans-serif !important;
}

/* تطبيق خط Cairo على جميع عناصر الإدخال */
input, textarea, select, button, label, span, div, p, h1, h2, h3, h4, h5, h6, a {
  font-family: 'Cairo', sans-serif !important;
}

/* تطبيق خط Cairo على عناصر النموذج */
.form-control, .form-select, .form-check-label, .btn {
  font-family: 'Cairo', sans-serif !important;
}

/* تطبيق خط Cairo على النصوص المدخلة */
input[type="text"], input[type="email"], input[type="password"], input[type="number"], textarea {
  font-family: 'Cairo', sans-serif !important;
}

/* تطبيق خط Cairo على القوائم المنسدلة */
option, .dropdown-item {
  font-family: 'Cairo', sans-serif !important;
}

html {
  font-size: 14px;
  direction: rtl;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9998;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-text {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Enhanced Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-bottom: none;
}

/* Enhanced Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    transform: translateY(-1px);
}

/* Enhanced Tables */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: background-color 0.15s ease-in-out;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Enhanced Forms */
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Enhanced Pagination */
.pagination .page-link {
    border-radius: 0.375rem;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    transition: all 0.15s ease-in-out;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
}

/* Enhanced Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
    border-left: 4px solid;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-left-color: #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-left-color: #dc3545;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left-color: #ffc107;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-left-color: #17a2b8;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .table-responsive {
        border-radius: 0.5rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .btn:last-child {
        margin-bottom: 0;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
  direction: rtl;
  text-align: right;
}

/* تحسينات للتصميم العربي */
.navbar-brand {
  font-weight: 700;
}

/* تصميم الشعار الأبيض */
.navbar-brand img,
.logo {
  filter: brightness(0) invert(1) contrast(100%);
  /* يحول الشعار إلى اللون الأبيض */
  transition: filter 0.3s ease;
  /* طريقة بديلة للشعار الأبيض */
  -webkit-filter: brightness(0) invert(1) contrast(100%);
  -moz-filter: brightness(0) invert(1) contrast(100%);
  -o-filter: brightness(0) invert(1) contrast(100%);
  -ms-filter: brightness(0) invert(1) contrast(100%);
}

/* تأثير hover للشعار */
.navbar-brand:hover img {
  filter: brightness(0) invert(1) drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
}

/* شعار أبيض للخلفيات الداكنة */
.bg-primary .navbar-brand img,
.bg-dark .navbar-brand img,
.navbar-dark .navbar-brand img {
  filter: brightness(0) invert(1);
}

/* شعار داكن للخلفيات الفاتحة */
.bg-light .navbar-brand img,
.navbar-light .navbar-brand img {
  filter: none;
}

/* تحسينات إضافية للشعار في الـ navbar */
.navbar-brand img {
  border-radius: 8px;
  padding: 2px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.navbar-brand img:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* CSS بديل للمتصفحات القديمة */
@supports not (filter: invert(1)) {
  .navbar-brand img,
  .logo {
    background-color: white;
    mix-blend-mode: difference;
  }
}

/* تحسين إضافي للشعار */
.navbar-brand img {
  max-width: 40px;
  max-height: 40px;
  width: auto;
  height: auto;
}

.card {
  border-radius: 15px;
}

.btn {
  border-radius: 10px;
  font-weight: 600;
}

.form-control, .form-select {
  border-radius: 10px;
  text-align: right;
}

.table th {
  font-weight: 600;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* تحسينات للتبويبات */
.nav-tabs .nav-link {
  border-radius: 10px 10px 0 0;
  font-weight: 600;
}

.nav-tabs .nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

/* تحسينات للبطاقات */
.card:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease-in-out;
}

/* تحسينات للأزرار */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Purple theme colors */
.text-purple {
    color: #6f42c1 !important;
}

.bg-purple {
    background-color: #6f42c1 !important;
}

.btn-purple {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
}

.btn-purple:hover {
    background-color: #5a359a;
    border-color: #5a359a;
    color: white;
}

.btn-outline-purple {
    color: #6f42c1;
    border-color: #6f42c1;
}

.btn-outline-purple:hover {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
}

.border-purple {
    border-color: #6f42c1 !important;
}

/* Teal theme colors for departments */
.text-teal {
    color: #20c997 !important;
}

.bg-teal {
    background-color: #20c997 !important;
}

.btn-teal {
    background-color: #20c997;
    border-color: #20c997;
    color: white;
}

.btn-teal:hover {
    background-color: #1aa085;
    border-color: #1aa085;
    color: white;
}

.btn-outline-teal {
    color: #20c997;
    border-color: #20c997;
}

.btn-outline-teal:hover {
    background-color: #20c997;
    border-color: #20c997;
    color: white;
}

.border-teal {
    border-color: #20c997 !important;
}