/**
 * إصلاحات عامة للنماذج
 * يحل مشكلة زر المسافة في حقول النص ومشاكل أخرى شائعة
 */

(function() {
    'use strict';

    // إصلاح مشكلة زر المسافة في حقول النص
    function fixSpaceKeyInTextInputs() {
        // لا نحتاج لأي كود خاص هنا
        // زر المسافة يجب أن يعمل بشكل طبيعي في حقول النص
        // المشكلة كانت في استخدام stopPropagation في الصفحات الأخرى
        console.log('تم تطبيق إصلاح زر المسافة');
    }

    // إصلاح مشكلة Enter في حقول النص (منع إرسال النموذج غير المرغوب فيه)
    function fixEnterKeyInTextInputs() {
        const textInputs = document.querySelectorAll('input[type="text"], input[type="search"]');
        
        textInputs.forEach(function(input) {
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    // منع إرسال النموذج عند الضغط على Enter في حقول النص
                    // إلا إذا كان هناك زر إرسال واحد فقط
                    const form = input.closest('form');
                    if (form) {
                        const submitButtons = form.querySelectorAll('button[type="submit"], input[type="submit"]');
                        if (submitButtons.length !== 1) {
                            e.preventDefault();
                        }
                    }
                }
            });
        });
    }

    // تحسين تجربة المستخدم للقوائم المنسدلة
    function enhanceSelectElements() {
        const selectElements = document.querySelectorAll('select');
        
        selectElements.forEach(function(select) {
            // إضافة فئة CSS للتحسين البصري
            select.classList.add('enhanced-select');
            
            // تحسين التنقل بالكيبورد
            select.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.stopPropagation();
                }
            });
        });
    }

    // تحسين تنظيف حقول النص
    function enhanceTextInputCleaning() {
        const textInputs = document.querySelectorAll('input[type="text"], textarea');

        textInputs.forEach(function(input) {
            // تجاهل حقول الكود التي يجب أن تزيل المسافات
            if (input.id && (input.id.toLowerCase().includes('code') || input.id.toLowerCase().includes('كود'))) {
                return; // لا نطبق التنظيف على حقول الكود
            }

            // تنظيف النص عند فقدان التركيز فقط، وليس أثناء الكتابة
            input.addEventListener('blur', function() {
                if (this.value) {
                    // إزالة المسافات الزائدة في البداية والنهاية
                    this.value = this.value.trim();
                    // تنظيف المسافات المتعددة بين الكلمات (تحويلها إلى مسافة واحدة)
                    this.value = this.value.replace(/\s+/g, ' ');
                }
            });
        });
    }

    // تحسين حقول الأرقام
    function enhanceNumberInputs() {
        const numberInputs = document.querySelectorAll('input[type="number"]');

        numberInputs.forEach(function(input) {
            // منع إدخال الحروف في حقول الأرقام
            input.addEventListener('keypress', function(e) {
                const char = String.fromCharCode(e.which);
                if (!/[0-9]/.test(char) && e.which !== 8 && e.which !== 0) {
                    e.preventDefault();
                }
            });

            // منع استخدام عجلة الماوس لتغيير القيم
            input.addEventListener('wheel', function(e) {
                e.preventDefault();
            });
        });
    }

    // تحسين حقول التاريخ
    function enhanceDateInputs() {
        const dateInputs = document.querySelectorAll('input[type="date"]');
        
        dateInputs.forEach(function(input) {
            // إضافة placeholder للتوضيح
            if (!input.placeholder) {
                input.placeholder = 'اختر التاريخ';
            }
        });
    }

    // إصلاح مشكلة التركيز التلقائي
    function fixAutoFocus() {
        // منع التركيز التلقائي المزعج في بعض المتصفحات
        const autoFocusElements = document.querySelectorAll('[autofocus]');
        
        autoFocusElements.forEach(function(element) {
            // تأخير التركيز قليلاً لضمان تحميل الصفحة بالكامل
            setTimeout(function() {
                if (element.offsetParent !== null) { // التأكد من أن العنصر مرئي
                    element.focus();
                }
            }, 100);
        });
    }

    // تحسين رسائل التحقق
    function enhanceValidationMessages() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                const invalidInputs = form.querySelectorAll(':invalid');
                
                if (invalidInputs.length > 0) {
                    // التركيز على أول حقل غير صحيح
                    invalidInputs[0].focus();
                    
                    // إضافة فئة CSS للتمييز البصري
                    invalidInputs.forEach(function(input) {
                        input.classList.add('is-invalid');
                    });
                }
            });
            
            // إزالة فئة الخطأ عند التصحيح
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(function(input) {
                input.addEventListener('input', function() {
                    if (this.checkValidity()) {
                        this.classList.remove('is-invalid');
                    }
                });
            });
        });
    }

    // تطبيق جميع الإصلاحات عند تحميل الصفحة
    function applyAllFixes() {
        fixSpaceKeyInTextInputs();
        fixEnterKeyInTextInputs();
        enhanceSelectElements();
        enhanceTextInputCleaning();
        enhanceNumberInputs();
        enhanceDateInputs();
        fixAutoFocus();
        enhanceValidationMessages();
    }

    // تطبيق الإصلاحات عند تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyAllFixes);
    } else {
        applyAllFixes();
    }

    // إعادة تطبيق الإصلاحات عند إضافة محتوى جديد ديناميكياً
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // تطبيق الإصلاحات على العناصر الجديدة فقط
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const newTextInputs = node.querySelectorAll('input[type="text"], textarea');
                        newTextInputs.forEach(function(input) {
                            // تجنب إضافة المعالجات أكثر من مرة
                            if (input.hasAttribute('data-enhanced')) {
                                return;
                            }
                            input.setAttribute('data-enhanced', 'true');

                            // تطبيق تنظيف النص (تجاهل حقول الكود)
                            if (!(input.id && (input.id.toLowerCase().includes('code') || input.id.toLowerCase().includes('كود')))) {
                                input.addEventListener('blur', function() {
                                    if (this.value) {
                                        this.value = this.value.trim();
                                        this.value = this.value.replace(/\s+/g, ' ');
                                    }
                                });
                            }
                        });
                    }
                });
            }
        });
    });

    // مراقبة التغييرات في DOM
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // تصدير الدوال للاستخدام الخارجي إذا لزم الأمر
    window.FormFixes = {
        fixSpaceKeyInTextInputs: fixSpaceKeyInTextInputs,
        enhanceSelectElements: enhanceSelectElements,
        applyAllFixes: applyAllFixes
    };

})();
