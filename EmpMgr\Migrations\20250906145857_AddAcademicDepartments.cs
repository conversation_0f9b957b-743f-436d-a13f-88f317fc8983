﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmpMgr.Migrations
{
    /// <inheritdoc />
    public partial class AddAcademicDepartments : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "AcademicDepartmentId",
                table: "Employees",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AcademicDepartments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Code = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CollegeId = table.Column<int>(type: "int", nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    EstablishedYear = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AcademicDepartments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AcademicDepartments_Colleges_CollegeId",
                        column: x => x.CollegeId,
                        principalTable: "Colleges",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Employees_AcademicDepartmentId",
                table: "Employees",
                column: "AcademicDepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicDepartments_CollegeId",
                table: "AcademicDepartments",
                column: "CollegeId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicDepartments_Name_CollegeId",
                table: "AcademicDepartments",
                columns: new[] { "Name", "CollegeId" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Employees_AcademicDepartments_AcademicDepartmentId",
                table: "Employees",
                column: "AcademicDepartmentId",
                principalTable: "AcademicDepartments",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Employees_AcademicDepartments_AcademicDepartmentId",
                table: "Employees");

            migrationBuilder.DropTable(
                name: "AcademicDepartments");

            migrationBuilder.DropIndex(
                name: "IX_Employees_AcademicDepartmentId",
                table: "Employees");

            migrationBuilder.DropColumn(
                name: "AcademicDepartmentId",
                table: "Employees");
        }
    }
}
