﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmpMgr.Migrations
{
    /// <inheritdoc />
    public partial class AddSystemSettingsTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SystemSettings_Key",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "Value",
                table: "SystemSettings");

            migrationBuilder.RenameColumn(
                name: "UpdatedDate",
                table: "SystemSettings",
                newName: "LastUpdated");

            migrationBuilder.RenameColumn(
                name: "Key",
                table: "SystemSettings",
                newName: "TimeZone");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "SystemSettings",
                newName: "OrganizationAddress");

            migrationBuilder.AddColumn<bool>(
                name: "AllowRegistration",
                table: "SystemSettings",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "AllowRememberMe",
                table: "SystemSettings",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "AutoBackupEnabled",
                table: "SystemSettings",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "BackupIntervalHours",
                table: "SystemSettings",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<bool>(
                name: "DarkModeEnabled",
                table: "SystemSettings",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "DefaultLanguage",
                table: "SystemSettings",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "FaviconPath",
                table: "SystemSettings",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastUpdatedBy",
                table: "SystemSettings",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "LockoutDurationMinutes",
                table: "SystemSettings",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "LogoPath",
                table: "SystemSettings",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MaxLoginAttempts",
                table: "SystemSettings",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "OrganizationName",
                table: "SystemSettings",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "OrganizationWebsite",
                table: "SystemSettings",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PrimaryColor",
                table: "SystemSettings",
                type: "nvarchar(7)",
                maxLength: 7,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "SecondaryColor",
                table: "SystemSettings",
                type: "nvarchar(7)",
                maxLength: 7,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "SessionTimeoutMinutes",
                table: "SystemSettings",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "SupportEmail",
                table: "SystemSettings",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SupportPhone",
                table: "SystemSettings",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SystemDescription",
                table: "SystemSettings",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SystemName",
                table: "SystemSettings",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "SystemVersion",
                table: "SystemSettings",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AllowRegistration",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "AllowRememberMe",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "AutoBackupEnabled",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "BackupIntervalHours",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "DarkModeEnabled",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "DefaultLanguage",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "FaviconPath",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "LastUpdatedBy",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "LockoutDurationMinutes",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "LogoPath",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "MaxLoginAttempts",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "OrganizationName",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "OrganizationWebsite",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "PrimaryColor",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "SecondaryColor",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "SessionTimeoutMinutes",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "SupportEmail",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "SupportPhone",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "SystemDescription",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "SystemName",
                table: "SystemSettings");

            migrationBuilder.DropColumn(
                name: "SystemVersion",
                table: "SystemSettings");

            migrationBuilder.RenameColumn(
                name: "TimeZone",
                table: "SystemSettings",
                newName: "Key");

            migrationBuilder.RenameColumn(
                name: "OrganizationAddress",
                table: "SystemSettings",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "LastUpdated",
                table: "SystemSettings",
                newName: "UpdatedDate");

            migrationBuilder.AddColumn<string>(
                name: "Value",
                table: "SystemSettings",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_SystemSettings_Key",
                table: "SystemSettings",
                column: "Key",
                unique: true);
        }
    }
}
