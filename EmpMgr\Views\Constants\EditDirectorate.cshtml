@model EmpMgr.Models.Directorate

@{
    ViewData["Title"] = "تعديل المديرية";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المديرية: @Model.Name
                    </h4>
                </div>

                <div class="card-body">
                    <form asp-action="EditDirectorate" method="post">
                        <input asp-for="Id" type="hidden" />
                        <input asp-for="CreatedDate" type="hidden" />
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label asp-for="Name" class="form-label required"></label>
                                <input asp-for="Name" class="form-control" placeholder="أدخل اسم المديرية">
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="Code" class="form-label"></label>
                                <input asp-for="Code" class="form-control" placeholder="رمز المديرية">
                                <span asp-validation-for="Code" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="AgencyId" class="form-label required"></label>
                                <select asp-for="AgencyId" class="form-select" asp-items="ViewBag.AgencyId">
                                    <option value="">اختر الوكالة</option>
                                </select>
                                <span asp-validation-for="AgencyId" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label asp-for="Description" class="form-label"></label>
                                <textarea asp-for="Description" class="form-control" rows="3" 
                                          placeholder="وصف مختصر عن المديرية ومهامها"></textarea>
                                <span asp-validation-for="Description" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="EstablishedYear" class="form-label"></label>
                                <input asp-for="EstablishedYear" class="form-control" type="number" 
                                       min="1900" max="@DateTime.Now.Year" placeholder="سنة التأسيس">
                                <span asp-validation-for="EstablishedYear" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input">
                                <label asp-for="IsActive" class="form-check-label">
                                    المديرية نشطة
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-dark">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التعديلات
                                </button>
                                <a asp-action="Directorates" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                            <a asp-action="Directorates" class="btn btn-outline-dark">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // تركيز على حقل الاسم
            $('#Name').focus();
        });
    </script>
}

<style>
    .required::after {
        content: " *";
        color: red;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #212529;
        box-shadow: 0 0 0 0.2rem rgba(33, 37, 41, 0.25);
    }
    
    .btn-dark:focus {
        box-shadow: 0 0 0 0.2rem rgba(33, 37, 41, 0.5);
    }
</style>
