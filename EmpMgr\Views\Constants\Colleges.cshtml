@model IEnumerable<EmpMgr.Models.College>

@{
    ViewData["Title"] = "إدارة الكليات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-building me-2"></i>
                    إدارة الكليات
                </h2>
                <div>
                    <a asp-action="CreateCollege" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة كلية جديدة
                    </a>
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للثوابت
                    </a>
                </div>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الكليات
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الرقم</th>
                                        <th>اسم الكلية</th>
                                        <th>الجامعة</th>
                                        <th>الكود</th>
                                        <th>النوع</th>
                                        <th>سنة التأسيس</th>
                                        <th>الحالة</th>
                                        <th>عدد الموظفين</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @{int counter = 1;}
                                    @foreach (var college in Model)
                                    {
                                        <tr>
                                            <td>@counter</td>
                                            <td>
                                                <strong>@college.Name</strong>
                                                @if (!string.IsNullOrEmpty(college.Description))
                                                {
                                                    <br><small class="text-muted">@college.Description</small>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@college.University?.Name</span>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(college.Code))
                                                {
                                                    <span class="badge bg-secondary">@college.Code</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                            <td>
                                                @switch (college.Type)
                                                {
                                                    case EmpMgr.Models.CollegeType.Scientific:
                                                        <span class="badge bg-primary">علمية</span>
                                                        break;
                                                    case EmpMgr.Models.CollegeType.Humanities:
                                                        <span class="badge bg-secondary">إنسانية</span>
                                                        break;
                                                    case EmpMgr.Models.CollegeType.Medical:
                                                        <span class="badge bg-danger">طبية</span>
                                                        break;
                                                    case EmpMgr.Models.CollegeType.Engineering:
                                                        <span class="badge bg-warning text-dark">هندسية</span>
                                                        break;
                                                    case EmpMgr.Models.CollegeType.Technical:
                                                        <span class="badge bg-info">تقنية</span>
                                                        break;
                                                    case EmpMgr.Models.CollegeType.Administrative:
                                                        <span class="badge bg-success">إدارية</span>
                                                        break;
                                                    case EmpMgr.Models.CollegeType.Legal:
                                                        <span class="badge bg-dark">قانونية</span>
                                                        break;
                                                    case EmpMgr.Models.CollegeType.Educational:
                                                        <span class="badge bg-light text-dark">تربوية</span>
                                                        break;
                                                    case EmpMgr.Models.CollegeType.Arts:
                                                        <span class="badge" style="background-color: #6f42c1; color: white;">فنية</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">غير محدد</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                @if (college.EstablishedYear.HasValue)
                                                {
                                                    <span class="text-muted">@college.EstablishedYear</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                            <td>
                                                @if (college.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@college.Employees.Count</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="EditCollege" asp-route-id="@college.Id" 
                                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @if (college.Employees.Count == 0)
                                                    {
                                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                onclick="confirmDelete(@college.Id, '@college.Name')" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    }
                                                    else
                                                    {
                                                        <button type="button" class="btn btn-sm btn-outline-secondary" 
                                                                disabled title="لا يمكن الحذف - يوجد موظفين مرتبطين">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                        counter++;
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد كليات مضافة</h5>
                            <p class="text-muted">ابدأ بإضافة الكليات لتتمكن من إدارة بيانات الموظفين التعليمية بشكل أكثر تفصيلاً</p>
                            <a asp-action="CreateCollege" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>
                                إضافة أول كلية
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للتأكيد من الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الكلية <strong id="collegeName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    هذا الإجراء لا يمكن التراجع عنه
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" class="d-inline">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(collegeId, collegeName) {
            document.getElementById('collegeName').textContent = collegeName;
            document.getElementById('deleteForm').action = '@Url.Action("DeleteCollege")/' + collegeId;
            
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        // إخفاء الرسائل تلقائياً بعد 5 ثوان
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
}
