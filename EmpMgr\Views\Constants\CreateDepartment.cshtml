@model EmpMgr.Models.Department

@{
    ViewData["Title"] = "إضافة قسم جديد";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-plus-circle me-2"></i>
                    إضافة قسم جديد
                </h2>
                <a asp-action="Departments" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة لقائمة الأقسام
                </a>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-sitemap me-2"></i>
                                بيانات القسم الجديد
                            </h5>
                        </div>
                        <div class="card-body">
                            <form asp-action="CreateDepartment" method="post">
                                <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                                <!-- المعهد -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label asp-for="InstituteId" class="form-label required">المعهد</label>
                                        <select asp-for="InstituteId" class="form-select" required>
                                            <option value="">اختر المعهد</option>
                                            @foreach (var institute in (ViewBag.Institutes as List<EmpMgr.Models.Institute>) ?? new List<EmpMgr.Models.Institute>())
                                            {
                                                <option value="@institute.Id">@institute.Name</option>
                                            }
                                        </select>
                                        <span asp-validation-for="InstituteId" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <label asp-for="Type" class="form-label required">نوع القسم</label>
                                        <select asp-for="Type" class="form-select" asp-items="Html.GetEnumSelectList<EmpMgr.Models.DepartmentType>()" required>
                                            <option value="">اختر نوع القسم</option>
                                        </select>
                                        <span asp-validation-for="Type" class="text-danger"></span>
                                    </div>
                                </div>

                                <!-- اسم القسم والكود -->
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label asp-for="Name" class="form-label required">اسم القسم</label>
                                        <input asp-for="Name" class="form-control" placeholder="أدخل اسم القسم" required />
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4">
                                        <label asp-for="Code" class="form-label">رمز القسم</label>
                                        <input asp-for="Code" class="form-control" placeholder="مثال: CS" />
                                        <span asp-validation-for="Code" class="text-danger"></span>
                                    </div>
                                </div>

                                <!-- الوصف -->
                                <div class="mb-3">
                                    <label asp-for="Description" class="form-label">الوصف</label>
                                    <textarea asp-for="Description" class="form-control" rows="3" 
                                              placeholder="وصف مختصر عن القسم وأنشطته"></textarea>
                                    <span asp-validation-for="Description" class="text-danger"></span>
                                </div>

                                <!-- سنة التأسيس والحالة -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label asp-for="EstablishedYear" class="form-label">سنة التأسيس</label>
                                        <input asp-for="EstablishedYear" type="number" class="form-control" 
                                               min="1900" max="@DateTime.Now.Year" placeholder="@DateTime.Now.Year" />
                                        <span asp-validation-for="EstablishedYear" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">الحالة</label>
                                        <div class="form-check form-switch">
                                            <input asp-for="IsActive" class="form-check-input" type="checkbox" checked />
                                            <label asp-for="IsActive" class="form-check-label">
                                                القسم نشط
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- أزرار الإجراءات -->
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ القسم
                                        </button>
                                        <button type="reset" class="btn btn-outline-secondary">
                                            <i class="fas fa-undo me-2"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                    <a asp-action="Departments" class="btn btn-outline-danger">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات مهمة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">
                                        <i class="fas fa-graduation-cap me-2"></i>
                                        حول المعاهد
                                    </h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>كل قسم يجب أن يكون مرتبط بمعهد</li>
                                        <li><i class="fas fa-check text-success me-2"></i>لا يمكن تكرار اسم القسم في نفس المعهد</li>
                                        <li><i class="fas fa-check text-success me-2"></i>يمكن نقل القسم بين المعاهد</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-info">
                                        <i class="fas fa-sitemap me-2"></i>
                                        أنواع الأقسام
                                    </h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-cog text-primary me-2"></i>تقني</li>
                                        <li><i class="fas fa-briefcase text-warning me-2"></i>إداري</li>
                                        <li><i class="fas fa-heartbeat text-danger me-2"></i>طبي</li>
                                        <li><i class="fas fa-laptop-code text-info me-2"></i>حاسوب</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تحسين تجربة المستخدم
            const nameInput = document.getElementById('Name');
            const codeInput = document.getElementById('Code');
            
            // إنشاء كود تلقائي من الاسم
            nameInput.addEventListener('input', function() {
                if (!codeInput.value) {
                    const name = this.value.trim();
                    if (name) {
                        // استخراج الأحرف الأولى من الكلمات
                        const words = name.split(' ');
                        let code = '';
                        words.forEach(word => {
                            if (word.length > 0) {
                                code += word.charAt(0).toUpperCase();
                            }
                        });
                        codeInput.value = code.substring(0, 10); // حد أقصى 10 أحرف
                    }
                }
            });

            // تحديد السنة الحالية كافتراضي
            const yearInput = document.getElementById('EstablishedYear');
            if (!yearInput.value) {
                yearInput.value = new Date().getFullYear();
            }
        });
    </script>
}
