-- إضافة كليات أساسية لجامعة بغداد (ID = 1)
IF NOT EXISTS (SELECT 1 FROM Colleges)
BEGIN
    INSERT INTO Colleges (Name, UniversityId, Type, IsActive, CreatedDate) VALUES
    ('كلية الطب', 1, 3, 1, GETDATE()),
    ('كلية الهندسة', 1, 4, 1, GETDATE()),
    ('كلية العلوم', 1, 1, 1, GETDATE()),
    ('كلية الآداب', 1, 2, 1, GETDATE()),
    ('كلية القانون', 1, 5, 1, GETDATE()),
    ('كلية الإدارة والاقتصاد', 1, 6, 1, GETDATE()),
    ('كلية التربية', 1, 7, 1, GETDATE()),
    ('كلية الصيدلة', 1, 3, 1, GETDATE()),
    ('كلية طب الأسنان', 1, 3, 1, GETDATE()),
    ('كلية الزراعة', 1, 1, 1, GETDATE());
END

-- إضافة أقسام أساسية للمعاهد
IF NOT EXISTS (SELECT 1 FROM Departments)
BEGIN
    INSERT INTO Departments (Name, InstituteId, Type, IsActive, CreatedDate) VALUES
    -- أقسام معهد الإدارة التقني (ID = 1)
    ('قسم تقنيات المحاسبة', 1, 6, 1, GETDATE()),
    ('قسم تقنيات الإدارة', 1, 2, 1, GETDATE()),
    ('قسم تقنيات الحاسوب', 1, 5, 1, GETDATE()),
    ('قسم التقنيات القانونية', 1, 7, 1, GETDATE()),
    
    -- أقسام المعهد التقني الطبي (ID = 2)
    ('قسم تقنيات التمريض', 2, 3, 1, GETDATE()),
    ('قسم تقنيات المختبرات الطبية', 2, 3, 1, GETDATE()),
    ('قسم تقنيات الأشعة', 2, 3, 1, GETDATE()),
    
    -- أقسام المعهد التقني الهندسي (ID = 3)
    ('قسم تقنيات الهندسة المدنية', 3, 4, 1, GETDATE()),
    ('قسم تقنيات الهندسة الكهربائية', 3, 4, 1, GETDATE()),
    ('قسم تقنيات الهندسة الميكانيكية', 3, 4, 1, GETDATE());
END

-- إضافة تخصصات أساسية
IF NOT EXISTS (SELECT 1 FROM Specializations)
BEGIN
    INSERT INTO Specializations (Name, Type, AcademicLevel, CollegeId, DepartmentId, DurationYears, IsActive, CreatedDate) VALUES
    -- تخصصات كلية الطب (CollegeId = 1)
    ('الطب العام', 4, 2, 1, NULL, 6, 1, GETDATE()),
    ('طب الأطفال', 4, 3, 1, NULL, 4, 1, GETDATE()),
    ('الجراحة العامة', 4, 3, 1, NULL, 5, 1, GETDATE()),
    
    -- تخصصات كلية الهندسة (CollegeId = 2)
    ('الهندسة المدنية', 5, 2, 2, NULL, 4, 1, GETDATE()),
    ('الهندسة الكهربائية', 5, 2, 2, NULL, 4, 1, GETDATE()),
    ('الهندسة الميكانيكية', 5, 2, 2, NULL, 4, 1, GETDATE()),
    ('هندسة الحاسوب', 5, 2, 2, NULL, 4, 1, GETDATE()),
    
    -- تخصصات كلية العلوم (CollegeId = 3)
    ('الرياضيات', 1, 2, 3, NULL, 4, 1, GETDATE()),
    ('الفيزياء', 1, 2, 3, NULL, 4, 1, GETDATE()),
    ('الكيمياء', 1, 2, 3, NULL, 4, 1, GETDATE()),
    ('علوم الحاسوب', 1, 2, 3, NULL, 4, 1, GETDATE()),
    
    -- تخصصات كلية الآداب (CollegeId = 4)
    ('اللغة العربية', 2, 2, 4, NULL, 4, 1, GETDATE()),
    ('اللغة الإنجليزية', 2, 2, 4, NULL, 4, 1, GETDATE()),
    ('التاريخ', 2, 2, 4, NULL, 4, 1, GETDATE()),
    ('الجغرافية', 2, 2, 4, NULL, 4, 1, GETDATE()),
    
    -- تخصصات كلية القانون (CollegeId = 5)
    ('القانون العام', 6, 2, 5, NULL, 4, 1, GETDATE()),
    ('القانون الخاص', 6, 2, 5, NULL, 4, 1, GETDATE()),
    
    -- تخصصات كلية الإدارة والاقتصاد (CollegeId = 6)
    ('إدارة الأعمال', 7, 2, 6, NULL, 4, 1, GETDATE()),
    ('المحاسبة', 7, 2, 6, NULL, 4, 1, GETDATE()),
    ('الاقتصاد', 7, 2, 6, NULL, 4, 1, GETDATE()),
    ('الإحصاء', 7, 2, 6, NULL, 4, 1, GETDATE()),
    
    -- تخصصات الأقسام التقنية
    -- قسم تقنيات المحاسبة (DepartmentId = 1)
    ('تقنيات المحاسبة', 3, 1, NULL, 1, 2, 1, GETDATE()),
    
    -- قسم تقنيات الإدارة (DepartmentId = 2)
    ('تقنيات الإدارة', 3, 1, NULL, 2, 2, 1, GETDATE()),
    
    -- قسم تقنيات الحاسوب (DepartmentId = 3)
    ('تقنيات الحاسوب', 3, 1, NULL, 3, 2, 1, GETDATE()),
    ('تقنيات البرمجة', 3, 1, NULL, 3, 2, 1, GETDATE()),
    
    -- قسم تقنيات التمريض (DepartmentId = 5)
    ('تقنيات التمريض', 4, 1, NULL, 5, 2, 1, GETDATE()),
    
    -- قسم تقنيات المختبرات الطبية (DepartmentId = 6)
    ('تقنيات المختبرات الطبية', 4, 1, NULL, 6, 2, 1, GETDATE());
END

PRINT 'تم إضافة البيانات الأساسية بنجاح';
