@model EmpMgr.Models.Employee
@{
    ViewData["Title"] = "تفاصيل الموظف";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="card shadow mb-4">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-circle me-2"></i>
                        تفاصيل الموظف: @Model.FullName
                    </h4>
                    <div class="btn-group">
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <a asp-action="Print" asp-route-id="@Model.Id" class="btn btn-secondary btn-sm" target="_blank">
                            <i class="fas fa-print me-1"></i>طباعة
                        </a>
                        <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger btn-sm">
                            <i class="fas fa-trash me-1"></i>حذف
                        </a>
                        <a asp-action="Index" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- الصورة والمعلومات الأساسية -->
                <div class="col-lg-4">
                    <div class="card shadow mb-4">
                        <div class="card-body text-center">
                            <div class="employee-photo mb-3">
                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center shadow"
                                     style="width: 200px; height: 200px; margin: 0 auto;">
                                    <i class="fas fa-user fa-5x text-muted"></i>
                                </div>
                            </div>
                            <h3 class="text-primary">@Model.FullName</h3>
                            <p class="text-muted mb-2">
                                @if (Model.Gender == EmpMgr.Models.Gender.Male)
                                {
                                    <i class="fas fa-mars text-primary me-1"></i><span>ذكر</span>
                                }
                                else
                                {
                                    <i class="fas fa-venus text-danger me-1"></i><span>أنثى</span>
                                }
                            </p>
                            @if (Model.Rank != null)
                            {
                                <span class="badge bg-success fs-6 mb-2">@Model.Rank.Name</span>
                            }
                            <div class="row text-center mt-3">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h5 class="text-primary">@Model.EmployeeNumber</h5>
                                        <small class="text-muted">رقم الموظف</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h5 class="text-info">@Model.StatisticalNumber</h5>
                                    <small class="text-muted">الرقم الإحصائي</small>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>

                <!-- التفاصيل -->
                <div class="col-lg-8">
                    <!-- البيانات الشخصية -->
                    <div class="card shadow mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-id-card me-2"></i>البيانات الشخصية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الاسم الأول:</label>
                                    <p class="fw-bold">@Model.FirstName</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">اسم الأب:</label>
                                    <p class="fw-bold">@Model.FatherName</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">اسم الجد:</label>
                                    <p class="fw-bold">@Model.GrandFatherName</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">اسم الجد الأكبر:</label>
                                    <p class="fw-bold">@Model.GreatGrandFatherName</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">اللقب:</label>
                                    <p class="fw-bold">@Model.LastName</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الحالة الاجتماعية:</label>
                                    <p class="fw-bold">
                                        @switch (Model.MaritalStatus)
                                        {
                                            case EmpMgr.Models.MaritalStatus.Single:
                                                <span class="badge bg-info">أعزب</span>
                                                break;
                                            case EmpMgr.Models.MaritalStatus.Married:
                                                <span class="badge bg-success">متزوج</span>
                                                break;
                                            case EmpMgr.Models.MaritalStatus.Divorced:
                                                <span class="badge bg-warning">مطلق</span>
                                                break;
                                            case EmpMgr.Models.MaritalStatus.Widowed:
                                                <span class="badge bg-secondary">أرمل</span>
                                                break;
                                        }
                                    </p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">فصيلة الدم:</label>
                                    <p class="fw-bold">
                                        <span class="badge bg-danger">@Model.BloodType</span>
                                    </p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الحالة الصحية:</label>
                                    <p class="fw-bold">
                                        @switch (Model.HealthStatus)
                                        {
                                            case EmpMgr.Models.HealthStatus.Healthy:
                                                <span class="badge bg-success">سليم</span>
                                                break;
                                            case EmpMgr.Models.HealthStatus.Sick:
                                                <span class="badge bg-warning">مريض</span>
                                                break;
                                            case EmpMgr.Models.HealthStatus.Disabled:
                                                <span class="badge bg-info">معاق</span>
                                                break;
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات السكن -->
                    <div class="card shadow mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-home me-2"></i>معلومات السكن
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">المحافظة:</label>
                                    <p class="fw-bold">@(Model.Province?.Name ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">القضاء:</label>
                                    <p class="fw-bold">@(Model.District ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الناحية:</label>
                                    <p class="fw-bold">@(Model.Subdistrict ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">القرية:</label>
                                    <p class="fw-bold">@(Model.Village ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">المحلة:</label>
                                    <p class="fw-bold">@(Model.Neighborhood ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الحي:</label>
                                    <p class="fw-bold">@(Model.Quarter ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الزقاق:</label>
                                    <p class="fw-bold">@(Model.Alley ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">رقم الدار:</label>
                                    <p class="fw-bold">@(Model.House ?? "غير محدد")</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات التعليم -->
                    <div class="card shadow mb-4">
                        <div class="card-header bg-warning text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-graduation-cap me-2"></i>معلومات التعليم
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">المستوى التعليمي:</label>
                                    <p class="fw-bold">
                                        @switch (Model.EducationLevel)
                                        {
                                            case EmpMgr.Models.EducationLevel.Primary:
                                                <span class="badge bg-secondary">ابتدائية</span>
                                                break;
                                            case EmpMgr.Models.EducationLevel.Intermediate:
                                                <span class="badge bg-info">متوسطة</span>
                                                break;
                                            case EmpMgr.Models.EducationLevel.Secondary:
                                                <span class="badge bg-primary">إعدادية</span>
                                                break;
                                            case EmpMgr.Models.EducationLevel.Diploma:
                                                <span class="badge bg-warning">دبلوم</span>
                                                break;
                                            case EmpMgr.Models.EducationLevel.Bachelor:
                                                <span class="badge bg-success">بكالوريوس</span>
                                                break;
                                            case EmpMgr.Models.EducationLevel.Master:
                                                <span class="badge bg-warning">ماجستير</span>
                                                break;
                                            case EmpMgr.Models.EducationLevel.PhD:
                                                <span class="badge bg-danger">دكتوراه</span>
                                                break;
                                        }
                                    </p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الجامعة:</label>
                                    <p class="fw-bold">@(Model.University?.Name ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">المعهد:</label>
                                    <p class="fw-bold">@(Model.Institute?.Name ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الكلية:</label>
                                    <p class="fw-bold">@(Model.College?.Name ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">القسم:</label>
                                    <p class="fw-bold">@(Model.Department?.Name ?? "غير محدد")</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المعلومات الوظيفية -->
                    <div class="card shadow mb-4">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-building me-2"></i>المعلومات الوظيفية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الوزارة:</label>
                                    <p class="fw-bold">@(Model.Ministry?.Name ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الوكالة:</label>
                                    <p class="fw-bold">@(Model.Agency?.Name ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">المديرية:</label>
                                    <p class="fw-bold">@(Model.Directorate?.Name ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">القسم الحكومي:</label>
                                    <p class="fw-bold">@(Model.GovernmentDepartment?.Name ?? "غير محدد")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الشعبة:</label>
                                    <p class="fw-bold">@(Model.Division?.Name ?? "غير محدد")</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="card shadow mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات إضافية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">تاريخ الإضافة:</label>
                                    <p class="fw-bold">@Model.CreatedDate.ToString("yyyy/MM/dd HH:mm")</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">آخر تحديث:</label>
                                    <p class="fw-bold">@(Model.UpdatedDate?.ToString("yyyy/MM/dd HH:mm") ?? "لم يتم التحديث")</p>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // تحسين عرض الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات hover للبطاقات
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.2s ease';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });


    </script>
}
