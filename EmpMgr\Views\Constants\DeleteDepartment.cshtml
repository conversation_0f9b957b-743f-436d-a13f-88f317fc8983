@model EmpMgr.Models.Department

@{
    ViewData["Title"] = "حذف القسم";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-danger">
                    <i class="fas fa-trash me-2"></i>
                    حذف القسم
                </h2>
                <a asp-action="Departments" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة لقائمة الأقسام
                </a>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <!-- تحذير -->
                    <div class="alert alert-danger" role="alert">
                        <h4 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تحذير!
                        </h4>
                        <p>أنت على وشك حذف القسم التالي. هذا الإجراء لا يمكن التراجع عنه.</p>
                        <hr>
                        <p class="mb-0">
                            <strong>تأكد من أن هذا القسم غير مرتبط بأي موظفين قبل الحذف.</strong>
                        </p>
                    </div>

                    <!-- بيانات القسم -->
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-sitemap me-2"></i>
                                بيانات القسم المراد حذفه
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">معلومات أساسية</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>اسم القسم:</strong></td>
                                            <td>@Model.Name</td>
                                        </tr>
                                        <tr>
                                            <td><strong>المعهد:</strong></td>
                                            <td>
                                                <span class="badge bg-warning text-dark">
                                                    @Model.Institute?.Name
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الكود:</strong></td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.Code))
                                                {
                                                    <span class="badge bg-secondary">@Model.Code</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>النوع:</strong></td>
                                            <td>
                                                <span class="badge bg-info">
                                                    @Html.DisplayFor(model => model.Type)
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-info">معلومات إضافية</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>سنة التأسيس:</strong></td>
                                            <td>
                                                @if (Model.EstablishedYear.HasValue)
                                                {
                                                    @Model.EstablishedYear.Value
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الإنشاء:</strong></td>
                                            <td>@Model.CreatedDate.ToString("dd/MM/yyyy")</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                @if (Model.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>عدد الموظفين:</strong></td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    @Model.Employees.Count
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            @if (!string.IsNullOrEmpty(Model.Description))
                            {
                                <div class="mt-3">
                                    <h6 class="text-secondary">الوصف</h6>
                                    <p class="text-muted">@Model.Description</p>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- تحقق من الموظفين المرتبطين -->
                    @if (Model.Employees.Any())
                    {
                        <div class="card mt-4 border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    الموظفون المرتبطون بهذا القسم
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning" role="alert">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        تحذير: لا يمكن حذف هذا القسم
                                    </h6>
                                    <p>هذا القسم مرتبط بـ <strong>@Model.Employees.Count موظف</strong>. يجب نقل أو حذف هؤلاء الموظفين أولاً قبل حذف القسم.</p>
                                </div>
                                
                                <h6>قائمة الموظفين المرتبطين:</h6>
                                <ul class="list-group">
                                    @foreach (var employee in Model.Employees.Take(5))
                                    {
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>
                                                <strong>@employee.FirstName @employee.FatherName @employee.GrandFatherName @employee.LastName</strong>
                                                <br>
                                                <small class="text-muted">رقم الموظف: @employee.EmployeeNumber</small>
                                            </span>
                                            <span class="badge bg-primary">@employee.Rank?.Name</span>
                                        </li>
                                    }
                                    @if (Model.Employees.Count > 5)
                                    {
                                        <li class="list-group-item text-center text-muted">
                                            ... و @(Model.Employees.Count - 5) موظف آخر
                                        </li>
                                    }
                                </ul>
                            </div>
                        </div>
                    }

                    <!-- أزرار الإجراءات -->
                    <div class="card mt-4">
                        <div class="card-body">
                            @if (Model.Employees.Any())
                            {
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="button" class="btn btn-danger" disabled>
                                            <i class="fas fa-trash me-2"></i>
                                            لا يمكن الحذف
                                        </button>
                                        <small class="text-muted d-block mt-1">
                                            القسم مرتبط بموظفين
                                        </small>
                                    </div>
                                    <div>
                                        <a asp-action="EditDepartment" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                                            <i class="fas fa-edit me-2"></i>
                                            تعديل القسم
                                        </a>
                                        <a asp-action="Departments" class="btn btn-outline-secondary">
                                            <i class="fas fa-arrow-right me-2"></i>
                                            العودة للقائمة
                                        </a>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <form asp-action="DeleteDepartment" method="post">
                                    <input asp-for="Id" type="hidden" />
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <button type="submit" class="btn btn-danger">
                                                <i class="fas fa-trash me-2"></i>
                                                تأكيد الحذف
                                            </button>
                                            <small class="text-muted d-block mt-1">
                                                هذا الإجراء لا يمكن التراجع عنه
                                            </small>
                                        </div>
                                        <div>
                                            <a asp-action="EditDepartment" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                                                <i class="fas fa-edit me-2"></i>
                                                تعديل بدلاً من الحذف
                                            </a>
                                            <a asp-action="Departments" class="btn btn-outline-secondary">
                                                <i class="fas fa-times me-2"></i>
                                                إلغاء
                                            </a>
                                        </div>
                                    </div>
                                </form>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأكيد الحذف النهائي
            const deleteForm = document.querySelector('form[action*="DeleteDepartment"]');
            if (deleteForm) {
                deleteForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const departmentName = '@Model.Name';
                    const confirmMessage = `هل أنت متأكد تماماً من حذف القسم "${departmentName}"؟\n\nهذا الإجراء نهائي ولا يمكن التراجع عنه.`;
                    
                    if (confirm(confirmMessage)) {
                        this.submit();
                    }
                });
            }
        });
    </script>
}
