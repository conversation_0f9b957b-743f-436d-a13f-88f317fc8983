using System.ComponentModel.DataAnnotations;

namespace EmpMgr.Models
{
    public class SystemSettings
    {
        [Key]
        public int Id { get; set; }

        [Display(Name = "اسم النظام")]
        [StringLength(200, ErrorMessage = "اسم النظام يجب أن يكون أقل من 200 حرف")]
        public string SystemName { get; set; } = "نظام إدارة الضباط والمنتسبين";

        [Display(Name = "اسم المؤسسة")]
        [StringLength(200, ErrorMessage = "اسم المؤسسة يجب أن يكون أقل من 200 حرف")]
        public string OrganizationName { get; set; } = "وزارة الداخلية - جمهورية العراق";

        [Display(Name = "شعار النظام")]
        [StringLength(500, ErrorMessage = "مسار الشعار يجب أن يكون أقل من 500 حرف")]
        public string? LogoPath { get; set; } = "/images/moi-logo.png";

        [Display(Name = "أيقونة النظام")]
        [StringLength(500, ErrorMessage = "مسار الأيقونة يجب أن يكون أقل من 500 حرف")]
        public string? FaviconPath { get; set; } = "/images/favicon.ico";

        [Display(Name = "لون النظام الأساسي")]
        [StringLength(7, ErrorMessage = "لون النظام يجب أن يكون بصيغة hex")]
        public string PrimaryColor { get; set; } = "#667eea";

        [Display(Name = "لون النظام الثانوي")]
        [StringLength(7, ErrorMessage = "لون النظام يجب أن يكون بصيغة hex")]
        public string SecondaryColor { get; set; } = "#764ba2";

        [Display(Name = "وصف النظام")]
        [StringLength(1000, ErrorMessage = "وصف النظام يجب أن يكون أقل من 1000 حرف")]
        public string? SystemDescription { get; set; } = "نظام شامل لإدارة بيانات الضباط والمنتسبين";

        [Display(Name = "إصدار النظام")]
        [StringLength(20, ErrorMessage = "إصدار النظام يجب أن يكون أقل من 20 حرف")]
        public string SystemVersion { get; set; } = "1.0.0";

        [Display(Name = "البريد الإلكتروني للدعم")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(200, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 200 حرف")]
        public string? SupportEmail { get; set; }

        [Display(Name = "رقم هاتف الدعم")]
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 حرف")]
        public string? SupportPhone { get; set; }

        [Display(Name = "عنوان المؤسسة")]
        [StringLength(500, ErrorMessage = "العنوان يجب أن يكون أقل من 500 حرف")]
        public string? OrganizationAddress { get; set; }

        [Display(Name = "موقع المؤسسة الإلكتروني")]
        [Url(ErrorMessage = "رابط الموقع غير صحيح")]
        [StringLength(200, ErrorMessage = "رابط الموقع يجب أن يكون أقل من 200 حرف")]
        public string? OrganizationWebsite { get; set; }

        [Display(Name = "تفعيل التسجيل الجديد")]
        public bool AllowRegistration { get; set; } = false;

        [Display(Name = "تفعيل ميزة تذكرني")]
        public bool AllowRememberMe { get; set; } = true;

        [Display(Name = "مدة انتهاء الجلسة (بالدقائق)")]
        [Range(5, 1440, ErrorMessage = "مدة الجلسة يجب أن تكون بين 5 دقائق و 24 ساعة")]
        public int SessionTimeoutMinutes { get; set; } = 60;

        [Display(Name = "عدد محاولات تسجيل الدخول المسموحة")]
        [Range(3, 10, ErrorMessage = "عدد المحاولات يجب أن يكون بين 3 و 10")]
        public int MaxLoginAttempts { get; set; } = 5;

        [Display(Name = "مدة حظر المستخدم (بالدقائق)")]
        [Range(5, 1440, ErrorMessage = "مدة الحظر يجب أن تكون بين 5 دقائق و 24 ساعة")]
        public int LockoutDurationMinutes { get; set; } = 15;

        [Display(Name = "تاريخ آخر تحديث")]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        [Display(Name = "المستخدم الذي قام بآخر تحديث")]
        [StringLength(200, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 200 حرف")]
        public string? LastUpdatedBy { get; set; }

        [Display(Name = "تفعيل الوضع المظلم")]
        public bool DarkModeEnabled { get; set; } = false;

        [Display(Name = "اللغة الافتراضية")]
        [StringLength(10, ErrorMessage = "رمز اللغة يجب أن يكون أقل من 10 أحرف")]
        public string DefaultLanguage { get; set; } = "ar";

        [Display(Name = "المنطقة الزمنية")]
        [StringLength(100, ErrorMessage = "المنطقة الزمنية يجب أن تكون أقل من 100 حرف")]
        public string TimeZone { get; set; } = "Asia/Baghdad";

        [Display(Name = "تفعيل النسخ الاحتياطي التلقائي")]
        public bool AutoBackupEnabled { get; set; } = true;

        [Display(Name = "تكرار النسخ الاحتياطي (بالساعات)")]
        [Range(1, 168, ErrorMessage = "تكرار النسخ الاحتياطي يجب أن يكون بين ساعة واحدة و أسبوع")]
        public int BackupIntervalHours { get; set; } = 24;
    }
}
