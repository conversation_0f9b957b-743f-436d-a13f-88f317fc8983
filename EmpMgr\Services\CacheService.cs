using Microsoft.Extensions.Caching.Memory;
using Microsoft.EntityFrameworkCore;
using EmpMgr.Data;
using EmpMgr.Models;

namespace EmpMgr.Services
{
    public class CacheService : ICacheService
    {
        private readonly ApplicationDbContext _context;
        private readonly IMemoryCache _cache;
        private readonly TimeSpan _cacheExpiration = TimeSpan.FromHours(1);

        public CacheService(ApplicationDbContext context, IMemoryCache cache)
        {
            _context = context;
            _cache = cache;
        }

        public async Task<List<Rank>> GetRanksAsync()
        {
            const string cacheKey = "ranks";
            if (!_cache.TryGetValue(cacheKey, out List<Rank>? ranks))
            {
                ranks = await _context.Ranks
                    .OrderBy(r => r.Order)
                    .ToListAsync();
                
                _cache.Set(cacheKey, ranks, _cacheExpiration);
            }
            return ranks ?? new List<Rank>();
        }

        public async Task<List<Province>> GetProvincesAsync()
        {
            const string cacheKey = "provinces";
            if (!_cache.TryGetValue(cacheKey, out List<Province>? provinces))
            {
                provinces = await _context.Provinces
                    .OrderBy(p => p.Name)
                    .ToListAsync();
                
                _cache.Set(cacheKey, provinces, _cacheExpiration);
            }
            return provinces ?? new List<Province>();
        }

        public async Task<List<University>> GetUniversitiesAsync()
        {
            const string cacheKey = "universities";
            if (!_cache.TryGetValue(cacheKey, out List<University>? universities))
            {
                universities = await _context.Universities
                    .OrderBy(u => u.Name)
                    .ToListAsync();
                
                _cache.Set(cacheKey, universities, _cacheExpiration);
            }
            return universities ?? new List<University>();
        }

        public async Task<List<Institute>> GetInstitutesAsync()
        {
            const string cacheKey = "institutes";
            if (!_cache.TryGetValue(cacheKey, out List<Institute>? institutes))
            {
                institutes = await _context.Institutes
                    .OrderBy(i => i.Name)
                    .ToListAsync();
                
                _cache.Set(cacheKey, institutes, _cacheExpiration);
            }
            return institutes ?? new List<Institute>();
        }

        public async Task<List<College>> GetCollegesAsync()
        {
            const string cacheKey = "colleges";
            if (!_cache.TryGetValue(cacheKey, out List<College>? colleges))
            {
                colleges = await _context.Colleges
                    .Include(c => c.University)
                    .OrderBy(c => c.Name)
                    .ToListAsync();
                
                _cache.Set(cacheKey, colleges, _cacheExpiration);
            }
            return colleges ?? new List<College>();
        }

        public async Task<List<Ministry>> GetMinistriesAsync()
        {
            const string cacheKey = "ministries";
            if (!_cache.TryGetValue(cacheKey, out List<Ministry>? ministries))
            {
                ministries = await _context.Ministries
                    .OrderBy(m => m.Name)
                    .ToListAsync();
                
                _cache.Set(cacheKey, ministries, _cacheExpiration);
            }
            return ministries ?? new List<Ministry>();
        }

        public async Task<List<Agency>> GetAgenciesAsync()
        {
            const string cacheKey = "agencies";
            if (!_cache.TryGetValue(cacheKey, out List<Agency>? agencies))
            {
                agencies = await _context.Agencies
                    .Include(a => a.Ministry)
                    .OrderBy(a => a.Name)
                    .ToListAsync();
                
                _cache.Set(cacheKey, agencies, _cacheExpiration);
            }
            return agencies ?? new List<Agency>();
        }

        public async Task<List<Directorate>> GetDirectoratesAsync()
        {
            const string cacheKey = "directorates";
            if (!_cache.TryGetValue(cacheKey, out List<Directorate>? directorates))
            {
                directorates = await _context.Directorates
                    .Include(d => d.Agency)
                    .OrderBy(d => d.Name)
                    .ToListAsync();
                
                _cache.Set(cacheKey, directorates, _cacheExpiration);
            }
            return directorates ?? new List<Directorate>();
        }

        public async Task<List<GovernmentDepartment>> GetGovernmentDepartmentsAsync()
        {
            const string cacheKey = "government_departments";
            if (!_cache.TryGetValue(cacheKey, out List<GovernmentDepartment>? departments))
            {
                departments = await _context.GovernmentDepartments
                    .Include(gd => gd.Directorate)
                    .OrderBy(gd => gd.Name)
                    .ToListAsync();
                
                _cache.Set(cacheKey, departments, _cacheExpiration);
            }
            return departments ?? new List<GovernmentDepartment>();
        }

        public async Task<List<Division>> GetDivisionsAsync()
        {
            const string cacheKey = "divisions";
            if (!_cache.TryGetValue(cacheKey, out List<Division>? divisions))
            {
                divisions = await _context.Divisions
                    .Include(d => d.GovernmentDepartment)
                    .OrderBy(d => d.Name)
                    .ToListAsync();
                
                _cache.Set(cacheKey, divisions, _cacheExpiration);
            }
            return divisions ?? new List<Division>();
        }

        public void ClearCache()
        {
            var cacheKeys = new[]
            {
                "ranks", "provinces", "universities", "institutes", "colleges",
                "ministries", "agencies", "directorates", "government_departments", "divisions"
            };

            foreach (var key in cacheKeys)
            {
                _cache.Remove(key);
            }
        }

        public void ClearCache(string key)
        {
            _cache.Remove(key);
        }
    }
}
