class AdvancedSearch {
    constructor() {
        this.searchCache = new Map();
        this.searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
        this.maxHistoryItems = 10;
        this.debounceTimer = null;
        this.currentRequest = null;
        this.filters = {};
        this.sortOptions = {
            field: 'CreatedDate',
            direction: 'desc'
        };
    }

    // تهيئة نظام البحث
    init(searchInputId, resultsContainerId, options = {}) {
        this.searchInput = document.getElementById(searchInputId);
        this.resultsContainer = document.getElementById(resultsContainerId);
        this.options = {
            minLength: 2,
            debounceDelay: 300,
            maxResults: 50,
            enableHistory: true,
            enableFilters: true,
            enableSort: true,
            ...options
        };

        this.createSearchInterface();
        this.attachEventListeners();
        this.loadSearchHistory();
    }

    // إنشاء واجهة البحث
    createSearchInterface() {
        const searchContainer = this.searchInput.parentElement;
        searchContainer.classList.add('advanced-search-container');

        // إنشاء قائمة الاقتراحات
        const suggestionsContainer = document.createElement('div');
        suggestionsContainer.className = 'search-suggestions';
        suggestionsContainer.id = 'searchSuggestions';
        searchContainer.appendChild(suggestionsContainer);

        // إنشاء منطقة الفلاتر
        if (this.options.enableFilters) {
            this.createFiltersPanel();
        }

        // إنشاء منطقة النتائج
        this.createResultsPanel();

        // إضافة الأنماط
        this.addStyles();
    }

    // إنشاء لوحة الفلاتر
    createFiltersPanel() {
        const filtersHtml = `
            <div class="search-filters" id="searchFilters">
                <div class="filters-header">
                    <h6>
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="advancedSearch.clearFilters()">
                        <i class="fas fa-times me-1"></i>
                        مسح الفلاتر
                    </button>
                </div>
                <div class="filters-content">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label">الرتبة</label>
                            <select class="form-select" id="filterRank" onchange="advancedSearch.updateFilter('rank', this.value)">
                                <option value="">جميع الرتب</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">المحافظة</label>
                            <select class="form-select" id="filterProvince" onchange="advancedSearch.updateFilter('province', this.value)">
                                <option value="">جميع المحافظات</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">التحصيل الدراسي</label>
                            <select class="form-select" id="filterEducation" onchange="advancedSearch.updateFilter('education', this.value)">
                                <option value="">جميع المستويات</option>
                                <option value="Primary">ابتدائية</option>
                                <option value="Intermediate">متوسطة</option>
                                <option value="Secondary">إعدادية</option>
                                <option value="Diploma">دبلوم</option>
                                <option value="Bachelor">بكالوريوس</option>
                                <option value="Master">ماجستير</option>
                                <option value="PhD">دكتوراه</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">الجنس</label>
                            <select class="form-select" id="filterGender" onchange="advancedSearch.updateFilter('gender', this.value)">
                                <option value="">الكل</option>
                                <option value="Male">ذكر</option>
                                <option value="Female">أنثى</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ الإضافة من</label>
                            <input type="date" class="form-control" id="filterDateFrom" onchange="advancedSearch.updateFilter('dateFrom', this.value)">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ الإضافة إلى</label>
                            <input type="date" class="form-control" id="filterDateTo" onchange="advancedSearch.updateFilter('dateTo', this.value)">
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.searchInput.parentElement.insertAdjacentHTML('afterend', filtersHtml);
        this.loadFilterOptions();
    }

    // إنشاء لوحة النتائج
    createResultsPanel() {
        const resultsHtml = `
            <div class="search-results-panel" id="searchResultsPanel">
                <div class="results-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="results-title">
                            <i class="fas fa-search me-2"></i>
                            نتائج البحث (<span id="resultsCount">0</span>)
                        </h6>
                        <div class="results-actions">
                            <select class="form-select form-select-sm me-2" id="sortOptions" onchange="advancedSearch.updateSort(this.value)">
                                <option value="CreatedDate_desc">الأحدث أولاً</option>
                                <option value="CreatedDate_asc">الأقدم أولاً</option>
                                <option value="FullName_asc">الاسم (أ-ي)</option>
                                <option value="FullName_desc">الاسم (ي-أ)</option>
                                <option value="EmployeeNumber_asc">رقم الموظف</option>
                            </select>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="advancedSearch.exportResults()">
                                <i class="fas fa-download me-1"></i>
                                تصدير
                            </button>
                        </div>
                    </div>
                </div>
                <div class="results-content" id="searchResultsContent">
                    <!-- النتائج ستظهر هنا -->
                </div>
                <div class="results-pagination" id="resultsPagination">
                    <!-- الترقيم سيظهر هنا -->
                </div>
            </div>
        `;

        this.resultsContainer.innerHTML = resultsHtml;
    }

    // إضافة مستمعي الأحداث
    attachEventListeners() {
        // البحث أثناء الكتابة
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        // إظهار/إخفاء الاقتراحات
        this.searchInput.addEventListener('focus', () => {
            this.showSuggestions();
        });

        this.searchInput.addEventListener('blur', (e) => {
            // تأخير لإتاحة النقر على الاقتراحات
            setTimeout(() => {
                this.hideSuggestions();
            }, 200);
        });

        // التنقل بالكيبورد
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeyNavigation(e);
        });

        // إغلاق الاقتراحات عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.advanced-search-container')) {
                this.hideSuggestions();
            }
        });
    }

    // معالجة إدخال البحث
    handleSearchInput(query) {
        clearTimeout(this.debounceTimer);
        
        if (query.length < this.options.minLength) {
            this.hideSuggestions();
            this.clearResults();
            return;
        }

        this.debounceTimer = setTimeout(() => {
            this.performSearch(query);
        }, this.options.debounceDelay);
    }

    // تنفيذ البحث
    async performSearch(query, page = 1) {
        try {
            // إلغاء الطلب السابق
            if (this.currentRequest) {
                this.currentRequest.abort();
            }

            // إظهار مؤشر التحميل
            this.showLoading();

            // التحقق من الكاش
            const cacheKey = this.getCacheKey(query, page);
            if (this.searchCache.has(cacheKey)) {
                const cachedResults = this.searchCache.get(cacheKey);
                this.displayResults(cachedResults);
                this.hideLoading();
                return;
            }

            // إنشاء طلب جديد
            const controller = new AbortController();
            this.currentRequest = controller;

            const searchParams = new URLSearchParams({
                query: query,
                page: page,
                pageSize: this.options.maxResults,
                sortField: this.sortOptions.field,
                sortDirection: this.sortOptions.direction,
                ...this.filters
            });

            const response = await fetch(`/Employee/Search?${searchParams}`, {
                signal: controller.signal
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const results = await response.json();

            // حفظ في الكاش
            this.searchCache.set(cacheKey, results);

            // عرض النتائج
            this.displayResults(results);

            // حفظ في التاريخ
            if (this.options.enableHistory) {
                this.addToHistory(query);
            }

            // إظهار الاقتراحات
            this.updateSuggestions(query, results.suggestions || []);

        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('خطأ في البحث:', error);
                this.showError('حدث خطأ أثناء البحث');
            }
        } finally {
            this.hideLoading();
            this.currentRequest = null;
        }
    }

    // عرض النتائج
    displayResults(results) {
        const resultsContent = document.getElementById('searchResultsContent');
        const resultsCount = document.getElementById('resultsCount');

        resultsCount.textContent = results.totalCount || 0;

        if (!results.data || results.data.length === 0) {
            resultsContent.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد نتائج</h5>
                    <p class="text-muted">جرب تعديل كلمات البحث أو الفلاتر</p>
                </div>
            `;
            return;
        }

        const resultsHtml = results.data.map(employee => `
            <div class="search-result-item" onclick="advancedSearch.viewEmployee(${employee.id})">
                <div class="result-avatar">
                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="fas fa-user text-muted"></i>
                    </div>
                </div>
                <div class="result-content">
                    <h6 class="result-name">${employee.fullName}</h6>
                    <div class="result-details">
                        <span class="badge bg-primary me-2">${employee.employeeNumber}</span>
                        <span class="badge bg-success me-2">${employee.rank}</span>
                        <span class="text-muted">${employee.province}</span>
                    </div>
                    <small class="result-meta">
                        ${employee.education} • ${employee.createdDate}
                    </small>
                </div>
                <div class="result-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); advancedSearch.editEmployee(${employee.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </div>
        `).join('');

        resultsContent.innerHTML = resultsHtml;

        // إنشاء الترقيم
        this.createPagination(results.currentPage, results.totalPages);
    }

    // إنشاء الترقيم
    createPagination(currentPage, totalPages) {
        const paginationContainer = document.getElementById('resultsPagination');
        
        if (totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let paginationHtml = '<nav><ul class="pagination justify-content-center">';

        // زر السابق
        if (currentPage > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="advancedSearch.goToPage(${currentPage - 1})">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;
        }

        // أرقام الصفحات
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="advancedSearch.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        // زر التالي
        if (currentPage < totalPages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="advancedSearch.goToPage(${currentPage + 1})">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;
        }

        paginationHtml += '</ul></nav>';
        paginationContainer.innerHTML = paginationHtml;
    }

    // تحديث الاقتراحات
    updateSuggestions(query, suggestions) {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        
        // إضافة التاريخ
        const historySuggestions = this.searchHistory
            .filter(item => item.toLowerCase().includes(query.toLowerCase()))
            .slice(0, 3);

        const allSuggestions = [...new Set([...historySuggestions, ...suggestions])].slice(0, 8);

        if (allSuggestions.length === 0) {
            suggestionsContainer.innerHTML = '';
            return;
        }

        const suggestionsHtml = allSuggestions.map(suggestion => `
            <div class="suggestion-item" onclick="advancedSearch.selectSuggestion('${suggestion}')">
                <i class="fas fa-search me-2"></i>
                ${suggestion}
            </div>
        `).join('');

        suggestionsContainer.innerHTML = suggestionsHtml;
        suggestionsContainer.style.display = 'block';
    }

    // اختيار اقتراح
    selectSuggestion(suggestion) {
        this.searchInput.value = suggestion;
        this.hideSuggestions();
        this.performSearch(suggestion);
    }

    // إظهار/إخفاء الاقتراحات
    showSuggestions() {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (suggestionsContainer.innerHTML.trim()) {
            suggestionsContainer.style.display = 'block';
        }
    }

    hideSuggestions() {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        suggestionsContainer.style.display = 'none';
    }

    // تحديث الفلتر
    updateFilter(filterName, value) {
        if (value) {
            this.filters[filterName] = value;
        } else {
            delete this.filters[filterName];
        }

        // إعادة البحث
        const query = this.searchInput.value;
        if (query.length >= this.options.minLength) {
            this.performSearch(query);
        }
    }

    // مسح الفلاتر
    clearFilters() {
        this.filters = {};
        
        // مسح قيم الفلاتر
        document.getElementById('filterRank').value = '';
        document.getElementById('filterProvince').value = '';
        document.getElementById('filterEducation').value = '';
        document.getElementById('filterGender').value = '';
        document.getElementById('filterDateFrom').value = '';
        document.getElementById('filterDateTo').value = '';

        // إعادة البحث
        const query = this.searchInput.value;
        if (query.length >= this.options.minLength) {
            this.performSearch(query);
        }
    }

    // تحديث الترتيب
    updateSort(sortValue) {
        const [field, direction] = sortValue.split('_');
        this.sortOptions = { field, direction };

        // إعادة البحث
        const query = this.searchInput.value;
        if (query.length >= this.options.minLength) {
            this.performSearch(query);
        }
    }

    // الانتقال لصفحة
    goToPage(page) {
        const query = this.searchInput.value;
        this.performSearch(query, page);
    }

    // عرض موظف
    viewEmployee(employeeId) {
        window.location.href = `/Employee/Details/${employeeId}`;
    }

    // تعديل موظف
    editEmployee(employeeId) {
        window.location.href = `/Employee/Edit/${employeeId}`;
    }

    // تصدير النتائج
    exportResults() {
        showInfo('قريباً', 'ميزة تصدير نتائج البحث ستكون متاحة قريباً');
    }

    // إضافة للتاريخ
    addToHistory(query) {
        if (!this.searchHistory.includes(query)) {
            this.searchHistory.unshift(query);
            this.searchHistory = this.searchHistory.slice(0, this.maxHistoryItems);
            localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
        }
    }

    // تحميل تاريخ البحث
    loadSearchHistory() {
        // يمكن إضافة واجهة لعرض التاريخ
    }

    // تحميل خيارات الفلاتر
    async loadFilterOptions() {
        try {
            // تحميل الرتب
            const ranksResponse = await fetch('/Constants/GetRanks');
            const ranks = await ranksResponse.json();
            const rankSelect = document.getElementById('filterRank');
            ranks.forEach(rank => {
                const option = document.createElement('option');
                option.value = rank.id;
                option.textContent = rank.name;
                rankSelect.appendChild(option);
            });

            // تحميل المحافظات
            const provincesResponse = await fetch('/Constants/GetProvinces');
            const provinces = await provincesResponse.json();
            const provinceSelect = document.getElementById('filterProvince');
            provinces.forEach(province => {
                const option = document.createElement('option');
                option.value = province.id;
                option.textContent = province.name;
                provinceSelect.appendChild(option);
            });

        } catch (error) {
            console.error('خطأ في تحميل خيارات الفلاتر:', error);
        }
    }

    // دوال مساعدة
    getCacheKey(query, page) {
        return `${query}_${page}_${JSON.stringify(this.filters)}_${this.sortOptions.field}_${this.sortOptions.direction}`;
    }

    showLoading() {
        const resultsContent = document.getElementById('searchResultsContent');
        resultsContent.innerHTML = `
            <div class="search-loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري البحث...</span>
                </div>
                <p class="mt-2">جاري البحث...</p>
            </div>
        `;
    }

    hideLoading() {
        // سيتم استبدال المحتوى بالنتائج
    }

    clearResults() {
        const resultsContent = document.getElementById('searchResultsContent');
        const resultsCount = document.getElementById('resultsCount');
        resultsContent.innerHTML = '';
        resultsCount.textContent = '0';
    }

    showError(message) {
        const resultsContent = document.getElementById('searchResultsContent');
        resultsContent.innerHTML = `
            <div class="search-error">
                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                <h5 class="text-danger">خطأ في البحث</h5>
                <p class="text-muted">${message}</p>
            </div>
        `;
    }

    // إضافة الأنماط
    addStyles() {
        if (document.getElementById('advancedSearchStyles')) return;

        const styles = document.createElement('style');
        styles.id = 'advancedSearchStyles';
        styles.textContent = `
            .advanced-search-container {
                position: relative;
            }

            .search-suggestions {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 0 0 10px 10px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                display: none;
                max-height: 300px;
                overflow-y: auto;
            }

            .suggestion-item {
                padding: 12px 15px;
                cursor: pointer;
                border-bottom: 1px solid #f8f9fa;
                transition: background-color 0.2s ease;
            }

            .suggestion-item:hover {
                background-color: #f8f9fa;
            }

            .suggestion-item:last-child {
                border-bottom: none;
            }

            .search-filters {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 15px;
                padding: 20px;
                margin: 20px 0;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            }

            .filters-header {
                display: flex;
                justify-content: between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 15px;
                border-bottom: 1px solid #e9ecef;
            }

            .filters-header h6 {
                margin: 0;
                color: #2c3e50;
                font-weight: 700;
            }

            .search-results-panel {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 15px;
                margin-top: 20px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            }

            .results-header {
                padding: 20px;
                border-bottom: 1px solid #e9ecef;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-radius: 15px 15px 0 0;
            }

            .results-title {
                margin: 0;
                color: #2c3e50;
                font-weight: 700;
            }

            .results-content {
                padding: 20px;
                max-height: 600px;
                overflow-y: auto;
            }

            .search-result-item {
                display: flex;
                align-items: center;
                padding: 15px;
                border: 1px solid #e9ecef;
                border-radius: 10px;
                margin-bottom: 15px;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .search-result-item:hover {
                border-color: #667eea;
                box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
                transform: translateY(-2px);
            }

            .result-avatar img {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                object-fit: cover;
                margin-left: 15px;
            }

            .result-content {
                flex-grow: 1;
            }

            .result-name {
                margin: 0 0 8px 0;
                color: #2c3e50;
                font-weight: 600;
            }

            .result-details {
                margin-bottom: 5px;
            }

            .result-meta {
                color: #6c757d;
            }

            .result-actions {
                margin-right: 15px;
            }

            .no-results, .search-loading, .search-error {
                text-align: center;
                padding: 60px 20px;
            }

            .results-pagination {
                padding: 20px;
                border-top: 1px solid #e9ecef;
            }

            @media (max-width: 768px) {
                .search-filters .row > div {
                    margin-bottom: 15px;
                }
                
                .results-header .d-flex {
                    flex-direction: column;
                    gap: 15px;
                }
                
                .search-result-item {
                    flex-direction: column;
                    text-align: center;
                }
                
                .result-avatar {
                    margin-bottom: 15px;
                }
            }
        `;

        document.head.appendChild(styles);
    }
}

// إنشاء مثيل عام
const advancedSearch = new AdvancedSearch();
