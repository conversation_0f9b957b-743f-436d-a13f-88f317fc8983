@{
    var hasCurrentPhoto = ViewBag.HasCurrentPhoto as bool? ?? false;
    var currentPhotoBase64 = ViewBag.CurrentPhotoBase64 as string;
}

<div class="photo-upload-container">
    <div class="row justify-content-center">
        <!-- الصورة في الوسط -->
        <div class="col-md-8 col-lg-6">
            <div class="photo-section-centered">
                <h6 class="text-center mb-4">
                    <i class="fas fa-camera me-2"></i>
                    الصورة الشخصية
                </h6>

                <div class="photo-main-container">
                    <div id="photoPreview" class="photo-preview-main" onclick="showPhotoOptions()">
                        @if (hasCurrentPhoto && !string.IsNullOrEmpty(currentPhotoBase64))
                        {
                            <img id="previewImage" src="data:image/jpeg;base64,@currentPhotoBase64" alt="صورة الموظف" class="preview-img-main" />
                            <div class="photo-overlay-main">
                                <div class="overlay-content">
                                    <i class="fas fa-edit fa-2x"></i>
                                    <p class="mt-2 mb-0">اضغط لتغيير الصورة</p>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-danger photo-remove-btn" onclick="removePhoto(event)">
                                <i class="fas fa-trash"></i>
                            </button>
                        }
                        else
                        {
                            <div class="no-photo-placeholder-main">
                                <i class="fas fa-camera fa-4x text-primary mb-3"></i>
                                <h5 class="text-primary mb-2">إضافة صورة</h5>
                                <p class="text-muted mb-0">اضغط هنا لاختيار صورة أو التقاط صورة جديدة</p>
                            </div>
                        }
                    </div>

                    <!-- قائمة خيارات الصورة -->
                    <div id="photoOptionsMenu" class="photo-options-menu" style="display: none;">
                        <div class="option-item" onclick="selectFromFile()">
                            <i class="fas fa-folder-open"></i>
                            <span>اختيار من ملف</span>
                        </div>
                        <div class="option-item" onclick="openCamera()">
                            <i class="fas fa-camera"></i>
                            <span>التقاط صورة</span>
                        </div>
                        <div class="option-item cancel" onclick="hidePhotoOptions()">
                            <i class="fas fa-times"></i>
                            <span>إلغاء</span>
                        </div>
                    </div>
                </div>

                <!-- حقل رفع الملف المخفي -->
                <input type="file" id="photoFileInput" name="PhotoFile" accept="image/*" style="display: none;" onchange="handleFileSelect(this)" />

                <!-- حقل البيانات المخفي -->
                <input type="hidden" id="photoBase64Input" name="PhotoBase64" value="" />
                <input type="hidden" id="photoContentTypeInput" name="PhotoContentType" value="" />
                <input type="hidden" id="photoFileNameInput" name="PhotoFileName" value="" />

                <!-- معلومات الصورة -->
                <div id="photoInfo" class="photo-info mt-3" style="display: none;">
                    <div class="alert alert-info text-center">
                        <small>
                            <strong>اسم الملف:</strong> <span id="photoInfoName"></span><br>
                            <strong>الحجم:</strong> <span id="photoInfoSize"></span><br>
                            <strong>النوع:</strong> <span id="photoInfoType"></span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم الكاميرا (منبثق) -->
    <div id="cameraModal" class="camera-modal" style="display: none;">
        <div class="camera-modal-content">
            <div class="camera-modal-header">
                <h5>التقاط صورة</h5>
                <button type="button" class="btn-close" onclick="closeCamera()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="camera-modal-body">
                <div class="camera-container">
                    <video id="cameraVideo" autoplay playsinline></video>
                    <canvas id="cameraCanvas" style="display: none;"></canvas>
                </div>
            </div>
            <div class="camera-modal-footer">
                <button type="button" class="btn btn-success me-2" onclick="capturePhoto()">
                    <i class="fas fa-camera me-2"></i>
                    التقاط
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeCamera()">
                    <i class="fas fa-times me-2"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.photo-upload-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid #dee2e6;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.photo-section-centered {
    text-align: center;
}

.photo-main-container {
    position: relative;
    display: inline-block;
}

.photo-preview-main {
    width: 250px;
    height: 250px;
    border: 3px dashed #007bff;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

.photo-preview-main:hover {
    border-color: #0056b3;
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 123, 255, 0.25);
}

.preview-img-main {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 17px;
}

.no-photo-placeholder-main {
    text-align: center;
    padding: 20px;
}

.photo-overlay-main {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 17px;
}

.photo-preview-main:hover .photo-overlay-main {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: white;
}

.photo-remove-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.photo-preview-main:hover .photo-remove-btn {
    opacity: 1;
}

/* قائمة خيارات الصورة */
.photo-options-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid #dee2e6;
    z-index: 1000;
    min-width: 200px;
    margin-top: 10px;
    overflow: hidden;
}

.option-item {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border-bottom: 1px solid #f1f3f4;
}

.option-item:last-child {
    border-bottom: none;
}

.option-item:hover {
    background-color: #f8f9fa;
}

.option-item.cancel:hover {
    background-color: #fee;
    color: #dc3545;
}

.option-item i {
    margin-left: 12px;
    width: 20px;
    text-align: center;
    color: #007bff;
}

.option-item.cancel i {
    color: #dc3545;
}

/* الكاميرا المنبثقة */
.camera-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: flex-start;
    justify-content: center;
    z-index: 2000;
    padding-top: 20px;
}

.camera-modal-content {
    background: white;
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 85vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    margin-top: 0;
    transition: all 0.3s ease-out;
}

.camera-modal-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.camera-modal-header h5 {
    margin: 0;
    color: white;
    font-weight: 600;
}

.btn-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: white;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.camera-modal-body {
    padding: 20px;
    text-align: center;
}

.camera-container {
    background: #000;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#cameraVideo {
    max-width: 100%;
    max-height: 400px;
    border-radius: 10px;
}

.camera-modal-footer {
    padding: 20px;
    border-top: 1px solid #dee2e6;
    text-align: center;
    background: #f8f9fa;
    border-radius: 0 0 15px 15px;
}

.camera-modal-footer .btn {
    border-radius: 25px;
    padding: 10px 20px;
    font-weight: 600;
    min-width: 120px;
}

.photo-info {
    border-radius: 10px;
}

@@media (max-width: 768px) {
    .photo-preview-main {
        width: 200px;
        height: 200px;
    }

    .photo-upload-container {
        padding: 20px;
    }

    .camera-modal {
        padding-top: 10px;
    }

    .camera-modal-content {
        width: 95%;
        max-height: 90vh;
        margin: 0 10px;
    }

    .camera-modal-body {
        padding: 15px;
    }

    .camera-container {
        min-height: 250px;
    }

    #cameraVideo {
        max-height: 300px;
    }

    .photo-options-menu {
        min-width: 180px;
    }

    .option-item {
        padding: 12px 15px;
    }
}

@@media (max-width: 480px) {
    .camera-modal {
        padding-top: 5px;
    }

    .camera-modal-content {
        width: 98%;
        max-height: 95vh;
        margin: 0 5px;
        border-radius: 10px;
    }

    .camera-modal-header {
        padding: 15px;
    }

    .camera-modal-body {
        padding: 10px;
    }

    .camera-modal-footer {
        padding: 15px;
    }

    .camera-container {
        min-height: 200px;
    }

    #cameraVideo {
        max-height: 250px;
    }
}
</style>

<script>
let cameraStream = null;

// إظهار قائمة خيارات الصورة
function showPhotoOptions() {
    const menu = document.getElementById('photoOptionsMenu');
    menu.style.display = 'block';

    // إخفاء القائمة عند الضغط خارجها
    setTimeout(() => {
        document.addEventListener('click', hidePhotoOptionsOnClickOutside);
    }, 100);
}

// إخفاء قائمة خيارات الصورة
function hidePhotoOptions() {
    const menu = document.getElementById('photoOptionsMenu');
    menu.style.display = 'none';
    document.removeEventListener('click', hidePhotoOptionsOnClickOutside);
}

// إخفاء القائمة عند الضغط خارجها
function hidePhotoOptionsOnClickOutside(event) {
    const menu = document.getElementById('photoOptionsMenu');
    const container = document.getElementById('photoPreview');

    if (!menu.contains(event.target) && !container.contains(event.target)) {
        hidePhotoOptions();
    }
}

// اختيار ملف من الجهاز
function selectFromFile() {
    hidePhotoOptions();
    document.getElementById('photoFileInput').click();
}

// معالجة اختيار الملف
function handleFileSelect(input) {
    const file = input.files[0];
    if (file) {
        // التحقق من نوع الملف
        if (!file.type.startsWith('image/')) {
            alert('يرجى اختيار ملف صورة صحيح');
            return;
        }

        // التحقق من حجم الملف (5MB كحد أقصى)
        if (file.size > 5 * 1024 * 1024) {
            alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            displayPhoto(e.target.result, file.type, file.name, file.size);
        };
        reader.readAsDataURL(file);
    }
}

// فتح الكاميرا
async function openCamera() {
    hidePhotoOptions();

    try {
        const constraints = {
            video: {
                width: { ideal: 640 },
                height: { ideal: 480 },
                facingMode: 'user'
            }
        };

        cameraStream = await navigator.mediaDevices.getUserMedia(constraints);
        const video = document.getElementById('cameraVideo');
        video.srcObject = cameraStream;

        document.getElementById('cameraModal').style.display = 'flex';
    } catch (error) {
        console.error('خطأ في فتح الكاميرا:', error);
        alert('لا يمكن الوصول إلى الكاميرا. تأكد من منح الإذن للموقع.');
    }
}

// التقاط صورة من الكاميرا
function capturePhoto() {
    const video = document.getElementById('cameraVideo');
    const canvas = document.getElementById('cameraCanvas');
    const context = canvas.getContext('2d');

    // تعيين أبعاد الكانفاس
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // رسم الصورة على الكانفاس
    context.drawImage(video, 0, 0);

    // تحويل إلى base64
    const dataURL = canvas.toDataURL('image/jpeg', 0.8);
    
    // عرض الصورة
    displayPhoto(dataURL, 'image/jpeg', 'camera_capture.jpg', null);
    
    // إغلاق الكاميرا
    closeCamera();
}

// إغلاق الكاميرا
function closeCamera() {
    if (cameraStream) {
        cameraStream.getTracks().forEach(track => track.stop());
        cameraStream = null;
    }
    document.getElementById('cameraModal').style.display = 'none';
}

// عرض الصورة
function displayPhoto(dataURL, contentType, fileName, fileSize) {
    // عرض الصورة في المعاينة
    const preview = document.getElementById('photoPreview');
    preview.innerHTML = `
        <img id="previewImage" src="${dataURL}" alt="صورة الموظف" class="preview-img-main" />
        <div class="photo-overlay-main">
            <div class="overlay-content">
                <i class="fas fa-edit fa-2x"></i>
                <p class="mt-2 mb-0">اضغط لتغيير الصورة</p>
            </div>
        </div>
        <button type="button" class="btn btn-sm btn-danger photo-remove-btn" onclick="removePhoto(event)">
            <i class="fas fa-trash"></i>
        </button>
    `;

    // حفظ البيانات في الحقول المخفية
    const base64Data = dataURL.split(',')[1];
    document.getElementById('photoBase64Input').value = base64Data;
    document.getElementById('photoContentTypeInput').value = contentType;
    document.getElementById('photoFileNameInput').value = fileName;

    // عرض معلومات الصورة
    document.getElementById('photoInfoName').textContent = fileName;
    document.getElementById('photoInfoType').textContent = contentType;
    
    if (fileSize) {
        document.getElementById('photoInfoSize').textContent = formatFileSize(fileSize);
    } else {
        document.getElementById('photoInfoSize').textContent = 'غير محدد';
    }
    
    document.getElementById('photoInfo').style.display = 'block';
}

// حذف الصورة
function removePhoto(event) {
    if (event) {
        event.stopPropagation(); // منع تشغيل حدث الضغط على الصورة
    }

    document.getElementById('photoPreview').innerHTML = `
        <div class="no-photo-placeholder-main">
            <i class="fas fa-camera fa-4x text-primary mb-3"></i>
            <h5 class="text-primary mb-2">إضافة صورة</h5>
            <p class="text-muted mb-0">اضغط هنا لاختيار صورة أو التقاط صورة جديدة</p>
        </div>
    `;

    // مسح البيانات
    document.getElementById('photoBase64Input').value = '';
    document.getElementById('photoContentTypeInput').value = '';
    document.getElementById('photoFileNameInput').value = '';
    document.getElementById('photoFileInput').value = '';

    document.getElementById('photoInfo').style.display = 'none';
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// تنظيف عند مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    closeCamera();
});
</script>
