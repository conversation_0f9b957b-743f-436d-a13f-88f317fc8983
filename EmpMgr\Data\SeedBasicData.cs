using Microsoft.EntityFrameworkCore;
using EmpMgr.Models;

namespace EmpMgr.Data
{
    public static class SeedBasicData
    {
        public static async Task SeedCollegesAndDepartmentsAsync(ApplicationDbContext context)
        {
            // إضافة كليات أساسية لجامعة بغداد (ID = 1)
            if (!context.Colleges.Any())
            {
                var colleges = new List<College>
                {
                    new College { Name = "كلية الطب", UniversityId = 1, Type = CollegeType.Medical, IsActive = true, CreatedDate = DateTime.Now },
                    new College { Name = "كلية الهندسة", UniversityId = 1, Type = CollegeType.Engineering, IsActive = true, CreatedDate = DateTime.Now },
                    new College { Name = "كلية العلوم", UniversityId = 1, Type = CollegeType.Scientific, IsActive = true, CreatedDate = DateTime.Now },
                    new College { Name = "كلية الآداب", UniversityId = 1, Type = CollegeType.Humanities, IsActive = true, CreatedDate = DateTime.Now },
                    new College { Name = "كلية القانون", UniversityId = 1, Type = CollegeType.Legal, IsActive = true, CreatedDate = DateTime.Now },
                    new College { Name = "كلية الإدارة والاقتصاد", UniversityId = 1, Type = CollegeType.Administrative, IsActive = true, CreatedDate = DateTime.Now },
                    new College { Name = "كلية التربية", UniversityId = 1, Type = CollegeType.Educational, IsActive = true, CreatedDate = DateTime.Now },
                    new College { Name = "كلية الصيدلة", UniversityId = 1, Type = CollegeType.Medical, IsActive = true, CreatedDate = DateTime.Now },
                    new College { Name = "كلية طب الأسنان", UniversityId = 1, Type = CollegeType.Medical, IsActive = true, CreatedDate = DateTime.Now },
                    new College { Name = "كلية الزراعة", UniversityId = 1, Type = CollegeType.Scientific, IsActive = true, CreatedDate = DateTime.Now }
                };

                context.Colleges.AddRange(colleges);
                await context.SaveChangesAsync();
            }

            // إضافة أقسام أساسية للمعهد التقني الإداري (ID = 1)
            if (!context.Departments.Any())
            {
                var departments = new List<Department>
                {
                    new Department { Name = "قسم تقنيات المحاسبة", InstituteId = 1, Type = DepartmentType.Accounting, IsActive = true, CreatedDate = DateTime.Now },
                    new Department { Name = "قسم تقنيات الإدارة", InstituteId = 1, Type = DepartmentType.Administrative, IsActive = true, CreatedDate = DateTime.Now },
                    new Department { Name = "قسم تقنيات الحاسوب", InstituteId = 1, Type = DepartmentType.Computer, IsActive = true, CreatedDate = DateTime.Now },
                    new Department { Name = "قسم التقنيات القانونية", InstituteId = 1, Type = DepartmentType.Legal, IsActive = true, CreatedDate = DateTime.Now },
                    
                    // أقسام للمعهد التقني الطبي (ID = 2)
                    new Department { Name = "قسم تقنيات التمريض", InstituteId = 2, Type = DepartmentType.Medical, IsActive = true, CreatedDate = DateTime.Now },
                    new Department { Name = "قسم تقنيات المختبرات الطبية", InstituteId = 2, Type = DepartmentType.Medical, IsActive = true, CreatedDate = DateTime.Now },
                    new Department { Name = "قسم تقنيات الأشعة", InstituteId = 2, Type = DepartmentType.Medical, IsActive = true, CreatedDate = DateTime.Now },
                    
                    // أقسام للمعهد التقني الهندسي (ID = 3)
                    new Department { Name = "قسم تقنيات الهندسة المدنية", InstituteId = 3, Type = DepartmentType.Engineering, IsActive = true, CreatedDate = DateTime.Now },
                    new Department { Name = "قسم تقنيات الهندسة الكهربائية", InstituteId = 3, Type = DepartmentType.Engineering, IsActive = true, CreatedDate = DateTime.Now },
                    new Department { Name = "قسم تقنيات الهندسة الميكانيكية", InstituteId = 3, Type = DepartmentType.Engineering, IsActive = true, CreatedDate = DateTime.Now }
                };

                context.Departments.AddRange(departments);
                await context.SaveChangesAsync();
            }


        }
    }
}
