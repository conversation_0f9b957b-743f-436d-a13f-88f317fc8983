-- إضافة أقسام أكاديمية أساسية للكليات

-- التحقق من وجود كليات أولاً
IF EXISTS (SELECT 1 FROM Colleges WHERE Id = 1)
BEGIN
    -- إضافة أقسام أكاديمية لكلية الطب (ID = 1)
    IF NOT EXISTS (SELECT 1 FROM AcademicDepartments WHERE CollegeId = 1)
    BEGIN
        INSERT INTO AcademicDepartments (Name, Code, CollegeId, Type, IsActive, CreatedDate) VALUES
        ('قسم الطب الباطني', 'MED-INT', 1, 3, 1, GETDATE()),
        ('قسم الجراحة العامة', 'MED-SUR', 1, 3, 1, GETDATE()),
        ('قسم طب الأطفال', 'MED-PED', 1, 3, 1, GETDATE()),
        ('قسم النساء والتوليد', 'MED-OBS', 1, 3, 1, GETDATE()),
        ('قسم الأشعة', 'MED-RAD', 1, 3, 1, GETDATE()),
        ('قسم التخدير', 'MED-ANE', 1, 3, 1, GETDATE()),
        ('قسم المختبرات الطبية', 'MED-LAB', 1, 3, 1, GETDATE());
    END
END

-- إضافة أقسام أكاديمية لكلية الهندسة (ID = 2)
IF EXISTS (SELECT 1 FROM Colleges WHERE Id = 2)
BEGIN
    IF NOT EXISTS (SELECT 1 FROM AcademicDepartments WHERE CollegeId = 2)
    BEGIN
        INSERT INTO AcademicDepartments (Name, Code, CollegeId, Type, IsActive, CreatedDate) VALUES
        ('قسم الهندسة المدنية', 'ENG-CIV', 2, 4, 1, GETDATE()),
        ('قسم الهندسة الكهربائية', 'ENG-ELE', 2, 4, 1, GETDATE()),
        ('قسم الهندسة الميكانيكية', 'ENG-MEC', 2, 4, 1, GETDATE()),
        ('قسم هندسة الحاسوب', 'ENG-COM', 2, 4, 1, GETDATE()),
        ('قسم الهندسة الكيميائية', 'ENG-CHE', 2, 4, 1, GETDATE()),
        ('قسم الهندسة المعمارية', 'ENG-ARC', 2, 4, 1, GETDATE());
    END
END

-- إضافة أقسام أكاديمية لكلية العلوم (ID = 3)
IF EXISTS (SELECT 1 FROM Colleges WHERE Id = 3)
BEGIN
    IF NOT EXISTS (SELECT 1 FROM AcademicDepartments WHERE CollegeId = 3)
    BEGIN
        INSERT INTO AcademicDepartments (Name, Code, CollegeId, Type, IsActive, CreatedDate) VALUES
        ('قسم الرياضيات', 'SCI-MAT', 3, 10, 1, GETDATE()),
        ('قسم الفيزياء', 'SCI-PHY', 3, 11, 1, GETDATE()),
        ('قسم الكيمياء', 'SCI-CHE', 3, 12, 1, GETDATE()),
        ('قسم علوم الحياة', 'SCI-BIO', 3, 13, 1, GETDATE()),
        ('قسم علوم الحاسوب', 'SCI-CS', 3, 14, 1, GETDATE()),
        ('قسم الإحصاء', 'SCI-STA', 3, 10, 1, GETDATE());
    END
END

-- إضافة أقسام أكاديمية لكلية الآداب (ID = 4)
IF EXISTS (SELECT 1 FROM Colleges WHERE Id = 4)
BEGIN
    IF NOT EXISTS (SELECT 1 FROM AcademicDepartments WHERE CollegeId = 4)
    BEGIN
        INSERT INTO AcademicDepartments (Name, Code, CollegeId, Type, IsActive, CreatedDate) VALUES
        ('قسم اللغة العربية', 'ART-ARA', 4, 2, 1, GETDATE()),
        ('قسم اللغة الإنجليزية', 'ART-ENG', 4, 2, 1, GETDATE()),
        ('قسم التاريخ', 'ART-HIS', 4, 2, 1, GETDATE()),
        ('قسم الجغرافية', 'ART-GEO', 4, 2, 1, GETDATE()),
        ('قسم الفلسفة', 'ART-PHI', 4, 2, 1, GETDATE()),
        ('قسم علم النفس', 'ART-PSY', 4, 2, 1, GETDATE());
    END
END

-- إضافة أقسام أكاديمية لكلية القانون (ID = 5)
IF EXISTS (SELECT 1 FROM Colleges WHERE Id = 5)
BEGIN
    IF NOT EXISTS (SELECT 1 FROM AcademicDepartments WHERE CollegeId = 5)
    BEGIN
        INSERT INTO AcademicDepartments (Name, Code, CollegeId, Type, IsActive, CreatedDate) VALUES
        ('قسم القانون العام', 'LAW-PUB', 5, 5, 1, GETDATE()),
        ('قسم القانون الخاص', 'LAW-PRI', 5, 5, 1, GETDATE()),
        ('قسم القانون الجنائي', 'LAW-CRI', 5, 5, 1, GETDATE()),
        ('قسم القانون التجاري', 'LAW-COM', 5, 5, 1, GETDATE()),
        ('قسم القانون الدولي', 'LAW-INT', 5, 5, 1, GETDATE());
    END
END

-- إضافة أقسام أكاديمية لكلية الاقتصاد والإدارة (ID = 6)
IF EXISTS (SELECT 1 FROM Colleges WHERE Id = 6)
BEGIN
    IF NOT EXISTS (SELECT 1 FROM AcademicDepartments WHERE CollegeId = 6)
    BEGIN
        INSERT INTO AcademicDepartments (Name, Code, CollegeId, Type, IsActive, CreatedDate) VALUES
        ('قسم الاقتصاد', 'ECO-ECO', 6, 6, 1, GETDATE()),
        ('قسم إدارة الأعمال', 'ECO-BUS', 6, 6, 1, GETDATE()),
        ('قسم المحاسبة', 'ECO-ACC', 6, 6, 1, GETDATE()),
        ('قسم التمويل والمصارف', 'ECO-FIN', 6, 6, 1, GETDATE()),
        ('قسم التسويق', 'ECO-MAR', 6, 6, 1, GETDATE()),
        ('قسم نظم المعلومات الإدارية', 'ECO-MIS', 6, 6, 1, GETDATE());
    END
END

-- إضافة أقسام أكاديمية لكلية التربية (ID = 7)
IF EXISTS (SELECT 1 FROM Colleges WHERE Id = 7)
BEGIN
    IF NOT EXISTS (SELECT 1 FROM AcademicDepartments WHERE CollegeId = 7)
    BEGIN
        INSERT INTO AcademicDepartments (Name, Code, CollegeId, Type, IsActive, CreatedDate) VALUES
        ('قسم التربية وعلم النفس', 'EDU-PSY', 7, 7, 1, GETDATE()),
        ('قسم المناهج وطرق التدريس', 'EDU-CUR', 7, 7, 1, GETDATE()),
        ('قسم الإدارة التربوية', 'EDU-ADM', 7, 7, 1, GETDATE()),
        ('قسم التربية الخاصة', 'EDU-SPE', 7, 7, 1, GETDATE()),
        ('قسم تكنولوجيا التعليم', 'EDU-TEC', 7, 7, 1, GETDATE());
    END
END

PRINT 'تم إضافة الأقسام الأكاديمية بنجاح'
