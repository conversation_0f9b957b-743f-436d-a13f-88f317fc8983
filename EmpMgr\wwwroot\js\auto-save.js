class AutoSave {
    constructor() {
        this.saveInterval = 30000; // 30 ثانية
        this.formData = {};
        this.intervalId = null;
        this.isEnabled = true;
    }

    // بدء الحفظ التلقائي
    start(formSelector = 'form') {
        if (!this.isEnabled) return;

        this.intervalId = setInterval(() => {
            this.saveFormData(formSelector);
        }, this.saveInterval);

        // حفظ عند تغيير أي حقل
        this.attachEventListeners(formSelector);
        
        // استرجاع البيانات المحفوظة عند التحميل
        this.loadSavedData(formSelector);
    }

    // إيقاف الحفظ التلقائي
    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    // حفظ بيانات النموذج
    saveFormData(formSelector) {
        const form = document.querySelector(formSelector);
        if (!form) return;

        const formData = new FormData(form);
        const data = {};

        // حفظ جميع الحقول
        for (let [key, value] of formData.entries()) {
            if (key !== '__RequestVerificationToken' && key !== 'PhotoFile') {
                data[key] = value;
            }
        }

        // حفظ الصورة من localStorage إذا كانت موجودة
        const tempPhoto = localStorage.getItem('temp_employee_photo');
        if (tempPhoto) {
            data.tempPhoto = tempPhoto;
        }

        // حفظ في localStorage
        localStorage.setItem('employee_form_autosave', JSON.stringify({
            data: data,
            timestamp: Date.now(),
            url: window.location.pathname
        }));

        this.showAutoSaveIndicator();
    }

    // تحميل البيانات المحفوظة
    loadSavedData(formSelector) {
        const savedData = localStorage.getItem('employee_form_autosave');
        if (!savedData) return;

        try {
            const parsed = JSON.parse(savedData);
            
            // التحقق من أن البيانات من نفس الصفحة وحديثة (أقل من ساعة)
            const isRecent = (Date.now() - parsed.timestamp) < 3600000; // ساعة واحدة
            const isSamePage = parsed.url === window.location.pathname;
            
            if (!isRecent || !isSamePage) {
                localStorage.removeItem('employee_form_autosave');
                return;
            }

            // عرض رسالة للمستخدم
            this.showRestorePrompt(parsed.data, formSelector);
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات المحفوظة:', error);
            localStorage.removeItem('employee_form_autosave');
        }
    }

    // عرض رسالة استرجاع البيانات
    showRestorePrompt(data, formSelector) {
        const promptHtml = `
            <div class="alert alert-info alert-dismissible fade show auto-save-prompt" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle me-3 fs-4"></i>
                    <div class="flex-grow-1">
                        <h6 class="alert-heading mb-1">بيانات محفوظة تلقائياً</h6>
                        <p class="mb-2">تم العثور على بيانات محفوظة تلقائياً من جلسة سابقة. هل تريد استرجاعها؟</p>
                        <div>
                            <button type="button" class="btn btn-primary btn-sm me-2" onclick="autoSave.restoreData('${formSelector}')">
                                <i class="fas fa-undo me-1"></i>
                                استرجاع البيانات
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="autoSave.discardSavedData()">
                                <i class="fas fa-trash me-1"></i>
                                تجاهل
                            </button>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        `;

        const container = document.querySelector('.container-fluid') || document.body;
        container.insertAdjacentHTML('afterbegin', promptHtml);
    }

    // استرجاع البيانات
    restoreData(formSelector) {
        const savedData = localStorage.getItem('employee_form_autosave');
        if (!savedData) return;

        try {
            const parsed = JSON.parse(savedData);
            const form = document.querySelector(formSelector);
            
            if (!form) return;

            // ملء الحقول
            Object.entries(parsed.data).forEach(([key, value]) => {
                if (key === 'tempPhoto') {
                    // استرجاع الصورة
                    const photoPreview = document.getElementById('photoPreview');
                    if (photoPreview && value) {
                        photoPreview.src = value;
                        if (typeof showClearButton === 'function') {
                            showClearButton();
                        }
                    }
                } else {
                    const field = form.querySelector(`[name="${key}"]`);
                    if (field) {
                        if (field.type === 'checkbox') {
                            field.checked = value === 'true';
                        } else if (field.type === 'radio') {
                            if (field.value === value) {
                                field.checked = true;
                            }
                        } else {
                            field.value = value;
                        }

                        // إطلاق حدث التغيير للحقول التي تحتاج تحديث
                        if (field.id === 'EducationLevel') {
                            field.dispatchEvent(new Event('change'));
                        }
                    }
                }
            });

            // تحديث العنوان الكامل
            if (typeof updateFullAddress === 'function') {
                updateFullAddress();
            }

            // إزالة الرسالة
            document.querySelector('.auto-save-prompt')?.remove();
            
            this.showSuccessMessage('تم استرجاع البيانات المحفوظة بنجاح');
            
        } catch (error) {
            console.error('خطأ في استرجاع البيانات:', error);
            this.showErrorMessage('حدث خطأ أثناء استرجاع البيانات');
        }
    }

    // تجاهل البيانات المحفوظة
    discardSavedData() {
        localStorage.removeItem('employee_form_autosave');
        localStorage.removeItem('temp_employee_photo');
        document.querySelector('.auto-save-prompt')?.remove();
        this.showSuccessMessage('تم تجاهل البيانات المحفوظة');
    }

    // إضافة مستمعي الأحداث
    attachEventListeners(formSelector) {
        const form = document.querySelector(formSelector);
        if (!form) return;

        // حفظ عند تغيير أي حقل
        form.addEventListener('input', () => {
            clearTimeout(this.saveTimeout);
            this.saveTimeout = setTimeout(() => {
                this.saveFormData(formSelector);
            }, 2000); // حفظ بعد ثانيتين من التوقف عن الكتابة
        });

        // حفظ عند تغيير القوائم المنسدلة
        form.addEventListener('change', () => {
            this.saveFormData(formSelector);
        });

        // حذف البيانات المحفوظة عند الإرسال الناجح
        form.addEventListener('submit', () => {
            setTimeout(() => {
                this.discardSavedData();
            }, 1000);
        });
    }

    // إظهار مؤشر الحفظ التلقائي
    showAutoSaveIndicator() {
        // إزالة المؤشر السابق إذا كان موجوداً
        document.querySelector('.auto-save-indicator')?.remove();

        const indicator = document.createElement('div');
        indicator.className = 'auto-save-indicator';
        indicator.innerHTML = `
            <i class="fas fa-check-circle text-success me-1"></i>
            <small class="text-muted">تم الحفظ تلقائياً</small>
        `;
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1050;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        document.body.appendChild(indicator);
        
        // إظهار المؤشر
        setTimeout(() => indicator.style.opacity = '1', 100);
        
        // إخفاء المؤشر بعد 3 ثوان
        setTimeout(() => {
            indicator.style.opacity = '0';
            setTimeout(() => indicator.remove(), 300);
        }, 3000);
    }

    // رسائل النجاح والخطأ
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    showErrorMessage(message) {
        this.showMessage(message, 'danger');
    }

    showMessage(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check' : 'exclamation'}-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        const container = document.querySelector('.container-fluid') || document.body;
        container.insertAdjacentHTML('afterbegin', alertHtml);

        // إزالة الرسالة بعد 5 ثوان
        setTimeout(() => {
            document.querySelector(`.alert-${type}`)?.remove();
        }, 5000);
    }

    // تمكين/تعطيل الحفظ التلقائي
    enable() {
        this.isEnabled = true;
    }

    disable() {
        this.isEnabled = false;
        this.stop();
    }
}

// إنشاء مثيل عام للحفظ التلقائي
const autoSave = new AutoSave();
