<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- الدرع الخارجي -->
  <path d="M50 15 L25 28 L25 55 Q25 72 50 85 Q75 72 75 55 L75 28 Z"
        fill="url(#shieldGradient)"
        stroke="#667eea"
        stroke-width="2"
        filter="url(#glow)"/>

  <!-- النجمة الداخلية -->
  <path d="M50 30 L53 40 L63 40 L55 47 L58 57 L50 50 L42 57 L45 47 L37 40 L47 40 Z"
        fill="#667eea"
        opacity="0.8"/>

  <!-- دائرة داخلية -->
  <circle cx="50" cy="45" r="8" fill="none" stroke="#764ba2" stroke-width="1.5" opacity="0.6"/>

  <!-- النص العربي -->
  <text x="50" y="75" text-anchor="middle" fill="#667eea" font-family="Arial" font-size="6" font-weight="bold">
    وزارة الداخلية
  </text>
</svg>
