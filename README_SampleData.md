# إدارة البيانات التجريبية

## حالة النظام الحالية
تم تكوين النظام ليعمل **بدون بيانات افتراضية**. قاعدة البيانات فارغة ويمكنك إضافة البيانات حسب احتياجاتك.

## إضافة البيانات التجريبية (اختياري)

إذا كنت تريد إضافة بيانات تجريبية للاختبار، يمكنك تعديل ملف `Program.cs`:

### الخطوة 1: تعديل Program.cs
ابحث عن السطر التالي في ملف `Program.cs`:
```csharp
await SeedData.SeedAsync(context, seedSampleData: false);
```

وغيره إلى:
```csharp
await SeedData.SeedAsync(context, seedSampleData: true);
```

### الخطوة 2: إعادة تشغيل التطبيق
أوقف التطبيق وأعد تشغيله باستخدام:
```bash
dotnet run
```

## البيانات التجريبية المتضمنة

عند تفعيل البيانات التجريبية، سيتم إنشاء:

### 🏛️ الوزارات
- وزارة الداخلية
- وزارة الدفاع  
- وزارة التربية

### 🏢 الوكالات (تحت وزارة الداخلية)
- وكالة الوزارة لشؤون الشرطة
- وكالة الوزارة للشؤون الإدارية
- وكالة الوزارة للشؤون الأمنية

### 🏬 المديريات
- مديرية شرطة بغداد
- مديرية شرطة البصرة
- مديرية الموارد البشرية
- مديرية الشؤون المالية

### 📋 الأقسام الحكومية
- قسم الدوريات
- قسم المرور
- قسم التوظيف
- قسم التدريب

### 📝 الشعب
- شعبة الدوريات النهارية
- شعبة الدوريات الليلية
- شعبة فحص الطلبات
- شعبة المقابلات

### 🎖️ الرتب العسكرية
- فريق أول، فريق، عميد، عقيد، مقدم، رائد، نقيب، ملازم أول، ملازم، مرشح ضابط

### 🏛️ الجامعات والمعاهد
- جامعة بغداد، جامعة البصرة، الجامعة المستنصرية، وغيرها
- معاهد تقنية ومهنية متنوعة

### 🗺️ المحافظات
- جميع محافظات العراق الـ18

## حذف البيانات التجريبية

لحذف البيانات التجريبية والعودة لقاعدة بيانات فارغة:

### الطريقة 1: من خلال SQL
```sql
DELETE FROM Divisions;
DELETE FROM GovernmentDepartments;
DELETE FROM Directorates;
DELETE FROM Agencies;
DELETE FROM Ministries;
DELETE FROM Departments;
DELETE FROM Colleges;
DELETE FROM Institutes;
DELETE FROM Universities;
DELETE FROM Provinces;
DELETE FROM Ranks;
```

### الطريقة 2: تعديل الكود
1. غير `seedSampleData: true` إلى `seedSampleData: false` في `Program.cs`
2. احذف البيانات يدوياً من قاعدة البيانات
3. أعد تشغيل التطبيق

## ملاحظات مهمة

- ⚠️ **تحذير**: حذف البيانات سيؤثر على جميع الموظفين المرتبطين بهذه الوحدات
- 🔄 **الترابط الهرمي**: البيانات مترابطة هرمياً (وزارة → وكالة → مديرية → قسم → شعبة)
- 📊 **البيانات الحقيقية**: يُنصح بإدخال البيانات الحقيقية لمؤسستك بدلاً من البيانات التجريبية
- 🔒 **النسخ الاحتياطي**: تأكد من عمل نسخة احتياطية قبل حذف أي بيانات

## الهيكل الهرمي للوحدات الإدارية

```
وزارة
├── وكالة
    ├── مديرية
        ├── قسم حكومي
            ├── شعبة
```

هذا الهيكل يضمن التنظيم الصحيح للوحدات الإدارية ويسمح بإدارة الموظفين على جميع المستويات.
