﻿// <auto-generated />
using System;
using EmpMgr.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace EmpMgr.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250725205305_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("EmpMgr.Models.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("FullName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("EmpMgr.Models.AuditLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<string>("NewValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OldValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecordId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("EmpMgr.Models.Employee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Alley")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("BloodType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("District")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("EducationLevel")
                        .HasColumnType("int");

                    b.Property<string>("EmployeeNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("FatherName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("Gender")
                        .HasColumnType("int");

                    b.Property<string>("GrandFatherName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("GreatGrandFatherName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("HealthStatus")
                        .HasColumnType("int");

                    b.Property<string>("House")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("InstituteId")
                        .HasColumnType("int");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("MaritalStatus")
                        .HasColumnType("int");

                    b.Property<string>("NearestLandmark")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Neighborhood")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PhotoPath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("ProvinceId")
                        .HasColumnType("int");

                    b.Property<string>("Quarter")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("RankId")
                        .HasColumnType("int");

                    b.Property<string>("StatisticalNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Subdistrict")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("UniversityId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Village")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeNumber")
                        .IsUnique();

                    b.HasIndex("InstituteId");

                    b.HasIndex("ProvinceId");

                    b.HasIndex("RankId");

                    b.HasIndex("StatisticalNumber")
                        .IsUnique();

                    b.HasIndex("UniversityId");

                    b.HasIndex("FirstName", "FatherName", "GrandFatherName", "GreatGrandFatherName", "LastName")
                        .IsUnique();

                    b.ToTable("Employees");
                });

            modelBuilder.Entity("EmpMgr.Models.Institute", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Institutes");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            IsActive = true,
                            Location = "بغداد",
                            Name = "المعهد التقني الطبي",
                            Type = 3
                        },
                        new
                        {
                            Id = 2,
                            IsActive = true,
                            Location = "بغداد",
                            Name = "المعهد التقني الإداري",
                            Type = 3
                        },
                        new
                        {
                            Id = 3,
                            IsActive = true,
                            Location = "بغداد",
                            Name = "معهد الإدارة",
                            Type = 1
                        },
                        new
                        {
                            Id = 4,
                            IsActive = true,
                            Location = "البصرة",
                            Name = "المعهد التقني البصرة",
                            Type = 3
                        });
                });

            modelBuilder.Entity("EmpMgr.Models.Province", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Provinces");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "BGD",
                            IsActive = true,
                            Name = "بغداد"
                        },
                        new
                        {
                            Id = 2,
                            Code = "BSR",
                            IsActive = true,
                            Name = "البصرة"
                        },
                        new
                        {
                            Id = 3,
                            Code = "NIN",
                            IsActive = true,
                            Name = "نينوى"
                        },
                        new
                        {
                            Id = 4,
                            Code = "ERB",
                            IsActive = true,
                            Name = "أربيل"
                        },
                        new
                        {
                            Id = 5,
                            Code = "NAJ",
                            IsActive = true,
                            Name = "النجف"
                        },
                        new
                        {
                            Id = 6,
                            Code = "KRB",
                            IsActive = true,
                            Name = "كربلاء"
                        },
                        new
                        {
                            Id = 7,
                            Code = "BAB",
                            IsActive = true,
                            Name = "بابل"
                        },
                        new
                        {
                            Id = 8,
                            Code = "ANB",
                            IsActive = true,
                            Name = "الأنبار"
                        },
                        new
                        {
                            Id = 9,
                            Code = "DHQ",
                            IsActive = true,
                            Name = "ذي قار"
                        },
                        new
                        {
                            Id = 10,
                            Code = "QAD",
                            IsActive = true,
                            Name = "القادسية"
                        },
                        new
                        {
                            Id = 11,
                            Code = "KRK",
                            IsActive = true,
                            Name = "كركوك"
                        },
                        new
                        {
                            Id = 12,
                            Code = "DYL",
                            IsActive = true,
                            Name = "ديالى"
                        },
                        new
                        {
                            Id = 13,
                            Code = "MTH",
                            IsActive = true,
                            Name = "المثنى"
                        },
                        new
                        {
                            Id = 14,
                            Code = "WST",
                            IsActive = true,
                            Name = "واسط"
                        },
                        new
                        {
                            Id = 15,
                            Code = "SLD",
                            IsActive = true,
                            Name = "صلاح الدين"
                        },
                        new
                        {
                            Id = 16,
                            Code = "DHK",
                            IsActive = true,
                            Name = "دهوك"
                        },
                        new
                        {
                            Id = 17,
                            Code = "SLM",
                            IsActive = true,
                            Name = "السليمانية"
                        },
                        new
                        {
                            Id = 18,
                            Code = "MYS",
                            IsActive = true,
                            Name = "ميسان"
                        });
                });

            modelBuilder.Entity("EmpMgr.Models.Rank", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Ranks");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            IsActive = true,
                            Name = "فريق أول",
                            Order = 1
                        },
                        new
                        {
                            Id = 2,
                            IsActive = true,
                            Name = "فريق",
                            Order = 2
                        },
                        new
                        {
                            Id = 3,
                            IsActive = true,
                            Name = "عميد",
                            Order = 3
                        },
                        new
                        {
                            Id = 4,
                            IsActive = true,
                            Name = "عقيد",
                            Order = 4
                        },
                        new
                        {
                            Id = 5,
                            IsActive = true,
                            Name = "مقدم",
                            Order = 5
                        },
                        new
                        {
                            Id = 6,
                            IsActive = true,
                            Name = "رائد",
                            Order = 6
                        },
                        new
                        {
                            Id = 7,
                            IsActive = true,
                            Name = "نقيب",
                            Order = 7
                        },
                        new
                        {
                            Id = 8,
                            IsActive = true,
                            Name = "ملازم أول",
                            Order = 8
                        },
                        new
                        {
                            Id = 9,
                            IsActive = true,
                            Name = "ملازم",
                            Order = 9
                        },
                        new
                        {
                            Id = 10,
                            IsActive = true,
                            Name = "رئيس رقباء",
                            Order = 10
                        },
                        new
                        {
                            Id = 11,
                            IsActive = true,
                            Name = "رقيب أول",
                            Order = 11
                        },
                        new
                        {
                            Id = 12,
                            IsActive = true,
                            Name = "رقيب",
                            Order = 12
                        },
                        new
                        {
                            Id = 13,
                            IsActive = true,
                            Name = "عريف أول",
                            Order = 13
                        },
                        new
                        {
                            Id = 14,
                            IsActive = true,
                            Name = "عريف",
                            Order = 14
                        },
                        new
                        {
                            Id = 15,
                            IsActive = true,
                            Name = "جندي أول",
                            Order = 15
                        },
                        new
                        {
                            Id = 16,
                            IsActive = true,
                            Name = "جندي",
                            Order = 16
                        });
                });

            modelBuilder.Entity("EmpMgr.Models.SystemSettings", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.HasKey("Id");

                    b.HasIndex("Key")
                        .IsUnique();

                    b.ToTable("SystemSettings");
                });

            modelBuilder.Entity("EmpMgr.Models.University", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Universities");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            IsActive = true,
                            Location = "بغداد",
                            Name = "جامعة بغداد",
                            Type = 1
                        },
                        new
                        {
                            Id = 2,
                            IsActive = true,
                            Location = "بغداد",
                            Name = "الجامعة المستنصرية",
                            Type = 1
                        },
                        new
                        {
                            Id = 3,
                            IsActive = true,
                            Location = "البصرة",
                            Name = "جامعة البصرة",
                            Type = 1
                        },
                        new
                        {
                            Id = 4,
                            IsActive = true,
                            Location = "نينوى",
                            Name = "جامعة الموصل",
                            Type = 1
                        },
                        new
                        {
                            Id = 5,
                            IsActive = true,
                            Location = "النجف",
                            Name = "جامعة الكوفة",
                            Type = 1
                        },
                        new
                        {
                            Id = 6,
                            IsActive = true,
                            Location = "كربلاء",
                            Name = "جامعة كربلاء",
                            Type = 1
                        },
                        new
                        {
                            Id = 7,
                            IsActive = true,
                            Location = "بابل",
                            Name = "جامعة بابل",
                            Type = 1
                        });
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("EmpMgr.Models.Employee", b =>
                {
                    b.HasOne("EmpMgr.Models.Institute", "Institute")
                        .WithMany("Employees")
                        .HasForeignKey("InstituteId");

                    b.HasOne("EmpMgr.Models.Province", "Province")
                        .WithMany("Employees")
                        .HasForeignKey("ProvinceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmpMgr.Models.Rank", "Rank")
                        .WithMany("Employees")
                        .HasForeignKey("RankId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmpMgr.Models.University", "University")
                        .WithMany("Employees")
                        .HasForeignKey("UniversityId");

                    b.Navigation("Institute");

                    b.Navigation("Province");

                    b.Navigation("Rank");

                    b.Navigation("University");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("EmpMgr.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("EmpMgr.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmpMgr.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("EmpMgr.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("EmpMgr.Models.Institute", b =>
                {
                    b.Navigation("Employees");
                });

            modelBuilder.Entity("EmpMgr.Models.Province", b =>
                {
                    b.Navigation("Employees");
                });

            modelBuilder.Entity("EmpMgr.Models.Rank", b =>
                {
                    b.Navigation("Employees");
                });

            modelBuilder.Entity("EmpMgr.Models.University", b =>
                {
                    b.Navigation("Employees");
                });
#pragma warning restore 612, 618
        }
    }
}
