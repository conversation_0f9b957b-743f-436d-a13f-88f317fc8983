@{
    ViewData["Title"] = "البحث المتقدم";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="search-page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="search-title">
                            <i class="fas fa-search me-3"></i>
                            البحث المتقدم
                        </h2>
                        <p class="search-subtitle">ابحث في قاعدة بيانات الموظفين باستخدام فلاتر متقدمة</p>
                    </div>
                    <div class="search-actions">
                        <button class="btn btn-outline-primary me-2" onclick="toggleFilters()">
                            <i class="fas fa-filter me-2"></i>
                            <span id="filterToggleText">إظهار الفلاتر</span>
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-list me-2"></i>
                            قائمة الموظفين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- شريط البحث الرئيسي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="main-search-container">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" 
                           id="mainSearchInput" 
                           class="form-control search-input" 
                           placeholder="ابحث بالاسم، رقم الموظف، الرقم الإحصائي، الرتبة، أو المحافظة..."
                           autocomplete="off">
                    <button type="button" class="btn btn-clear" id="clearSearchBtn" onclick="clearSearch()" style="display: none;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="search-suggestions" id="searchSuggestions"></div>
            </div>
        </div>
    </div>

    <!-- منطقة الفلاتر -->
    <div class="row" id="filtersSection" style="display: none;">
        <div class="col-12">
            <!-- سيتم إنشاؤها بواسطة JavaScript -->
        </div>
    </div>

    <!-- منطقة النتائج -->
    <div class="row">
        <div class="col-12">
            <div id="searchResultsContainer">
                <!-- البحث الافتراضي -->
                <div class="search-welcome">
                    <div class="welcome-content">
                        <i class="fas fa-search fa-4x text-primary mb-4"></i>
                        <h4>مرحباً بك في البحث المتقدم</h4>
                        <p class="text-muted">ابدأ بكتابة كلمة البحث أو استخدم الفلاتر للعثور على الموظفين</p>
                        
                        <div class="search-tips mt-4">
                            <h6>نصائح للبحث:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-lightbulb text-warning me-2"></i>يمكنك البحث بالاسم الكامل أو جزء منه</li>
                                <li><i class="fas fa-lightbulb text-warning me-2"></i>استخدم رقم الموظف للبحث السريع</li>
                                <li><i class="fas fa-lightbulb text-warning me-2"></i>الفلاتر تساعد في تضييق نطاق البحث</li>
                                <li><i class="fas fa-lightbulb text-warning me-2"></i>يمكنك ترتيب النتائج حسب التاريخ أو الاسم</li>
                            </ul>
                        </div>

                        <div class="quick-actions mt-4">
                            <h6>إجراءات سريعة:</h6>
                            <div class="d-flex flex-wrap gap-2 justify-content-center">
                                <button class="btn btn-outline-primary btn-sm" onclick="quickSearch('ضابط')">
                                    <i class="fas fa-user-tie me-1"></i>
                                    الضباط
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="quickSearch('منتسب')">
                                    <i class="fas fa-user-friends me-1"></i>
                                    المنتسبين
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="quickSearch('بكالوريوس')">
                                    <i class="fas fa-graduation-cap me-1"></i>
                                    حملة البكالوريوس
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="showRecentEmployees()">
                                    <i class="fas fa-clock me-1"></i>
                                    المضافين حديثاً
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- تضمين ملفات البحث المتقدم -->
    <script src="~/js/advanced-search.js"></script>
    <script src="~/js/performance-optimizer.js"></script>
    <script src="~/js/notification-system.js"></script>
    
    <script>
        // متغيرات عامة
        let filtersVisible = false;

        // تهيئة البحث المتقدم
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة نظام البحث
            advancedSearch.init('mainSearchInput', 'searchResultsContainer', {
                minLength: 1,
                debounceDelay: 500,
                maxResults: 20,
                enableHistory: true,
                enableFilters: true,
                enableSort: true
            });

            // إضافة مستمعي الأحداث
            setupEventListeners();
            
            // تحميل البيانات الأولية
            loadInitialData();
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            const searchInput = document.getElementById('mainSearchInput');
            
            // إظهار/إخفاء زر المسح
            searchInput.addEventListener('input', function() {
                const clearBtn = document.getElementById('clearSearchBtn');
                if (this.value.length > 0) {
                    clearBtn.style.display = 'block';
                } else {
                    clearBtn.style.display = 'none';
                }
            });

            // البحث عند الضغط على Enter
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    advancedSearch.performSearch(this.value);
                }
            });
        }

        // تحميل البيانات الأولية
        function loadInitialData() {
            // يمكن تحميل إحصائيات سريعة أو موظفين حديثين
        }

        // تبديل عرض الفلاتر
        function toggleFilters() {
            const filtersSection = document.getElementById('filtersSection');
            const toggleText = document.getElementById('filterToggleText');
            
            if (filtersVisible) {
                filtersSection.style.display = 'none';
                toggleText.textContent = 'إظهار الفلاتر';
                filtersVisible = false;
            } else {
                filtersSection.style.display = 'block';
                toggleText.textContent = 'إخفاء الفلاتر';
                filtersVisible = true;
            }
        }

        // مسح البحث
        function clearSearch() {
            const searchInput = document.getElementById('mainSearchInput');
            const clearBtn = document.getElementById('clearSearchBtn');
            
            searchInput.value = '';
            clearBtn.style.display = 'none';
            advancedSearch.clearResults();
            
            // إظهار الشاشة الترحيبية
            showWelcomeScreen();
        }

        // بحث سريع
        function quickSearch(term) {
            const searchInput = document.getElementById('mainSearchInput');
            searchInput.value = term;
            advancedSearch.performSearch(term);
        }

        // إظهار الموظفين الحديثين
        function showRecentEmployees() {
            // تطبيق فلتر التاريخ للشهر الحالي
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            
            advancedSearch.updateFilter('dateFrom', firstDay.toISOString().split('T')[0]);
            advancedSearch.performSearch('');
        }

        // إظهار الشاشة الترحيبية
        function showWelcomeScreen() {
            const container = document.getElementById('searchResultsContainer');
            container.innerHTML = `
                <div class="search-welcome">
                    <div class="welcome-content">
                        <i class="fas fa-search fa-4x text-primary mb-4"></i>
                        <h4>مرحباً بك في البحث المتقدم</h4>
                        <p class="text-muted">ابدأ بكتابة كلمة البحث أو استخدم الفلاتر للعثور على الموظفين</p>
                    </div>
                </div>
            `;
        }

        // تصدير النتائج
        function exportSearchResults() {
            showInfo('قريباً', 'ميزة تصدير نتائج البحث ستكون متاحة قريباً');
        }

        // حفظ البحث
        function saveSearch() {
            const query = document.getElementById('mainSearchInput').value;
            if (query.trim()) {
                const savedSearches = JSON.parse(localStorage.getItem('savedSearches') || '[]');
                const searchData = {
                    query: query,
                    filters: advancedSearch.filters,
                    date: new Date().toISOString(),
                    name: prompt('أدخل اسم للبحث المحفوظ:') || `بحث ${savedSearches.length + 1}`
                };
                
                savedSearches.push(searchData);
                localStorage.setItem('savedSearches', JSON.stringify(savedSearches));
                
                showSuccess('تم الحفظ', 'تم حفظ البحث بنجاح');
            }
        }

        // تحميل بحث محفوظ
        function loadSavedSearch() {
            const savedSearches = JSON.parse(localStorage.getItem('savedSearches') || '[]');
            if (savedSearches.length === 0) {
                showInfo('لا توجد عمليات بحث محفوظة', 'لم تقم بحفظ أي عمليات بحث بعد');
                return;
            }

            // يمكن إضافة modal لعرض البحوثات المحفوظة
            showInfo('قريباً', 'ميزة البحوثات المحفوظة ستكون متاحة قريباً');
        }
    </script>
}

@section Styles {
    <style>
        .search-page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .search-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
        }

        .search-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .main-search-container {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-icon {
            position: absolute;
            right: 20px;
            color: #6c757d;
            z-index: 10;
        }

        .search-input {
            padding: 20px 60px 20px 60px;
            font-size: 18px;
            border: 2px solid #e9ecef;
            border-radius: 50px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .btn-clear {
            position: absolute;
            left: 15px;
            background: none;
            border: none;
            color: #6c757d;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .btn-clear:hover {
            background: #f8f9fa;
            color: #dc3545;
        }

        .search-welcome {
            text-align: center;
            padding: 80px 20px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .welcome-content h4 {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .search-tips {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            text-align: right;
            max-width: 500px;
            margin: 0 auto;
        }

        .search-tips h6 {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .search-tips ul li {
            padding: 5px 0;
            color: #6c757d;
        }

        .quick-actions {
            max-width: 600px;
            margin: 0 auto;
        }

        .quick-actions h6 {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .search-actions .btn {
            border-radius: 25px;
            font-weight: 600;
            padding: 10px 20px;
        }

        @@media (max-width: 768px) {
            .search-title {
                font-size: 2rem;
            }

            .search-actions {
                margin-top: 20px;
            }

            .search-input {
                padding: 15px 50px 15px 50px;
                font-size: 16px;
            }

            .search-welcome {
                padding: 40px 15px;
            }

            .quick-actions .btn {
                margin-bottom: 10px;
                width: 100%;
            }
        }
    </style>
}
