using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace EmpMgr.Services
{
    public interface ISecurityService
    {
        string EncryptData(string data);
        string DecryptData(string encryptedData);
        string HashPassword(string password);
        bool VerifyPassword(string password, string hash);
        string GenerateSecureToken();
        bool ValidateToken(string token);
        string SanitizeInput(string input);
        bool IsValidFileType(IFormFile file, string[] allowedTypes);
        string GenerateCSRFToken();
        bool ValidateCSRFToken(string token);
    }

    public class SecurityService : ISecurityService
    {
        private readonly IConfiguration _configuration;
        private readonly string _encryptionKey;
        private readonly string _hashSalt;

        public SecurityService(IConfiguration configuration)
        {
            _configuration = configuration;
            _encryptionKey = _configuration["Security:EncryptionKey"] ?? GenerateRandomKey();
            _hashSalt = _configuration["Security:HashSalt"] ?? GenerateRandomKey();
        }

        // تشفير البيانات
        public string EncryptData(string data)
        {
            if (string.IsNullOrEmpty(data))
                return string.Empty;

            try
            {
                using var aes = Aes.Create();
                aes.Key = Encoding.UTF8.GetBytes(_encryptionKey.PadRight(32).Substring(0, 32));
                aes.IV = new byte[16]; // استخدام IV ثابت للبساطة (يفضل استخدام IV عشوائي في الإنتاج)

                using var encryptor = aes.CreateEncryptor();
                using var msEncrypt = new MemoryStream();
                using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
                using var swEncrypt = new StreamWriter(csEncrypt);

                swEncrypt.Write(data);
                swEncrypt.Close();

                return Convert.ToBase64String(msEncrypt.ToArray());
            }
            catch (Exception ex)
            {
                throw new SecurityException("خطأ في تشفير البيانات", ex);
            }
        }

        // فك تشفير البيانات
        public string DecryptData(string encryptedData)
        {
            if (string.IsNullOrEmpty(encryptedData))
                return string.Empty;

            try
            {
                using var aes = Aes.Create();
                aes.Key = Encoding.UTF8.GetBytes(_encryptionKey.PadRight(32).Substring(0, 32));
                aes.IV = new byte[16];

                using var decryptor = aes.CreateDecryptor();
                using var msDecrypt = new MemoryStream(Convert.FromBase64String(encryptedData));
                using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using var srDecrypt = new StreamReader(csDecrypt);

                return srDecrypt.ReadToEnd();
            }
            catch (Exception ex)
            {
                throw new SecurityException("خطأ في فك تشفير البيانات", ex);
            }
        }

        // تشفير كلمة المرور
        public string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                throw new ArgumentException("كلمة المرور لا يمكن أن تكون فارغة");

            using var sha256 = SHA256.Create();
            var saltedPassword = password + _hashSalt;
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
            return Convert.ToBase64String(hashedBytes);
        }

        // التحقق من كلمة المرور
        public bool VerifyPassword(string password, string hash)
        {
            if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hash))
                return false;

            try
            {
                var hashedPassword = HashPassword(password);
                return hashedPassword == hash;
            }
            catch
            {
                return false;
            }
        }

        // إنشاء رمز أمان
        public string GenerateSecureToken()
        {
            using var rng = RandomNumberGenerator.Create();
            var tokenBytes = new byte[32];
            rng.GetBytes(tokenBytes);
            return Convert.ToBase64String(tokenBytes);
        }

        // التحقق من صحة الرمز
        public bool ValidateToken(string token)
        {
            if (string.IsNullOrEmpty(token))
                return false;

            try
            {
                var tokenBytes = Convert.FromBase64String(token);
                return tokenBytes.Length == 32;
            }
            catch
            {
                return false;
            }
        }

        // تنظيف المدخلات
        public string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // إزالة الأحرف الخطيرة
            var dangerous = new[] { "<", ">", "\"", "'", "&", "javascript:", "vbscript:", "onload=", "onerror=" };
            var sanitized = input;

            foreach (var danger in dangerous)
            {
                sanitized = sanitized.Replace(danger, "", StringComparison.OrdinalIgnoreCase);
            }

            // تحديد الطول الأقصى
            if (sanitized.Length > 1000)
            {
                sanitized = sanitized.Substring(0, 1000);
            }

            return sanitized.Trim();
        }

        // التحقق من نوع الملف
        public bool IsValidFileType(IFormFile file, string[] allowedTypes)
        {
            if (file == null || allowedTypes == null || allowedTypes.Length == 0)
                return false;

            // التحقق من امتداد الملف
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!allowedTypes.Contains(extension))
                return false;

            // التحقق من نوع المحتوى
            var allowedMimeTypes = new Dictionary<string, string[]>
            {
                { ".jpg", new[] { "image/jpeg", "image/jpg" } },
                { ".jpeg", new[] { "image/jpeg", "image/jpg" } },
                { ".png", new[] { "image/png" } },
                { ".gif", new[] { "image/gif" } },
                { ".pdf", new[] { "application/pdf" } },
                { ".doc", new[] { "application/msword" } },
                { ".docx", new[] { "application/vnd.openxmlformats-officedocument.wordprocessingml.document" } },
                { ".xls", new[] { "application/vnd.ms-excel" } },
                { ".xlsx", new[] { "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" } }
            };

            if (allowedMimeTypes.ContainsKey(extension))
            {
                return allowedMimeTypes[extension].Contains(file.ContentType.ToLowerInvariant());
            }

            return false;
        }

        // إنشاء رمز CSRF
        public string GenerateCSRFToken()
        {
            using var rng = RandomNumberGenerator.Create();
            var tokenBytes = new byte[24];
            rng.GetBytes(tokenBytes);
            return Convert.ToBase64String(tokenBytes);
        }

        // التحقق من رمز CSRF
        public bool ValidateCSRFToken(string token)
        {
            if (string.IsNullOrEmpty(token))
                return false;

            try
            {
                var tokenBytes = Convert.FromBase64String(token);
                return tokenBytes.Length == 24;
            }
            catch
            {
                return false;
            }
        }

        // إنشاء مفتاح عشوائي
        private string GenerateRandomKey()
        {
            using var rng = RandomNumberGenerator.Create();
            var keyBytes = new byte[32];
            rng.GetBytes(keyBytes);
            return Convert.ToBase64String(keyBytes);
        }
    }

    // فئة استثناءات الأمان
    public class SecurityException : Exception
    {
        public SecurityException(string message) : base(message) { }
        public SecurityException(string message, Exception innerException) : base(message, innerException) { }
    }

    // خدمة مراقبة الأمان
    public interface ISecurityMonitoringService
    {
        Task LogSecurityEventAsync(string eventType, string description, string? userId = null);
        Task<bool> IsIPBlockedAsync(string ipAddress);
        Task BlockIPAsync(string ipAddress, TimeSpan duration);
        Task<int> GetFailedLoginAttemptsAsync(string email);
        Task RecordFailedLoginAsync(string email, string ipAddress);
        Task ClearFailedLoginAttemptsAsync(string email);
    }

    public class SecurityMonitoringService : ISecurityMonitoringService
    {
        private readonly ILogger<SecurityMonitoringService> _logger;
        private static readonly Dictionary<string, DateTime> _blockedIPs = new();
        private static readonly Dictionary<string, List<DateTime>> _failedLogins = new();
        private readonly object _lockObject = new();

        public SecurityMonitoringService(ILogger<SecurityMonitoringService> logger)
        {
            _logger = logger;
        }

        public async Task LogSecurityEventAsync(string eventType, string description, string? userId = null)
        {
            var logEntry = new
            {
                Timestamp = DateTime.UtcNow,
                EventType = eventType,
                Description = description,
                UserId = userId,
                IPAddress = GetCurrentIPAddress()
            };

            _logger.LogWarning("Security Event: {EventType} - {Description} - User: {UserId} - IP: {IPAddress}",
                eventType, description, userId, logEntry.IPAddress);

            // يمكن حفظ في قاعدة البيانات أو ملف منفصل
            await Task.CompletedTask;
        }

        public async Task<bool> IsIPBlockedAsync(string ipAddress)
        {
            lock (_lockObject)
            {
                if (_blockedIPs.ContainsKey(ipAddress))
                {
                    if (_blockedIPs[ipAddress] > DateTime.UtcNow)
                    {
                        return true;
                    }
                    else
                    {
                        _blockedIPs.Remove(ipAddress);
                    }
                }
            }

            return await Task.FromResult(false);
        }

        public async Task BlockIPAsync(string ipAddress, TimeSpan duration)
        {
            lock (_lockObject)
            {
                _blockedIPs[ipAddress] = DateTime.UtcNow.Add(duration);
            }

            await LogSecurityEventAsync("IP_BLOCKED", $"IP Address {ipAddress} blocked for {duration.TotalMinutes} minutes");
        }

        public async Task<int> GetFailedLoginAttemptsAsync(string email)
        {
            lock (_lockObject)
            {
                if (_failedLogins.ContainsKey(email))
                {
                    // إزالة المحاولات القديمة (أكثر من ساعة)
                    var cutoff = DateTime.UtcNow.AddHours(-1);
                    _failedLogins[email] = _failedLogins[email].Where(d => d > cutoff).ToList();
                    
                    return _failedLogins[email].Count;
                }
            }

            return await Task.FromResult(0);
        }

        public async Task RecordFailedLoginAsync(string email, string ipAddress)
        {
            lock (_lockObject)
            {
                if (!_failedLogins.ContainsKey(email))
                {
                    _failedLogins[email] = new List<DateTime>();
                }

                _failedLogins[email].Add(DateTime.UtcNow);
            }

            await LogSecurityEventAsync("FAILED_LOGIN", $"Failed login attempt for {email} from {ipAddress}");

            // حظر IP بعد 5 محاولات فاشلة
            var attempts = await GetFailedLoginAttemptsAsync(email);
            if (attempts >= 5)
            {
                await BlockIPAsync(ipAddress, TimeSpan.FromMinutes(30));
            }
        }

        public async Task ClearFailedLoginAttemptsAsync(string email)
        {
            lock (_lockObject)
            {
                if (_failedLogins.ContainsKey(email))
                {
                    _failedLogins.Remove(email);
                }
            }

            await Task.CompletedTask;
        }

        private string GetCurrentIPAddress()
        {
            // يمكن الحصول على IP من HttpContext
            return "Unknown";
        }
    }
}
