class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('selectedTheme') || 'default';
        this.themes = {
            default: {
                name: 'الافتراضي',
                primary: '#667eea',
                secondary: '#764ba2',
                success: '#28a745',
                danger: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8',
                light: '#f8f9fa',
                dark: '#343a40',
                background: '#ffffff',
                surface: '#f8f9fa',
                text: '#2c3e50'
            },
            dark: {
                name: 'الداكن',
                primary: '#667eea',
                secondary: '#764ba2',
                success: '#28a745',
                danger: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8',
                light: '#495057',
                dark: '#212529',
                background: '#1a1a1a',
                surface: '#2d2d2d',
                text: '#ffffff'
            },
            blue: {
                name: 'الأزرق',
                primary: '#007bff',
                secondary: '#6c757d',
                success: '#28a745',
                danger: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8',
                light: '#f8f9fa',
                dark: '#343a40',
                background: '#f0f8ff',
                surface: '#e6f3ff',
                text: '#1e3a8a'
            },
            green: {
                name: 'الأخضر',
                primary: '#28a745',
                secondary: '#20c997',
                success: '#198754',
                danger: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8',
                light: '#f8f9fa',
                dark: '#343a40',
                background: '#f0fff4',
                surface: '#e6ffe6',
                text: '#155724'
            },
            purple: {
                name: 'البنفسجي',
                primary: '#6f42c1',
                secondary: '#e83e8c',
                success: '#28a745',
                danger: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8',
                light: '#f8f9fa',
                dark: '#343a40',
                background: '#faf5ff',
                surface: '#f3e8ff',
                text: '#553c9a'
            }
        };
        this.animations = {
            enabled: localStorage.getItem('animationsEnabled') !== 'false',
            duration: parseInt(localStorage.getItem('animationDuration')) || 300
        };
    }

    // تهيئة مدير الثيمات
    init() {
        this.createThemeSelector();
        this.applyTheme(this.currentTheme);
        this.setupAnimations();
        this.addCustomStyles();
    }

    // إنشاء منتقي الثيمات
    createThemeSelector() {
        const themeSelector = document.createElement('div');
        themeSelector.className = 'theme-selector';
        themeSelector.innerHTML = `
            <div class="theme-toggle" onclick="themeManager.toggleThemePanel()">
                <i class="fas fa-palette"></i>
            </div>
            <div class="theme-panel" id="themePanel">
                <div class="theme-panel-header">
                    <h6>إعدادات المظهر</h6>
                    <button class="btn-close-theme" onclick="themeManager.closeThemePanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="theme-panel-content">
                    <div class="theme-section">
                        <label>اختر الثيم:</label>
                        <div class="theme-options">
                            ${Object.entries(this.themes).map(([key, theme]) => `
                                <div class="theme-option ${key === this.currentTheme ? 'active' : ''}" 
                                     onclick="themeManager.setTheme('${key}')" data-theme="${key}">
                                    <div class="theme-preview">
                                        <div class="theme-color" style="background: ${theme.primary}"></div>
                                        <div class="theme-color" style="background: ${theme.secondary}"></div>
                                        <div class="theme-color" style="background: ${theme.background}"></div>
                                    </div>
                                    <span class="theme-name">${theme.name}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    <div class="theme-section">
                        <label>الحركات والتأثيرات:</label>
                        <div class="animation-controls">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="animationsToggle" 
                                       ${this.animations.enabled ? 'checked' : ''} 
                                       onchange="themeManager.toggleAnimations()">
                                <label class="form-check-label" for="animationsToggle">
                                    تفعيل الحركات
                                </label>
                            </div>
                            <div class="animation-speed">
                                <label for="animationSpeed">سرعة الحركة:</label>
                                <input type="range" id="animationSpeed" min="100" max="1000" step="100" 
                                       value="${this.animations.duration}" 
                                       onchange="themeManager.setAnimationSpeed(this.value)">
                                <span class="speed-value">${this.animations.duration}ms</span>
                            </div>
                        </div>
                    </div>

                    <div class="theme-section">
                        <label>خيارات إضافية:</label>
                        <div class="additional-options">
                            <button class="btn btn-sm btn-outline-primary" onclick="themeManager.resetToDefault()">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="themeManager.exportSettings()">
                                <i class="fas fa-download me-1"></i>
                                تصدير الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(themeSelector);
    }

    // تطبيق الثيم
    applyTheme(themeName) {
        const theme = this.themes[themeName];
        if (!theme) return;

        const root = document.documentElement;
        
        // تطبيق متغيرات CSS
        Object.entries(theme).forEach(([key, value]) => {
            if (key !== 'name') {
                root.style.setProperty(`--theme-${key}`, value);
            }
        });

        // إضافة كلاس الثيم
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${themeName}`);

        this.currentTheme = themeName;
        localStorage.setItem('selectedTheme', themeName);

        // تحديث منتقي الثيمات
        this.updateThemeSelector();

        // إطلاق حدث تغيير الثيم
        document.dispatchEvent(new CustomEvent('themeChanged', { 
            detail: { theme: themeName, colors: theme } 
        }));
    }

    // تحديث منتقي الثيمات
    updateThemeSelector() {
        const options = document.querySelectorAll('.theme-option');
        options.forEach(option => {
            option.classList.toggle('active', option.dataset.theme === this.currentTheme);
        });
    }

    // تعيين ثيم جديد
    setTheme(themeName) {
        this.applyTheme(themeName);
        
        // تأثير انتقالي
        if (this.animations.enabled) {
            document.body.style.transition = `all ${this.animations.duration}ms ease`;
            setTimeout(() => {
                document.body.style.transition = '';
            }, this.animations.duration);
        }

        showSuccess('تم تغيير الثيم', `تم تطبيق ثيم ${this.themes[themeName].name} بنجاح`);
    }

    // إعداد الحركات
    setupAnimations() {
        const style = document.createElement('style');
        style.id = 'animation-styles';
        style.textContent = this.generateAnimationCSS();
        document.head.appendChild(style);

        if (!this.animations.enabled) {
            document.body.classList.add('no-animations');
        }
    }

    // إنشاء CSS للحركات
    generateAnimationCSS() {
        const duration = this.animations.duration;
        return `
            .animated {
                animation-duration: ${duration}ms !important;
                transition-duration: ${duration}ms !important;
            }

            .fade-in {
                animation: fadeIn ${duration}ms ease-in-out;
            }

            .slide-in-right {
                animation: slideInRight ${duration}ms ease-out;
            }

            .slide-in-left {
                animation: slideInLeft ${duration}ms ease-out;
            }

            .scale-in {
                animation: scaleIn ${duration}ms ease-out;
            }

            .bounce-in {
                animation: bounceIn ${duration * 1.5}ms ease-out;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideInLeft {
                from { transform: translateX(-100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes scaleIn {
                from { transform: scale(0.8); opacity: 0; }
                to { transform: scale(1); opacity: 1; }
            }

            @keyframes bounceIn {
                0% { transform: scale(0.3); opacity: 0; }
                50% { transform: scale(1.05); }
                70% { transform: scale(0.9); }
                100% { transform: scale(1); opacity: 1; }
            }

            .no-animations * {
                animation-duration: 0ms !important;
                transition-duration: 0ms !important;
            }
        `;
    }

    // تبديل الحركات
    toggleAnimations() {
        this.animations.enabled = !this.animations.enabled;
        localStorage.setItem('animationsEnabled', this.animations.enabled);

        document.body.classList.toggle('no-animations', !this.animations.enabled);

        const message = this.animations.enabled ? 'تم تفعيل الحركات' : 'تم إيقاف الحركات';
        showInfo('إعدادات الحركة', message);
    }

    // تعيين سرعة الحركة
    setAnimationSpeed(speed) {
        this.animations.duration = parseInt(speed);
        localStorage.setItem('animationDuration', this.animations.duration);

        // تحديث CSS
        const animationStyles = document.getElementById('animation-styles');
        if (animationStyles) {
            animationStyles.textContent = this.generateAnimationCSS();
        }

        // تحديث العرض
        document.querySelector('.speed-value').textContent = `${speed}ms`;
    }

    // تبديل لوحة الثيمات
    toggleThemePanel() {
        const panel = document.getElementById('themePanel');
        panel.classList.toggle('open');
    }

    // إغلاق لوحة الثيمات
    closeThemePanel() {
        const panel = document.getElementById('themePanel');
        panel.classList.remove('open');
    }

    // إعادة تعيين للافتراضي
    resetToDefault() {
        this.setTheme('default');
        this.animations.enabled = true;
        this.animations.duration = 300;
        
        localStorage.setItem('animationsEnabled', 'true');
        localStorage.setItem('animationDuration', '300');
        
        document.getElementById('animationsToggle').checked = true;
        document.getElementById('animationSpeed').value = 300;
        document.querySelector('.speed-value').textContent = '300ms';
        
        document.body.classList.remove('no-animations');
        this.setupAnimations();

        showSuccess('تم الإعادة', 'تم إعادة تعيين جميع الإعدادات للافتراضي');
    }

    // تصدير الإعدادات
    exportSettings() {
        const settings = {
            theme: this.currentTheme,
            animations: this.animations,
            exportDate: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'theme-settings.json';
        a.click();
        URL.revokeObjectURL(url);

        showSuccess('تم التصدير', 'تم تصدير إعدادات المظهر بنجاح');
    }

    // استيراد الإعدادات
    importSettings(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const settings = JSON.parse(e.target.result);
                
                if (settings.theme && this.themes[settings.theme]) {
                    this.setTheme(settings.theme);
                }
                
                if (settings.animations) {
                    this.animations = { ...this.animations, ...settings.animations };
                    localStorage.setItem('animationsEnabled', this.animations.enabled);
                    localStorage.setItem('animationDuration', this.animations.duration);
                    this.setupAnimations();
                }

                showSuccess('تم الاستيراد', 'تم استيراد إعدادات المظهر بنجاح');
            } catch (error) {
                showError('خطأ في الاستيراد', 'ملف الإعدادات غير صحيح');
            }
        };
        reader.readAsText(file);
    }

    // إضافة أنماط مخصصة
    addCustomStyles() {
        const style = document.createElement('style');
        style.id = 'theme-manager-styles';
        style.textContent = `
            .theme-selector {
                position: fixed;
                top: 50%;
                left: 20px;
                transform: translateY(-50%);
                z-index: 9999;
            }

            .theme-toggle {
                width: 50px;
                height: 50px;
                background: var(--theme-primary, #667eea);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }

            .theme-toggle:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
            }

            .theme-panel {
                position: absolute;
                left: 60px;
                top: 50%;
                transform: translateY(-50%);
                width: 320px;
                background: var(--theme-background, white);
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                border: 1px solid var(--theme-surface, #e9ecef);
            }

            .theme-panel.open {
                opacity: 1;
                visibility: visible;
            }

            .theme-panel-header {
                padding: 20px;
                border-bottom: 1px solid var(--theme-surface, #e9ecef);
                display: flex;
                justify-content: between;
                align-items: center;
            }

            .theme-panel-header h6 {
                margin: 0;
                color: var(--theme-text, #2c3e50);
                font-weight: 700;
            }

            .btn-close-theme {
                background: none;
                border: none;
                color: var(--theme-text, #2c3e50);
                cursor: pointer;
                padding: 5px;
                border-radius: 50%;
                transition: background-color 0.3s ease;
            }

            .btn-close-theme:hover {
                background: var(--theme-surface, #f8f9fa);
            }

            .theme-panel-content {
                padding: 20px;
                max-height: 400px;
                overflow-y: auto;
            }

            .theme-section {
                margin-bottom: 25px;
            }

            .theme-section label {
                display: block;
                margin-bottom: 10px;
                font-weight: 600;
                color: var(--theme-text, #2c3e50);
            }

            .theme-options {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }

            .theme-option {
                padding: 10px;
                border: 2px solid transparent;
                border-radius: 10px;
                cursor: pointer;
                transition: all 0.3s ease;
                text-align: center;
            }

            .theme-option:hover {
                border-color: var(--theme-primary, #667eea);
            }

            .theme-option.active {
                border-color: var(--theme-primary, #667eea);
                background: var(--theme-surface, #f8f9fa);
            }

            .theme-preview {
                display: flex;
                justify-content: center;
                margin-bottom: 8px;
            }

            .theme-color {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                margin: 0 2px;
                border: 2px solid white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .theme-name {
                font-size: 12px;
                color: var(--theme-text, #2c3e50);
                font-weight: 500;
            }

            .animation-controls {
                space-y: 15px;
            }

            .animation-speed {
                margin-top: 15px;
            }

            .animation-speed input[type="range"] {
                width: 100%;
                margin: 10px 0;
            }

            .speed-value {
                font-weight: 600;
                color: var(--theme-primary, #667eea);
            }

            .additional-options {
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
            }

            .additional-options .btn {
                flex: 1;
                min-width: 120px;
            }

            @media (max-width: 768px) {
                .theme-selector {
                    left: 10px;
                }

                .theme-panel {
                    width: 280px;
                    left: 50px;
                }

                .theme-options {
                    grid-template-columns: 1fr;
                }
            }
        `;

        document.head.appendChild(style);
    }

    // تطبيق حركة على عنصر
    animateElement(element, animationType = 'fade-in') {
        if (!this.animations.enabled) return;

        element.classList.add('animated', animationType);
        
        setTimeout(() => {
            element.classList.remove('animated', animationType);
        }, this.animations.duration);
    }

    // تطبيق حركات على مجموعة عناصر
    animateElements(elements, animationType = 'fade-in', delay = 100) {
        if (!this.animations.enabled) return;

        elements.forEach((element, index) => {
            setTimeout(() => {
                this.animateElement(element, animationType);
            }, index * delay);
        });
    }
}

// إنشاء مثيل عام
const themeManager = new ThemeManager();

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    try {
        themeManager.init();

        // تطبيق حركات على العناصر الموجودة
        const cards = document.querySelectorAll('.card');
        const buttons = document.querySelectorAll('.btn');

        if (cards.length > 0) {
            themeManager.animateElements(cards, 'fade-in', 150);
        }
        if (buttons.length > 0) {
            themeManager.animateElements(buttons, 'scale-in', 50);
        }
    } catch (error) {
        console.warn('Theme manager initialization failed:', error);
    }
});

// إغلاق لوحة الثيمات عند النقر خارجها
document.addEventListener('click', (e) => {
    const themeSelector = document.querySelector('.theme-selector');
    if (themeSelector && !themeSelector.contains(e.target)) {
        themeManager.closeThemePanel();
    }
});

// مدير التأثيرات المتقدمة
class AdvancedEffectsManager {
    constructor() {
        this.particleSystem = null;
        this.backgroundEffects = {
            enabled: localStorage.getItem('backgroundEffects') !== 'false',
            type: localStorage.getItem('backgroundEffectType') || 'particles'
        };
    }

    // تهيئة التأثيرات
    init() {
        if (this.backgroundEffects.enabled) {
            this.createBackgroundEffect();
        }
        this.setupScrollEffects();
        this.setupHoverEffects();
        this.setupLoadingEffects();
    }

    // إنشاء تأثير الخلفية
    createBackgroundEffect() {
        if (this.backgroundEffects.type === 'particles') {
            this.createParticleSystem();
        } else if (this.backgroundEffects.type === 'waves') {
            this.createWaveEffect();
        }
    }

    // نظام الجسيمات
    createParticleSystem() {
        const canvas = document.createElement('canvas');
        canvas.id = 'particle-canvas';
        canvas.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.3;
        `;
        document.body.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        const particles = [];
        const particleCount = 50;

        // تحديد حجم الكانفاس
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }

        // إنشاء جسيم
        function createParticle() {
            return {
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                size: Math.random() * 3 + 1,
                opacity: Math.random() * 0.5 + 0.2
            };
        }

        // تهيئة الجسيمات
        function initParticles() {
            particles.length = 0;
            for (let i = 0; i < particleCount; i++) {
                particles.push(createParticle());
            }
        }

        // تحديث الجسيمات
        function updateParticles() {
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;

                // إعادة تدوير الجسيمات
                if (particle.x < 0) particle.x = canvas.width;
                if (particle.x > canvas.width) particle.x = 0;
                if (particle.y < 0) particle.y = canvas.height;
                if (particle.y > canvas.height) particle.y = 0;
            });
        }

        // رسم الجسيمات
        function drawParticles() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            particles.forEach(particle => {
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(102, 126, 234, ${particle.opacity})`;
                ctx.fill();
            });

            // رسم الخطوط بين الجسيمات القريبة
            particles.forEach((particle, i) => {
                particles.slice(i + 1).forEach(otherParticle => {
                    const distance = Math.sqrt(
                        Math.pow(particle.x - otherParticle.x, 2) +
                        Math.pow(particle.y - otherParticle.y, 2)
                    );

                    if (distance < 100) {
                        ctx.beginPath();
                        ctx.moveTo(particle.x, particle.y);
                        ctx.lineTo(otherParticle.x, otherParticle.y);
                        ctx.strokeStyle = `rgba(102, 126, 234, ${0.1 * (1 - distance / 100)})`;
                        ctx.lineWidth = 1;
                        ctx.stroke();
                    }
                });
            });
        }

        // حلقة الرسم
        function animate() {
            updateParticles();
            drawParticles();
            requestAnimationFrame(animate);
        }

        // بدء النظام
        resizeCanvas();
        initParticles();
        animate();

        // إعادة تحديد الحجم عند تغيير النافذة
        window.addEventListener('resize', () => {
            resizeCanvas();
            initParticles();
        });

        this.particleSystem = { canvas, particles };
    }

    // تأثيرات التمرير
    setupScrollEffects() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                } else {
                    entry.target.classList.remove('animate-in');
                }
            });
        }, observerOptions);

        // مراقبة العناصر
        document.querySelectorAll('.card, .btn, .form-group').forEach(el => {
            observer.observe(el);
        });

        // إضافة أنماط التمرير
        const scrollStyles = document.createElement('style');
        scrollStyles.textContent = `
            .animate-in {
                animation: slideInUp 0.6s ease-out;
            }

            @keyframes slideInUp {
                from {
                    transform: translateY(30px);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(scrollStyles);
    }

    // تأثيرات التحويم
    setupHoverEffects() {
        const hoverStyles = document.createElement('style');
        hoverStyles.textContent = `
            .card {
                transition: all 0.3s ease;
            }

            .card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            }

            .btn {
                position: relative;
                overflow: hidden;
                transition: all 0.3s ease;
            }

            .btn::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 0;
                height: 0;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 50%;
                transform: translate(-50%, -50%);
                transition: width 0.6s, height 0.6s;
            }

            .btn:hover::before {
                width: 300px;
                height: 300px;
            }

            .nav-link {
                position: relative;
                transition: all 0.3s ease;
            }

            .nav-link::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                width: 0;
                height: 2px;
                background: var(--theme-primary, #667eea);
                transition: all 0.3s ease;
                transform: translateX(-50%);
            }

            .nav-link:hover::after {
                width: 100%;
            }
        `;
        document.head.appendChild(hoverStyles);
    }

    // تأثيرات التحميل
    setupLoadingEffects() {
        // إنشاء شاشة تحميل مخصصة
        const loadingScreen = document.createElement('div');
        loadingScreen.id = 'custom-loading';
        loadingScreen.innerHTML = `
            <div class="loading-content">
                <div class="loading-logo">
                    <i class="fas fa-users fa-3x"></i>
                </div>
                <div class="loading-text">
                    <h4>نظام إدارة الموظفين</h4>
                    <p>جاري التحميل...</p>
                </div>
                <div class="loading-progress">
                    <div class="progress-bar"></div>
                </div>
            </div>
        `;
        loadingScreen.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            color: white;
            text-align: center;
        `;

        const loadingStyles = document.createElement('style');
        loadingStyles.textContent = `
            .loading-content {
                animation: fadeInScale 1s ease-out;
            }

            .loading-logo {
                margin-bottom: 30px;
                animation: pulse 2s infinite;
            }

            .loading-text h4 {
                margin-bottom: 10px;
                font-weight: 700;
            }

            .loading-text p {
                margin-bottom: 30px;
                opacity: 0.9;
            }

            .loading-progress {
                width: 200px;
                height: 4px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 2px;
                overflow: hidden;
            }

            .progress-bar {
                height: 100%;
                background: white;
                border-radius: 2px;
                animation: loadingProgress 3s ease-in-out infinite;
            }

            @keyframes fadeInScale {
                from {
                    opacity: 0;
                    transform: scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }

            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.1); }
            }

            @keyframes loadingProgress {
                0% { width: 0%; }
                50% { width: 70%; }
                100% { width: 100%; }
            }
        `;
        document.head.appendChild(loadingStyles);

        // إظهار شاشة التحميل
        document.body.appendChild(loadingScreen);

        // إخفاء شاشة التحميل بعد التحميل
        window.addEventListener('load', () => {
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                loadingScreen.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    loadingScreen.remove();
                }, 500);
            }, 1000);
        });
    }

    // تبديل تأثيرات الخلفية
    toggleBackgroundEffects() {
        this.backgroundEffects.enabled = !this.backgroundEffects.enabled;
        localStorage.setItem('backgroundEffects', this.backgroundEffects.enabled);

        if (this.backgroundEffects.enabled) {
            this.createBackgroundEffect();
        } else {
            const canvas = document.getElementById('particle-canvas');
            if (canvas) canvas.remove();
        }
    }

    // تغيير نوع تأثير الخلفية
    setBackgroundEffectType(type) {
        this.backgroundEffects.type = type;
        localStorage.setItem('backgroundEffectType', type);

        // إزالة التأثير الحالي
        const canvas = document.getElementById('particle-canvas');
        if (canvas) canvas.remove();

        // إنشاء التأثير الجديد
        if (this.backgroundEffects.enabled) {
            this.createBackgroundEffect();
        }
    }
}

// إنشاء مثيل مدير التأثيرات
const effectsManager = new AdvancedEffectsManager();

// تهيئة التأثيرات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    effectsManager.init();
});
