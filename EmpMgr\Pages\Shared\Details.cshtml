@{
    ViewData["Title"] = "تفاصيل";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل العنصر
                    </h4>
                </div>
                
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        هذه صفحة تفاصيل عامة. يرجى تحديد العنصر المطلوب عرض تفاصيله.
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="javascript:history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للصفحة السابقة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
