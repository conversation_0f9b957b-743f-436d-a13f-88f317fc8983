using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmpMgr.Models
{
    // نموذج الرتب
    public class Rank
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "اسم الرتبة")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Display(Name = "الترتيب")]
        public int Order { get; set; }

        [Display(Name = "مدة الرتبة (بالسنوات)")]
        [Range(0, 50, ErrorMessage = "مدة الرتبة يجب أن تكون بين 0 و 50 سنة")]
        public int RankDurationYears { get; set; } = 3;



        [Display(Name = "الحد الأدنى للعمر للترقية")]
        [Range(18, 70, ErrorMessage = "العمر يجب أن يكون بين 18 و 70 سنة")]
        public int? MinAgeForPromotion { get; set; }

        [Display(Name = "الحد الأقصى للعمر للترقية")]
        [Range(18, 70, ErrorMessage = "العمر يجب أن يكون بين 18 و 70 سنة")]
        public int? MaxAgeForPromotion { get; set; }

        [Display(Name = "هل تتطلب دورات تدريبية؟")]
        public bool RequiresTrainingCourses { get; set; } = false;

        [Display(Name = "عدد الدورات المطلوبة")]
        [Range(0, 20, ErrorMessage = "عدد الدورات يجب أن يكون بين 0 و 20")]
        public int RequiredCoursesCount { get; set; } = 0;

        [Display(Name = "هل تتطلب تقييم أداء؟")]
        public bool RequiresPerformanceEvaluation { get; set; } = true;

        [Display(Name = "الحد الأدنى لتقييم الأداء")]
        [Range(0, 100, ErrorMessage = "تقييم الأداء يجب أن يكون بين 0 و 100")]
        public decimal? MinPerformanceScore { get; set; } = 75;

        [Display(Name = "ملاحظات الترقية")]
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? PromotionNotes { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ آخر تحديث")]
        public DateTime? LastUpdated { get; set; }

        // العلاقة مع الموظفين
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    // نموذج المحافظات
    public class Province
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "اسم المحافظة")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الكود")]
        public string? Code { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        // العلاقة مع الموظفين
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    // نموذج الوزارات
    public class Ministry
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الوزارة مطلوب")]
        [Display(Name = "اسم الوزارة")]
        [StringLength(200, ErrorMessage = "اسم الوزارة يجب أن يكون أقل من 200 حرف")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "رمز الوزارة")]
        [StringLength(10, ErrorMessage = "رمز الوزارة يجب أن يكون أقل من 10 أحرف")]
        public string? Code { get; set; }

        [Display(Name = "الوصف")]
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [Display(Name = "الموقع")]
        [StringLength(200, ErrorMessage = "الموقع يجب أن يكون أقل من 200 حرف")]
        public string? Location { get; set; }

        [Display(Name = "سنة التأسيس")]
        public int? EstablishedYear { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقات
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
        public virtual ICollection<Agency> Agencies { get; set; } = new List<Agency>();
    }

    // نموذج الوكالات
    public class Agency
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الوكالة مطلوب")]
        [Display(Name = "اسم الوكالة")]
        [StringLength(200, ErrorMessage = "اسم الوكالة يجب أن يكون أقل من 200 حرف")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "رمز الوكالة")]
        [StringLength(10, ErrorMessage = "رمز الوكالة يجب أن يكون أقل من 10 أحرف")]
        public string? Code { get; set; }

        [Display(Name = "الوصف")]
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "الوزارة مطلوبة")]
        [Display(Name = "الوزارة")]
        public int MinistryId { get; set; }

        [Display(Name = "سنة التأسيس")]
        public int? EstablishedYear { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقات
        [ForeignKey("MinistryId")]
        public virtual Ministry Ministry { get; set; } = null!;
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
        public virtual ICollection<Directorate> Directorates { get; set; } = new List<Directorate>();
    }

    // نموذج المديريات
    public class Directorate
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم المديرية مطلوب")]
        [Display(Name = "اسم المديرية")]
        [StringLength(200, ErrorMessage = "اسم المديرية يجب أن يكون أقل من 200 حرف")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "رمز المديرية")]
        [StringLength(10, ErrorMessage = "رمز المديرية يجب أن يكون أقل من 10 أحرف")]
        public string? Code { get; set; }

        [Display(Name = "الوصف")]
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "الوكالة مطلوبة")]
        [Display(Name = "الوكالة")]
        public int AgencyId { get; set; }

        [Display(Name = "سنة التأسيس")]
        public int? EstablishedYear { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقات
        [ForeignKey("AgencyId")]
        public virtual Agency Agency { get; set; } = null!;
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
        public virtual ICollection<GovernmentDepartment> GovernmentDepartments { get; set; } = new List<GovernmentDepartment>();
    }

    // نموذج الجامعات
    public class University
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "اسم الجامعة")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الموقع")]
        public string? Location { get; set; }

        [Display(Name = "نوع الجامعة")]
        public UniversityType Type { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        // العلاقة مع الموظفين
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();

        // العلاقة مع الكليات
        public virtual ICollection<College> Colleges { get; set; } = new List<College>();
    }

    // نموذج المعاهد
    public class Institute
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "اسم المعهد")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الموقع")]
        public string? Location { get; set; }

        [Display(Name = "نوع المعهد")]
        public InstituteType Type { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقات
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
        public virtual ICollection<Department> Departments { get; set; } = new List<Department>();
    }

    // نموذج الكليات
    public class College
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "اسم الكلية")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الكود")]
        public string? Code { get; set; }

        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required]
        [Display(Name = "الجامعة")]
        public int UniversityId { get; set; }
        [ForeignKey("UniversityId")]
        public virtual University? University { get; set; }

        [Display(Name = "نوع الكلية")]
        public CollegeType Type { get; set; }

        [Display(Name = "سنة التأسيس")]
        public int? EstablishedYear { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقات
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
        public virtual ICollection<AcademicDepartment> AcademicDepartments { get; set; } = new List<AcademicDepartment>();

    }

    // نموذج الأقسام الأكاديمية (للكليات)
    public class AcademicDepartment
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم القسم الأكاديمي مطلوب")]
        [Display(Name = "اسم القسم الأكاديمي")]
        [StringLength(200, ErrorMessage = "اسم القسم الأكاديمي يجب أن يكون أقل من 200 حرف")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "رمز القسم")]
        [StringLength(10, ErrorMessage = "رمز القسم يجب أن يكون أقل من 10 أحرف")]
        public string? Code { get; set; }

        [Display(Name = "الوصف")]
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "الكلية مطلوبة")]
        [Display(Name = "الكلية")]
        public int CollegeId { get; set; }

        [Display(Name = "نوع القسم")]
        public AcademicDepartmentType Type { get; set; }

        [Display(Name = "سنة التأسيس")]
        public int? EstablishedYear { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقات
        [ForeignKey("CollegeId")]
        public virtual College College { get; set; } = null!;
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    // نموذج الأقسام (للمعاهد)
    public class Department
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم القسم مطلوب")]
        [Display(Name = "اسم القسم")]
        [StringLength(200, ErrorMessage = "اسم القسم يجب أن يكون أقل من 200 حرف")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "رمز القسم")]
        [StringLength(10, ErrorMessage = "رمز القسم يجب أن يكون أقل من 10 أحرف")]
        public string? Code { get; set; }

        [Display(Name = "الوصف")]
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "المعهد مطلوب")]
        [Display(Name = "المعهد")]
        public int InstituteId { get; set; }

        [Display(Name = "نوع القسم")]
        public DepartmentType Type { get; set; }

        [Display(Name = "سنة التأسيس")]
        public int? EstablishedYear { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقات
        public virtual Institute Institute { get; set; } = null!;
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    // نموذج الأقسام الحكومية (للمديريات)
    public class GovernmentDepartment
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم القسم مطلوب")]
        [Display(Name = "اسم القسم")]
        [StringLength(200, ErrorMessage = "اسم القسم يجب أن يكون أقل من 200 حرف")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "رمز القسم")]
        [StringLength(10, ErrorMessage = "رمز القسم يجب أن يكون أقل من 10 أحرف")]
        public string? Code { get; set; }

        [Display(Name = "الوصف")]
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "المديرية مطلوبة")]
        [Display(Name = "المديرية")]
        public int DirectorateId { get; set; }

        [Display(Name = "سنة التأسيس")]
        public int? EstablishedYear { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقات
        [ForeignKey("DirectorateId")]
        public virtual Directorate Directorate { get; set; } = null!;
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
        public virtual ICollection<Division> Divisions { get; set; } = new List<Division>();
    }

    // نموذج الشعب
    public class Division
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الشعبة مطلوب")]
        [Display(Name = "اسم الشعبة")]
        [StringLength(200, ErrorMessage = "اسم الشعبة يجب أن يكون أقل من 200 حرف")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "رمز الشعبة")]
        [StringLength(10, ErrorMessage = "رمز الشعبة يجب أن يكون أقل من 10 أحرف")]
        public string? Code { get; set; }

        [Display(Name = "الوصف")]
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "القسم مطلوب")]
        [Display(Name = "القسم")]
        public int GovernmentDepartmentId { get; set; }

        [Display(Name = "سنة التأسيس")]
        public int? EstablishedYear { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقات
        [ForeignKey("GovernmentDepartmentId")]
        public virtual GovernmentDepartment GovernmentDepartment { get; set; } = null!;
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    // نموذج المستخدمين
    public class ApplicationUser : Microsoft.AspNetCore.Identity.IdentityUser
    {
        [Display(Name = "الاسم الكامل")]
        public string? FullName { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "آخر تسجيل دخول")]
        public DateTime? LastLoginDate { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;
    }

    // التعدادات للثوابت
    public enum UniversityType
    {
        [Display(Name = "حكومية")]
        Government = 1,
        [Display(Name = "أهلية")]
        Private = 2,
        [Display(Name = "تقنية")]
        Technical = 3
    }

    public enum InstituteType
    {
        [Display(Name = "تقني")]
        Technical = 1,
        [Display(Name = "مهني")]
        Vocational = 2,
        [Display(Name = "طبي")]
        Medical = 3,
        [Display(Name = "إداري")]
        Administrative = 4
    }

    public enum CollegeType
    {
        [Display(Name = "علمية")]
        Scientific = 1,
        [Display(Name = "إنسانية")]
        Humanities = 2,
        [Display(Name = "طبية")]
        Medical = 3,
        [Display(Name = "هندسية")]
        Engineering = 4,
        [Display(Name = "تقنية")]
        Technical = 5,
        [Display(Name = "إدارية")]
        Administrative = 6,
        [Display(Name = "قانونية")]
        Legal = 7,
        [Display(Name = "تربوية")]
        Educational = 8,
        [Display(Name = "فنية")]
        Arts = 9
    }

    // أنواع الأقسام الأكاديمية
    public enum AcademicDepartmentType
    {
        [Display(Name = "قسم علمي")]
        Scientific = 1,
        [Display(Name = "قسم إنساني")]
        Humanities = 2,
        [Display(Name = "قسم طبي")]
        Medical = 3,
        [Display(Name = "قسم هندسي")]
        Engineering = 4,
        [Display(Name = "قسم قانوني")]
        Law = 5,
        [Display(Name = "قسم اقتصادي")]
        Economics = 6,
        [Display(Name = "قسم تربوي")]
        Education = 7,
        [Display(Name = "قسم زراعي")]
        Agriculture = 8,
        [Display(Name = "قسم فني")]
        Arts = 9,
        [Display(Name = "قسم رياضيات")]
        Mathematics = 10,
        [Display(Name = "قسم فيزياء")]
        Physics = 11,
        [Display(Name = "قسم كيمياء")]
        Chemistry = 12,
        [Display(Name = "قسم أحياء")]
        Biology = 13,
        [Display(Name = "قسم حاسوب")]
        Computer = 14,
        [Display(Name = "أخرى")]
        Other = 15
    }

    // أنواع الأقسام
    public enum DepartmentType
    {
        [Display(Name = "تقني")]
        Technical = 1,
        [Display(Name = "إداري")]
        Administrative = 2,
        [Display(Name = "طبي")]
        Medical = 3,
        [Display(Name = "هندسي")]
        Engineering = 4,
        [Display(Name = "حاسوب")]
        Computer = 5,
        [Display(Name = "محاسبة")]
        Accounting = 6,
        [Display(Name = "قانوني")]
        Legal = 7,
        [Display(Name = "فنون")]
        Arts = 8,
        [Display(Name = "أخرى")]
        Other = 9
    }

    // أنواع الوكالات
    public enum AgencyType
    {
        [Display(Name = "وكالة الوزارة للشؤون الإدارية")]
        Administrative = 1,
        [Display(Name = "وكالة الوزارة للشؤون الفنية")]
        Technical = 2,
        [Display(Name = "وكالة الوزارة للشؤون المالية")]
        Financial = 3,
        [Display(Name = "وكالة الوزارة للتخطيط")]
        Planning = 4,
        [Display(Name = "وكالة الوزارة للتطوير")]
        Development = 5,
        [Display(Name = "أخرى")]
        Other = 6
    }

    // أنواع المديريات
    public enum DirectorateType
    {
        [Display(Name = "مديرية عامة")]
        General = 1,
        [Display(Name = "مديرية فنية")]
        Technical = 2,
        [Display(Name = "مديرية إدارية")]
        Administrative = 3,
        [Display(Name = "مديرية مالية")]
        Financial = 4,
        [Display(Name = "مديرية قانونية")]
        Legal = 5,
        [Display(Name = "مديرية تخطيط")]
        Planning = 6,
        [Display(Name = "مديرية تطوير")]
        Development = 7,
        [Display(Name = "أخرى")]
        Other = 8
    }

    // أنواع الأقسام الحكومية
    public enum GovernmentDepartmentType
    {
        [Display(Name = "قسم إداري")]
        Administrative = 1,
        [Display(Name = "قسم فني")]
        Technical = 2,
        [Display(Name = "قسم مالي")]
        Financial = 3,
        [Display(Name = "قسم قانوني")]
        Legal = 4,
        [Display(Name = "قسم الموارد البشرية")]
        HumanResources = 5,
        [Display(Name = "قسم التخطيط")]
        Planning = 6,
        [Display(Name = "قسم المتابعة")]
        Monitoring = 7,
        [Display(Name = "قسم التطوير")]
        Development = 8,
        [Display(Name = "قسم الحاسوب")]
        Computer = 9,
        [Display(Name = "أخرى")]
        Other = 10
    }

    // أنواع الشعب
    public enum DivisionType
    {
        [Display(Name = "شعبة إدارية")]
        Administrative = 1,
        [Display(Name = "شعبة فنية")]
        Technical = 2,
        [Display(Name = "شعبة مالية")]
        Financial = 3,
        [Display(Name = "شعبة قانونية")]
        Legal = 4,
        [Display(Name = "شعبة الأرشيف")]
        Archive = 5,
        [Display(Name = "شعبة المتابعة")]
        Monitoring = 6,
        [Display(Name = "شعبة الحاسوب")]
        Computer = 7,
        [Display(Name = "شعبة الإحصاء")]
        Statistics = 8,
        [Display(Name = "أخرى")]
        Other = 9
    }



    // نموذج سجل العمليات
    public class AuditLog
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "نوع العملية")]
        public string Action { get; set; } = string.Empty;

        [Required]
        [Display(Name = "الجدول")]
        public string TableName { get; set; } = string.Empty;

        [Display(Name = "معرف السجل")]
        public string? RecordId { get; set; }

        [Display(Name = "البيانات القديمة")]
        public string? OldValues { get; set; }

        [Display(Name = "البيانات الجديدة")]
        public string? NewValues { get; set; }

        [Required]
        [Display(Name = "المستخدم")]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [Display(Name = "تاريخ العملية")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "عنوان IP")]
        public string? IpAddress { get; set; }
    }
}
