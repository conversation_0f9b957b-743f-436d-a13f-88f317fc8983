using System.Text;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using EmpMgr.Data;
using EmpMgr.Models;
using OfficeOpenXml;
using System.Globalization;

namespace EmpMgr.Services
{
    public interface IExportImportService
    {
        Task<byte[]> ExportToExcelAsync(IEnumerable<Employee> employees);
        Task<byte[]> ExportToCsvAsync(IEnumerable<Employee> employees);
        Task<byte[]> ExportToJsonAsync(IEnumerable<Employee> employees);
        Task<byte[]> ExportToPdfAsync(IEnumerable<Employee> employees);
        Task<ImportResult> ImportFromExcelAsync(IFormFile file);
        Task<ImportResult> ImportFromCsvAsync(IFormFile file);
        Task<ImportResult> ImportFromJsonAsync(IFormFile file);
        Task<byte[]> GenerateTemplateAsync(string format);
    }

    public class ExportImportService : IExportImportService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ExportImportService> _logger;

        public ExportImportService(ApplicationDbContext context, ILogger<ExportImportService> logger)
        {
            _context = context;
            _logger = logger;
            // تأكد من تهيئة ترخيص EPPlus
            ExcelLicenseService.Initialize();
        }

        // تصدير إلى Excel
        public async Task<byte[]> ExportToExcelAsync(IEnumerable<Employee> employees)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("الموظفين");

            // إعداد الرؤوس
            var headers = new[]
            {
                "رقم الموظف", "الرقم الإحصائي", "الاسم الأول", "اسم الأب", "اسم الجد", 
                "اسم الجد الأكبر", "اللقب", "الاسم الكامل", "الجنس", "الحالة الاجتماعية",
                "فصيلة الدم", "الحالة الصحية", "التحصيل الدراسي", "الرتبة", "المحافظة",
                "الجامعة", "المعهد", "المنطقة", "الحي", "المحلة", "الزقاق", "الدار",
                "أقرب نقطة دالة", "تاريخ الإضافة"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
            }

            // إضافة البيانات
            int row = 2;
            foreach (var employee in employees)
            {
                worksheet.Cells[row, 1].Value = employee.EmployeeNumber;
                worksheet.Cells[row, 2].Value = employee.StatisticalNumber;
                worksheet.Cells[row, 3].Value = employee.FirstName;
                worksheet.Cells[row, 4].Value = employee.FatherName;
                worksheet.Cells[row, 5].Value = employee.GrandFatherName;
                worksheet.Cells[row, 6].Value = employee.GreatGrandFatherName;
                worksheet.Cells[row, 7].Value = employee.LastName;
                worksheet.Cells[row, 8].Value = employee.FullName;
                worksheet.Cells[row, 9].Value = employee.Gender == Gender.Male ? "ذكر" : "أنثى";
                worksheet.Cells[row, 10].Value = TranslateMaritalStatus(employee.MaritalStatus);
                worksheet.Cells[row, 11].Value = employee.BloodType;
                worksheet.Cells[row, 12].Value = employee.HealthStatus;
                worksheet.Cells[row, 13].Value = TranslateEducationLevel(employee.EducationLevel);
                worksheet.Cells[row, 14].Value = employee.Rank?.Name;
                worksheet.Cells[row, 15].Value = employee.Province?.Name;
                worksheet.Cells[row, 16].Value = employee.University?.Name;
                worksheet.Cells[row, 17].Value = employee.Institute?.Name;
                worksheet.Cells[row, 18].Value = employee.District;
                worksheet.Cells[row, 19].Value = employee.Neighborhood;
                worksheet.Cells[row, 20].Value = employee.Quarter;
                worksheet.Cells[row, 21].Value = employee.Alley;
                worksheet.Cells[row, 22].Value = employee.House;
                worksheet.Cells[row, 23].Value = employee.NearestLandmark;
                worksheet.Cells[row, 24].Value = employee.CreatedDate.ToString("yyyy-MM-dd");
                row++;
            }

            // تنسيق الجدول
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
            worksheet.View.RightToLeft = true;

            return await Task.FromResult(package.GetAsByteArray());
        }

        // تصدير إلى CSV
        public async Task<byte[]> ExportToCsvAsync(IEnumerable<Employee> employees)
        {
            var csv = new StringBuilder();
            
            // إضافة الرؤوس
            csv.AppendLine("رقم الموظف,الرقم الإحصائي,الاسم الأول,اسم الأب,اسم الجد,اسم الجد الأكبر,اللقب,الاسم الكامل,الجنس,الحالة الاجتماعية,فصيلة الدم,الحالة الصحية,التحصيل الدراسي,الرتبة,المحافظة,الجامعة,المعهد,المنطقة,الحي,المحلة,الزقاق,الدار,أقرب نقطة دالة,تاريخ الإضافة");

            // إضافة البيانات
            foreach (var employee in employees)
            {
                var line = $"\"{employee.EmployeeNumber}\",\"{employee.StatisticalNumber}\",\"{employee.FirstName}\",\"{employee.FatherName}\",\"{employee.GrandFatherName}\",\"{employee.GreatGrandFatherName}\",\"{employee.LastName}\",\"{employee.FullName}\",\"{(employee.Gender == Gender.Male ? "ذكر" : "أنثى")}\",\"{TranslateMaritalStatus(employee.MaritalStatus)}\",\"{employee.BloodType}\",\"{employee.HealthStatus}\",\"{TranslateEducationLevel(employee.EducationLevel)}\",\"{employee.Rank?.Name}\",\"{employee.Province?.Name}\",\"{employee.University?.Name}\",\"{employee.Institute?.Name}\",\"{employee.District}\",\"{employee.Neighborhood}\",\"{employee.Quarter}\",\"{employee.Alley}\",\"{employee.House}\",\"{employee.NearestLandmark}\",\"{employee.CreatedDate:yyyy-MM-dd}\"";
                csv.AppendLine(line);
            }

            return await Task.FromResult(Encoding.UTF8.GetBytes(csv.ToString()));
        }

        // تصدير إلى JSON
        public async Task<byte[]> ExportToJsonAsync(IEnumerable<Employee> employees)
        {
            var exportData = employees.Select(e => new
            {
                e.EmployeeNumber,
                e.StatisticalNumber,
                e.FirstName,
                e.FatherName,
                e.GrandFatherName,
                e.GreatGrandFatherName,
                e.LastName,
                FullName = e.FullName,
                Gender = e.Gender == Gender.Male ? "ذكر" : "أنثى",
                MaritalStatus = TranslateMaritalStatus(e.MaritalStatus),
                e.BloodType,
                e.HealthStatus,
                EducationLevel = TranslateEducationLevel(e.EducationLevel),
                Rank = e.Rank?.Name,
                Province = e.Province?.Name,
                University = e.University?.Name,
                Institute = e.Institute?.Name,
                e.District,
                e.Neighborhood,
                e.Quarter,
                e.Alley,
                e.House,
                e.NearestLandmark,
                CreatedDate = e.CreatedDate.ToString("yyyy-MM-dd")
            });

            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            var json = JsonSerializer.Serialize(exportData, options);
            return await Task.FromResult(Encoding.UTF8.GetBytes(json));
        }

        // تصدير إلى PDF
        public async Task<byte[]> ExportToPdfAsync(IEnumerable<Employee> employees)
        {
            // يمكن استخدام مكتبة مثل iTextSharp أو PdfSharp
            // هنا سنستخدم HTML to PDF كحل مؤقت
            var html = GenerateHtmlReport(employees);
            
            // تحويل HTML إلى PDF (يحتاج مكتبة إضافية)
            // return await ConvertHtmlToPdf(html);
            
            // مؤقتاً نرجع HTML
            return await Task.FromResult(Encoding.UTF8.GetBytes(html));
        }

        // استيراد من Excel
        public async Task<ImportResult> ImportFromExcelAsync(IFormFile file)
        {
            var result = new ImportResult();
            
            try
            {
                using var stream = new MemoryStream();
                await file.CopyToAsync(stream);
                
                using var package = new ExcelPackage(stream);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                
                if (worksheet == null)
                {
                    result.Errors.Add("لا يمكن العثور على ورقة عمل في الملف");
                    return result;
                }

                var rowCount = worksheet.Dimension?.Rows ?? 0;
                if (rowCount < 2)
                {
                    result.Errors.Add("الملف فارغ أو لا يحتوي على بيانات");
                    return result;
                }

                // تحميل البيانات المرجعية
                var ranks = await _context.Ranks.ToListAsync();
                var provinces = await _context.Provinces.ToListAsync();
                var universities = await _context.Universities.ToListAsync();
                var institutes = await _context.Institutes.ToListAsync();

                for (int row = 2; row <= rowCount; row++)
                {
                    try
                    {
                        var employee = new Employee
                        {
                            EmployeeNumber = worksheet.Cells[row, 1].Text,
                            StatisticalNumber = worksheet.Cells[row, 2].Text,
                            FirstName = worksheet.Cells[row, 3].Text,
                            FatherName = worksheet.Cells[row, 4].Text,
                            GrandFatherName = worksheet.Cells[row, 5].Text,
                            GreatGrandFatherName = worksheet.Cells[row, 6].Text,
                            LastName = worksheet.Cells[row, 7].Text,
                            Gender = worksheet.Cells[row, 9].Text == "ذكر" ? Gender.Male : Gender.Female,
                            MaritalStatus = ParseMaritalStatus(worksheet.Cells[row, 10].Text),
                            BloodType = ParseBloodType(worksheet.Cells[row, 11].Text),
                            HealthStatus = ParseHealthStatus(worksheet.Cells[row, 12].Text),
                            EducationLevel = ParseEducationLevel(worksheet.Cells[row, 13].Text),
                            District = worksheet.Cells[row, 18].Text,
                            Neighborhood = worksheet.Cells[row, 19].Text,
                            Quarter = worksheet.Cells[row, 20].Text,
                            Alley = worksheet.Cells[row, 21].Text,
                            House = worksheet.Cells[row, 22].Text,
                            NearestLandmark = worksheet.Cells[row, 23].Text,
                            CreatedDate = DateTime.Now
                        };

                        // البحث عن الرتبة
                        var rankName = worksheet.Cells[row, 14].Text;
                        var rank = ranks.FirstOrDefault(r => r.Name == rankName);
                        if (rank != null)
                        {
                            employee.RankId = rank.Id;
                        }

                        // البحث عن المحافظة
                        var provinceName = worksheet.Cells[row, 15].Text;
                        var province = provinces.FirstOrDefault(p => p.Name == provinceName);
                        if (province != null)
                        {
                            employee.ProvinceId = province.Id;
                        }

                        // البحث عن الجامعة
                        var universityName = worksheet.Cells[row, 16].Text;
                        if (!string.IsNullOrEmpty(universityName))
                        {
                            var university = universities.FirstOrDefault(u => u.Name == universityName);
                            if (university != null)
                            {
                                employee.UniversityId = university.Id;
                            }
                        }

                        // البحث عن المعهد
                        var instituteName = worksheet.Cells[row, 17].Text;
                        if (!string.IsNullOrEmpty(instituteName))
                        {
                            var institute = institutes.FirstOrDefault(i => i.Name == instituteName);
                            if (institute != null)
                            {
                                employee.InstituteId = institute.Id;
                            }
                        }

                        // التحقق من صحة البيانات
                        if (string.IsNullOrEmpty(employee.FirstName) || 
                            string.IsNullOrEmpty(employee.FatherName) ||
                            employee.RankId == 0 || 
                            employee.ProvinceId == 0)
                        {
                            result.Errors.Add($"الصف {row}: بيانات مطلوبة مفقودة");
                            continue;
                        }

                        _context.Employees.Add(employee);
                        result.SuccessCount++;
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"الصف {row}: {ex.Message}");
                    }
                }

                if (result.SuccessCount > 0)
                {
                    await _context.SaveChangesAsync();
                }

                result.TotalProcessed = rowCount - 1;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في معالجة الملف: {ex.Message}");
            }

            return result;
        }

        // استيراد من CSV
        public async Task<ImportResult> ImportFromCsvAsync(IFormFile file)
        {
            var result = new ImportResult();
            
            try
            {
                using var reader = new StreamReader(file.OpenReadStream(), Encoding.UTF8);
                var lines = new List<string>();
                
                while (!reader.EndOfStream)
                {
                    var line = await reader.ReadLineAsync();
                    if (line != null)
                        lines.Add(line);
                }

                if (lines.Count < 2)
                {
                    result.Errors.Add("الملف فارغ أو لا يحتوي على بيانات");
                    return result;
                }

                // تخطي الرأس
                for (int i = 1; i < lines.Count; i++)
                {
                    try
                    {
                        var fields = ParseCsvLine(lines[i]);
                        if (fields.Length < 15)
                        {
                            result.Errors.Add($"الصف {i + 1}: عدد الحقول غير كافي");
                            continue;
                        }

                        // معالجة البيانات مشابهة لـ Excel
                        // ... (كود مشابه لاستيراد Excel)
                        
                        result.SuccessCount++;
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"الصف {i + 1}: {ex.Message}");
                    }
                }

                result.TotalProcessed = lines.Count - 1;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في معالجة الملف: {ex.Message}");
            }

            return result;
        }

        // استيراد من JSON
        public async Task<ImportResult> ImportFromJsonAsync(IFormFile file)
        {
            var result = new ImportResult();
            
            try
            {
                using var reader = new StreamReader(file.OpenReadStream(), Encoding.UTF8);
                var json = await reader.ReadToEndAsync();
                
                var employees = JsonSerializer.Deserialize<List<dynamic>>(json);
                
                // معالجة البيانات
                // ... (كود معالجة JSON)
                
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في معالجة الملف: {ex.Message}");
            }

            return result;
        }

        // إنشاء قالب
        public async Task<byte[]> GenerateTemplateAsync(string format)
        {
            var sampleEmployees = new List<Employee>
            {
                new Employee
                {
                    EmployeeNumber = "EMP001",
                    StatisticalNumber = "123456789",
                    FirstName = "أحمد",
                    FatherName = "محمد",
                    GrandFatherName = "علي",
                    GreatGrandFatherName = "حسن",
                    LastName = "الكريم",
                    Gender = Gender.Male,
                    MaritalStatus = MaritalStatus.Single,
                    BloodType = BloodType.OPositive,
                    HealthStatus = HealthStatus.Healthy,
                    EducationLevel = EducationLevel.Bachelor,
                    District = "الكرخ",
                    Neighborhood = "الحارثية",
                    Quarter = "المحلة الأولى",
                    Alley = "الزقاق الأول",
                    House = "الدار رقم 1",
                    NearestLandmark = "قرب الجامع الكبير",
                    CreatedDate = DateTime.Now
                }
            };

            return format.ToLower() switch
            {
                "excel" => await ExportToExcelAsync(sampleEmployees),
                "csv" => await ExportToCsvAsync(sampleEmployees),
                "json" => await ExportToJsonAsync(sampleEmployees),
                _ => throw new ArgumentException("صيغة غير مدعومة")
            };
        }

        // دوال مساعدة
        private string TranslateMaritalStatus(MaritalStatus status)
        {
            return status switch
            {
                MaritalStatus.Single => "أعزب",
                MaritalStatus.Married => "متزوج",
                MaritalStatus.Divorced => "مطلق",
                MaritalStatus.Widowed => "أرمل",
                _ => status.ToString()
            };
        }

        private string TranslateEducationLevel(EducationLevel level)
        {
            return level switch
            {
                EducationLevel.Primary => "ابتدائية",
                EducationLevel.Intermediate => "متوسطة",
                EducationLevel.Secondary => "إعدادية",
                EducationLevel.Diploma => "دبلوم",
                EducationLevel.Bachelor => "بكالوريوس",
                EducationLevel.Master => "ماجستير",
                EducationLevel.PhD => "دكتوراه",
                _ => level.ToString()
            };
        }

        private MaritalStatus ParseMaritalStatus(string status)
        {
            return status switch
            {
                "أعزب" => MaritalStatus.Single,
                "متزوج" => MaritalStatus.Married,
                "مطلق" => MaritalStatus.Divorced,
                "أرمل" => MaritalStatus.Widowed,
                _ => MaritalStatus.Single
            };
        }

        private EducationLevel ParseEducationLevel(string level)
        {
            return level switch
            {
                "ابتدائية" => EducationLevel.Primary,
                "متوسطة" => EducationLevel.Intermediate,
                "إعدادية" => EducationLevel.Secondary,
                "دبلوم" => EducationLevel.Diploma,
                "بكالوريوس" => EducationLevel.Bachelor,
                "ماجستير" => EducationLevel.Master,
                "دكتوراه" => EducationLevel.PhD,
                _ => EducationLevel.Secondary
            };
        }

        private BloodType ParseBloodType(string bloodType)
        {
            return bloodType switch
            {
                "A+" => BloodType.APositive,
                "A-" => BloodType.ANegative,
                "B+" => BloodType.BPositive,
                "B-" => BloodType.BNegative,
                "AB+" => BloodType.ABPositive,
                "AB-" => BloodType.ABNegative,
                "O+" => BloodType.OPositive,
                "O-" => BloodType.ONegative,
                _ => BloodType.OPositive
            };
        }

        private HealthStatus ParseHealthStatus(string status)
        {
            return status switch
            {
                "سليم" => HealthStatus.Healthy,
                "مريض" => HealthStatus.Sick,
                "معاق" => HealthStatus.Disabled,
                _ => HealthStatus.Healthy
            };
        }

        private string[] ParseCsvLine(string line)
        {
            var fields = new List<string>();
            var inQuotes = false;
            var field = new StringBuilder();

            for (int i = 0; i < line.Length; i++)
            {
                var c = line[i];
                
                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    fields.Add(field.ToString());
                    field.Clear();
                }
                else
                {
                    field.Append(c);
                }
            }

            fields.Add(field.ToString());
            return fields.ToArray();
        }

        private string GenerateHtmlReport(IEnumerable<Employee> employees)
        {
            var html = new StringBuilder();
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html dir='rtl'>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='utf-8'>");
            html.AppendLine("<title>تقرير الموظفين</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: Arial, sans-serif; direction: rtl; }");
            html.AppendLine("table { width: 100%; border-collapse: collapse; }");
            html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }");
            html.AppendLine("th { background-color: #f2f2f2; }");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            html.AppendLine("<h1>تقرير الموظفين</h1>");
            html.AppendLine("<table>");
            
            // الرؤوس
            html.AppendLine("<tr>");
            html.AppendLine("<th>رقم الموظف</th><th>الاسم الكامل</th><th>الرتبة</th><th>المحافظة</th>");
            html.AppendLine("</tr>");
            
            // البيانات
            foreach (var employee in employees)
            {
                html.AppendLine("<tr>");
                html.AppendLine($"<td>{employee.EmployeeNumber}</td>");
                html.AppendLine($"<td>{employee.FullName}</td>");
                html.AppendLine($"<td>{employee.Rank?.Name}</td>");
                html.AppendLine($"<td>{employee.Province?.Name}</td>");
                html.AppendLine("</tr>");
            }
            
            html.AppendLine("</table>");
            html.AppendLine("</body>");
            html.AppendLine("</html>");
            
            return html.ToString();
        }
    }

    // نتيجة الاستيراد
    public class ImportResult
    {
        public int TotalProcessed { get; set; }
        public int SuccessCount { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public bool HasErrors => Errors.Any();
        public int ErrorCount => Errors.Count;
    }
}
