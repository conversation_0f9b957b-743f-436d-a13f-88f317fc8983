@{
    ViewData["Title"] = "الصفحة الرئيسية";
}

<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <h1 class="hero-title">
            <i class="fas fa-users me-3"></i>
            نظام إدارة الموظفين
        </h1>
        <p class="hero-subtitle">
            نظام متكامل وحديث لإدارة بيانات الموظفين بكفاءة وأمان
        </p>
        <div class="hero-buttons">
            <a asp-controller="Employee" asp-action="Create" class="btn btn-light btn-lg me-3">
                <i class="fas fa-user-plus me-2"></i>
                إضافة موظف جديد
            </a>
            <a asp-controller="Employee" asp-action="Index" class="btn btn-outline-light btn-lg me-3">
                <i class="fas fa-list me-2"></i>
                قائمة الموظفين
            </a>
            <a asp-controller="Constants" asp-action="Index" class="btn btn-outline-light btn-lg">
                <i class="fas fa-cog me-2"></i>
                إعداد النظام
            </a>
        </div>
    </div>
</div>

<!-- Quick Start Section -->
<div class="container">
    <div class="row mb-5">
        <div class="col-12 text-center mb-5">
            <h2 class="display-5 fw-bold text-primary">البدء السريع</h2>
            <p class="lead text-muted">ابدأ بإعداد النظام وإضافة البيانات الأساسية</p>
        </div>
    </div>
    
    <div class="row g-4 mb-5">
        <div class="col-lg-4 col-md-6">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-medal"></i>
                </div>
                <h4 class="feature-title">إضافة الرتب</h4>
                <p class="feature-description">
                    ابدأ بإضافة الرتب العسكرية والوظيفية للنظام
                </p>
                <a asp-controller="Constants" asp-action="Ranks" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إدارة الرتب
                </a>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <h4 class="feature-title">إضافة المحافظات</h4>
                <p class="feature-description">
                    أضف المحافظات والمناطق الجغرافية
                </p>
                <a asp-controller="Constants" asp-action="Provinces" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>
                    إدارة المحافظات
                </a>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-university"></i>
                </div>
                <h4 class="feature-title">إضافة الجامعات</h4>
                <p class="feature-description">
                    أضف الجامعات والمعاهد التعليمية
                </p>
                <a asp-controller="Constants" asp-action="Universities" class="btn btn-info">
                    <i class="fas fa-plus me-2"></i>
                    إدارة الجامعات
                </a>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <h4 class="feature-title">إضافة موظف</h4>
                <p class="feature-description">
                    ابدأ بإضافة الموظفين بعد إعداد البيانات الأساسية
                </p>
                <a asp-controller="Employee" asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة موظف
                </a>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h4 class="feature-title">البحث المتقدم</h4>
                <p class="feature-description">
                    ابحث في قاعدة بيانات الموظفين بطرق متقدمة
                </p>
                <a asp-controller="Employee" asp-action="Search" class="btn btn-warning">
                    <i class="fas fa-search me-2"></i>
                    البحث المتقدم
                </a>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h4 class="feature-title">لوحة التحكم</h4>
                <p class="feature-description">
                    عرض الإحصائيات والتقارير التفاعلية
                </p>
                <a asp-controller="Dashboard" asp-action="Index" class="btn btn-info">
                    <i class="fas fa-chart-line me-2"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stats Section -->
<div class="stats-section">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-4">
                <h3 class="fw-bold text-primary">إحصائيات النظام</h3>
            </div>
        </div>
        <div class="row" id="statsContainer">
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <span class="stat-number" id="totalEmployees">0</span>
                    <div class="stat-label">إجمالي الموظفين</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <span class="stat-number" id="totalRanks">0</span>
                    <div class="stat-label">الرتب المتاحة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <span class="stat-number" id="totalProvinces">0</span>
                    <div class="stat-label">المحافظات</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <span class="stat-number" id="totalUniversities">0</span>
                    <div class="stat-label">الجامعات</div>
                </div>
            </div>
        </div>
        
        <!-- Empty State Message -->
        <div id="emptyState" class="empty-state" style="display: none;">
            <i class="fas fa-database"></i>
            <h4>قاعدة البيانات فارغة</h4>
            <p>ابدأ بإضافة البيانات الأساسية للنظام من خلال الروابط أعلاه</p>
        </div>
    </div>
</div>

<!-- قسم معلومات المطور -->
<div class="container mb-5">
    <div class="row">
        <div class="col-12">
            <div class="developer-section">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="developer-info">
                            <h3 class="developer-title">
                                <i class="fas fa-code text-primary me-2"></i>
                                مطور النظام
                            </h3>
                            <h4 class="developer-name">العقيد المهندس حيدر چياد ثويني</h4>
                            <p class="developer-description">
                                <i class="fas fa-laptop-code me-2"></i>
                                مطور ومصمم نظام إدارة الموظفين - خبير في تطوير الأنظمة الإدارية والأمنية
                            </p>
                            <div class="developer-badges">
                                <span class="badge bg-primary me-2">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    خبير أمن معلومات
                                </span>
                                <span class="badge bg-success me-2">
                                    <i class="fas fa-cogs me-1"></i>
                                    مطور أنظمة
                                </span>
                                <span class="badge bg-info">
                                    <i class="fas fa-database me-1"></i>
                                    مصمم قواعد بيانات
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="copyright-section">
                            <div class="copyright-icon">
                                <i class="fas fa-copyright"></i>
                            </div>
                            <h5 class="copyright-text">جميع الحقوق محفوظة</h5>
                            <p class="copyright-year">@DateTime.Now.Year</p>
                            <div class="system-info">
                                <span class="badge bg-dark">
                                    <i class="fas fa-tag me-1"></i>
                                    الإصدار 1.0.0
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
            border-radius: 0 0 50px 50px;
            margin-bottom: 50px;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            border: none;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .feature-title {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .feature-description {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .stats-section {
            background: #f8f9fa;
            padding: 60px 0;
            border-radius: 50px;
            margin: 50px 0;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #667eea;
            display: block;
        }

        .stat-label {
            color: #6c757d;
            font-weight: 600;
            margin-top: 10px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        /* تصميم قسم معلومات المطور */
        .developer-section {
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border: 1px solid #e9ecef;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin: 2rem 0;
            position: relative;
            overflow: hidden;
        }

        .developer-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .developer-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .developer-name {
            color: #667eea;
            font-weight: 700;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .developer-description {
            color: #6c757d;
            font-size: 1rem;
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .developer-badges .badge {
            font-size: 0.85rem;
            padding: 0.5rem 0.8rem;
            margin-bottom: 0.5rem;
        }

        .copyright-section {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
        }

        .copyright-icon {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .copyright-text {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .copyright-year {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .system-info .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.8rem;
        }

        /* تأثيرات تفاعلية */
        .developer-section:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .developer-badges .badge:hover {
            transform: scale(1.05);
            transition: transform 0.2s ease;
        }

        @@media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-buttons .btn {
                display: block;
                margin: 10px 0;
            }

            .developer-section {
                padding: 1.5rem;
                margin: 1rem 0;
            }

            .developer-name {
                font-size: 1.3rem;
            }

            .copyright-section {
                margin-top: 1.5rem;
            }
        }
    </style>
}

@section Scripts {
    <script>
        // تحميل الإحصائيات الحقيقية من قاعدة البيانات
        document.addEventListener('DOMContentLoaded', function() {
            loadRealStatistics();
        });

        async function loadRealStatistics() {
            try {
                // تحميل الإحصائيات من Dashboard API
                const response = await fetch('/Dashboard/GetGeneralStats');
                if (response.ok) {
                    const stats = await response.json();

                    // تحديث الإحصائيات مع تأثير متحرك
                    animateCounter('totalEmployees', stats.totalEmployees || 0);
                    animateCounter('totalRanks', stats.totalRanks || 0);
                    animateCounter('totalProvinces', stats.totalProvinces || 0);

                    // تحميل إحصائيات الجامعات من Constants API
                    const universitiesResponse = await fetch('/Constants/GetUniversities');
                    if (universitiesResponse.ok) {
                        const universities = await universitiesResponse.json();
                        animateCounter('totalUniversities', universities.length || 0);
                    }

                    // إظهار رسالة قاعدة البيانات الفارغة إذا لزم الأمر
                    const totalData = (stats.totalEmployees || 0) + (stats.totalRanks || 0) +
                                     (stats.totalProvinces || 0);

                    if (totalData === 0) {
                        document.getElementById('statsContainer').style.display = 'none';
                        document.getElementById('emptyState').style.display = 'block';
                    }
                } else {
                    // في حالة عدم توفر البيانات، عرض أصفار
                    showEmptyState();
                }
            } catch (error) {
                console.warn('لا يمكن تحميل الإحصائيات:', error);
                showEmptyState();
            }
        }

        function showEmptyState() {
            document.getElementById('totalEmployees').textContent = '0';
            document.getElementById('totalRanks').textContent = '0';
            document.getElementById('totalProvinces').textContent = '0';
            document.getElementById('totalUniversities').textContent = '0';

            document.getElementById('statsContainer').style.display = 'none';
            document.getElementById('emptyState').style.display = 'block';
        }

        function animateCounter(elementId, targetValue) {
            const element = document.getElementById(elementId);
            let currentValue = 0;
            const increment = targetValue > 0 ? targetValue / 50 : 0;

            if (targetValue === 0) {
                element.textContent = '0';
                return;
            }

            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= targetValue) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(currentValue);
            }, 30);
        }
    </script>
}
