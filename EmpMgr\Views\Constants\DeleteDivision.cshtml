@model EmpMgr.Models.Division
@using EmpMgr.Extensions

@{
    ViewData["Title"] = "حذف الشعبة";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد حذف الشعبة
                    </h4>
                </div>

                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هل أنت متأكد من رغبتك في حذف هذه الشعبة؟ هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-danger">معلومات الشعبة:</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>اسم الشعبة:</strong></td>
                                    <td>@Model.Name</td>
                                </tr>
                                <tr>
                                    <td><strong>القسم الحكومي:</strong></td>
                                    <td>@Model.GovernmentDepartment.Name</td>
                                </tr>
                                <tr>
                                    <td><strong>المديرية:</strong></td>
                                    <td>@Model.GovernmentDepartment.Directorate.Name</td>
                                </tr>
                                <tr>
                                    <td><strong>الوكالة:</strong></td>
                                    <td>@Model.GovernmentDepartment.Directorate.Agency.Name</td>
                                </tr>
                                <tr>
                                    <td><strong>الوزارة:</strong></td>
                                    <td>@Model.GovernmentDepartment.Directorate.Agency.Ministry.Name</td>
                                </tr>
                                @if (!string.IsNullOrEmpty(Model.Code))
                                {
                                    <tr>
                                        <td><strong>الرمز:</strong></td>
                                        <td><span class="badge bg-secondary">@Model.Code</span></td>
                                    </tr>
                                }
                                @if (Model.EstablishedYear.HasValue)
                                {
                                    <tr>
                                        <td><strong>سنة التأسيس:</strong></td>
                                        <td>@Model.EstablishedYear</td>
                                    </tr>
                                }
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        @if (Model.IsActive)
                                        {
                                            <span class="badge bg-success">نشط</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">غير نشط</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-info">الإحصائيات:</h5>
                            <div class="row text-center">
                                <div class="col-12 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h3 class="text-success">@Model.Employees.Count</h3>
                                            <small>الموظفين</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            @if (Model.Employees.Any())
                            {
                                <div class="alert alert-danger" role="alert">
                                    <i class="fas fa-ban me-2"></i>
                                    <strong>لا يمكن الحذف:</strong> هذه الشعبة مرتبطة بموظفين. يجب حذف هذه الارتباطات أولاً.
                                </div>
                            }
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Description))
                    {
                        <div class="mt-3">
                            <h6><strong>الوصف:</strong></h6>
                            <p class="text-muted">@Model.Description</p>
                        </div>
                    }

                    <div class="d-flex justify-content-between mt-4">
                        <div>
                            @if (!Model.Employees.Any())
                            {
                                <form asp-action="DeleteDivision" method="post" class="d-inline">
                                    <input asp-for="Id" type="hidden" />
                                    <button type="submit" class="btn btn-danger" 
                                            onclick="return confirm('هل أنت متأكد من رغبتك في حذف هذه الشعبة نهائياً؟')">
                                        <i class="fas fa-trash me-2"></i>
                                        تأكيد الحذف
                                    </button>
                                </form>
                            }
                            else
                            {
                                <button type="button" class="btn btn-danger" disabled>
                                    <i class="fas fa-ban me-2"></i>
                                    لا يمكن الحذف
                                </button>
                            }
                        </div>
                        <div>
                            <a asp-action="EditDivision" asp-route-id="@Model.Id" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>
                                تعديل بدلاً من الحذف
                            </a>
                            <a asp-action="Divisions" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .table td {
        padding: 0.5rem;
        border: none;
    }
    
    .card.bg-light .card-body {
        padding: 1rem;
    }
    
    .badge {
        font-size: 0.8em;
    }
</style>
