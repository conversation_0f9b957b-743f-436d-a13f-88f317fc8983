{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=EmpMgrDb;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Security": {"EncryptionKey": "MySecretEncryptionKey123456789", "HashSalt": "MySecretSalt987654321", "MaxFileSize": 52428800, "AllowedImageTypes": [".jpg", ".jpeg", ".png", ".gif"], "AllowedDocumentTypes": [".pdf", ".doc", ".docx", ".xls", ".xlsx"], "SessionTimeout": 30, "MaxLoginAttempts": 5, "LockoutDuration": 30, "RequireHttps": true, "EnableCSP": true, "EnableHSTS": true, "RateLimiting": {"RequestsPerMinute": 60, "RequestsPerHour": 1000}, "PasswordPolicy": {"RequireDigit": true, "RequireLowercase": true, "RequireUppercase": true, "RequireNonAlphanumeric": true, "RequiredLength": 8, "RequiredUniqueChars": 4}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "EmpMgr.Middleware.SecurityMiddleware": "Information", "EmpMgr.Services": "Information", "EmpMgr.Controllers": "Information"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}, "File": {"Path": "Logs/app-{Date}.log", "MinLevel": "Information", "RollingInterval": "Day", "RetainedFileCountLimit": 30}}, "AllowedHosts": "*"}