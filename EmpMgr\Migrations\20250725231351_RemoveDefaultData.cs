﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace EmpMgr.Migrations
{
    /// <inheritdoc />
    public partial class RemoveDefaultData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Institutes",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "Institutes",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "Institutes",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "Institutes",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 8);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 9);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 10);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 11);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 12);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 13);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 14);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 15);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 16);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 17);

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValue: 18);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 8);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 9);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 10);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 11);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 12);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 13);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 14);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 15);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "Id",
                keyValue: 16);

            migrationBuilder.DeleteData(
                table: "Universities",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "Universities",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "Universities",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "Universities",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "Universities",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "Universities",
                keyColumn: "Id",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "Universities",
                keyColumn: "Id",
                keyValue: 7);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "Institutes",
                columns: new[] { "Id", "IsActive", "Location", "Name", "Type" },
                values: new object[,]
                {
                    { 1, true, "بغداد", "المعهد التقني الطبي", 3 },
                    { 2, true, "بغداد", "المعهد التقني الإداري", 3 },
                    { 3, true, "بغداد", "معهد الإدارة", 1 },
                    { 4, true, "البصرة", "المعهد التقني البصرة", 3 }
                });

            migrationBuilder.InsertData(
                table: "Provinces",
                columns: new[] { "Id", "Code", "IsActive", "Name" },
                values: new object[,]
                {
                    { 1, "BGD", true, "بغداد" },
                    { 2, "BSR", true, "البصرة" },
                    { 3, "NIN", true, "نينوى" },
                    { 4, "ERB", true, "أربيل" },
                    { 5, "NAJ", true, "النجف" },
                    { 6, "KRB", true, "كربلاء" },
                    { 7, "BAB", true, "بابل" },
                    { 8, "ANB", true, "الأنبار" },
                    { 9, "DHQ", true, "ذي قار" },
                    { 10, "QAD", true, "القادسية" },
                    { 11, "KRK", true, "كركوك" },
                    { 12, "DYL", true, "ديالى" },
                    { 13, "MTH", true, "المثنى" },
                    { 14, "WST", true, "واسط" },
                    { 15, "SLD", true, "صلاح الدين" },
                    { 16, "DHK", true, "دهوك" },
                    { 17, "SLM", true, "السليمانية" },
                    { 18, "MYS", true, "ميسان" }
                });

            migrationBuilder.InsertData(
                table: "Ranks",
                columns: new[] { "Id", "Description", "IsActive", "Name", "Order" },
                values: new object[,]
                {
                    { 1, null, true, "فريق أول", 1 },
                    { 2, null, true, "فريق", 2 },
                    { 3, null, true, "عميد", 3 },
                    { 4, null, true, "عقيد", 4 },
                    { 5, null, true, "مقدم", 5 },
                    { 6, null, true, "رائد", 6 },
                    { 7, null, true, "نقيب", 7 },
                    { 8, null, true, "ملازم أول", 8 },
                    { 9, null, true, "ملازم", 9 },
                    { 10, null, true, "رئيس رقباء", 10 },
                    { 11, null, true, "رقيب أول", 11 },
                    { 12, null, true, "رقيب", 12 },
                    { 13, null, true, "عريف أول", 13 },
                    { 14, null, true, "عريف", 14 },
                    { 15, null, true, "جندي أول", 15 },
                    { 16, null, true, "جندي", 16 }
                });

            migrationBuilder.InsertData(
                table: "Universities",
                columns: new[] { "Id", "IsActive", "Location", "Name", "Type" },
                values: new object[,]
                {
                    { 1, true, "بغداد", "جامعة بغداد", 1 },
                    { 2, true, "بغداد", "الجامعة المستنصرية", 1 },
                    { 3, true, "البصرة", "جامعة البصرة", 1 },
                    { 4, true, "نينوى", "جامعة الموصل", 1 },
                    { 5, true, "النجف", "جامعة الكوفة", 1 },
                    { 6, true, "كربلاء", "جامعة كربلاء", 1 },
                    { 7, true, "بابل", "جامعة بابل", 1 }
                });
        }
    }
}
