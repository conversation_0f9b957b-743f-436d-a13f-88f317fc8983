using System.ComponentModel.DataAnnotations;

namespace EmpMgr.Configuration
{
    public class SecurityOptions
    {
        public const string SectionName = "Security";

        [Required]
        [MinLength(16)]
        public string EncryptionKey { get; set; } = string.Empty;

        [Required]
        [MinLength(8)]
        public string HashSalt { get; set; } = string.Empty;

        [Range(1024, 104857600)] // 1KB to 100MB
        public long MaxFileSize { get; set; } = 52428800; // 50MB

        public string[] AllowedImageTypes { get; set; } = Array.Empty<string>();
        public string[] AllowedDocumentTypes { get; set; } = Array.Empty<string>();

        [Range(5, 480)] // 5 minutes to 8 hours
        public int SessionTimeout { get; set; } = 30;

        [Range(3, 10)]
        public int MaxLoginAttempts { get; set; } = 5;

        [Range(5, 1440)] // 5 minutes to 24 hours
        public int LockoutDuration { get; set; } = 30;

        public bool RequireHttps { get; set; } = true;
        public bool EnableCSP { get; set; } = true;
        public bool EnableHSTS { get; set; } = true;

        public RateLimitingOptions RateLimiting { get; set; } = new();
        public PasswordPolicyOptions PasswordPolicy { get; set; } = new();
    }

    public class RateLimitingOptions
    {
        [Range(10, 1000)]
        public int RequestsPerMinute { get; set; } = 60;

        [Range(100, 10000)]
        public int RequestsPerHour { get; set; } = 1000;
    }

    public class PasswordPolicyOptions
    {
        public bool RequireDigit { get; set; } = true;
        public bool RequireLowercase { get; set; } = true;
        public bool RequireUppercase { get; set; } = true;
        public bool RequireNonAlphanumeric { get; set; } = true;

        [Range(6, 128)]
        public int RequiredLength { get; set; } = 8;

        [Range(1, 10)]
        public int RequiredUniqueChars { get; set; } = 4;
    }
}
