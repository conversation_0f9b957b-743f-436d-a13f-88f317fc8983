@model EmpMgr.ViewModels.EmployeeCreateViewModel
@using EmpMgr.Models
@{
    ViewData["Title"] = "إضافة موظف جديد";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة موظف جديد
                    </h4>
                </div>

                <div class="card-body">
                    @using (Html.BeginForm("Create", "Employee", FormMethod.Post, new { enctype = "multipart/form-data", id = "employeeForm" }))
                    {
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>

                        <!-- الصورة الشخصية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-camera me-2"></i>
                                    الصورة الشخصية
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                @{
                                    ViewBag.HasCurrentPhoto = false;
                                    ViewBag.CurrentPhotoBase64 = "";
                                }
                                @await Html.PartialAsync("_PhotoUpload")
                            </div>
                        </div>

                        <!-- التبويبات المحسنة -->
                        <ul class="nav nav-tabs enhanced-tabs" id="employeeTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="basic-info-tab" data-bs-toggle="tab"
                                        data-bs-target="#basic-info" type="button" role="tab">
                                    <div class="tab-icon-wrapper">
                                        <i class="fas fa-id-card text-primary"></i>
                                    </div>
                                    <div class="tab-content-wrapper">
                                        <span class="tab-title">المعلومات الأساسية</span>
                                        <small class="tab-subtitle">البيانات الشخصية والعنوان</small>
                                    </div>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="job-info-tab" data-bs-toggle="tab"
                                        data-bs-target="#job-info" type="button" role="tab">
                                    <div class="tab-icon-wrapper">
                                        <i class="fas fa-briefcase text-success"></i>
                                    </div>
                                    <div class="tab-content-wrapper">
                                        <span class="tab-title">البيانات الوظيفية</span>
                                        <small class="tab-subtitle">المنصب والرتبة والتعليم</small>
                                    </div>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="promotion-allowance-tab" data-bs-toggle="tab"
                                        data-bs-target="#promotion-allowance" type="button" role="tab">
                                    <div class="tab-icon-wrapper">
                                        <i class="fas fa-chart-line text-warning"></i>
                                    </div>
                                    <div class="tab-content-wrapper">
                                        <span class="tab-title">الترقية والعلاوة</span>
                                        <small class="tab-subtitle">التعيين والترقيات والعلاوات</small>
                                    </div>
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content mt-3" id="employeeTabsContent">
                            <!-- المعلومات الأساسية -->
                            <div class="tab-pane fade show active" id="basic-info" role="tabpanel">
                                <div class="row">
                                    <!-- البيانات الشخصية -->
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">البيانات الشخصية</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6 mb-3">
                                                        <label asp-for="EmployeeNumber" class="form-label"></label>
                                                        <input asp-for="EmployeeNumber" class="form-control" readonly>
                                                        <span asp-validation-for="EmployeeNumber" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-6 mb-3">
                                                        <label asp-for="StatisticalNumber" class="form-label"></label>
                                                        <input asp-for="StatisticalNumber" class="form-control"
                                                               placeholder="أدخل الرقم الإحصائي"
                                                               pattern="[0-9]*"
                                                               title="يُسمح بالأرقام فقط"
                                                               oninput="this.value = this.value.replace(/[^0-9]/g, '')">
                                                        <span asp-validation-for="StatisticalNumber" class="text-danger"></span>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6 mb-3">
                                                        <label asp-for="RankId" class="form-label"></label>
                                                        <select asp-for="RankId" class="form-select" id="RankId">
                                                            <option value="">اختر الرتبة</option>
                                                            @foreach (var rank in Model.Ranks)
                                                            {
                                                                <option value="@rank.Id"
                                                                        data-rank-duration="@rank.RankDurationYears">
                                                                    @rank.Name
                                                                </option>
                                                            }
                                                        </select>
                                                        <span asp-validation-for="RankId" class="text-danger"></span>
                                                        <!-- رابط إضافة رتبة جديدة -->
                                                        <div class="mt-2">
                                                            <a href="@Url.Action("CreateRank", "Constants")"
                                                               target="_blank"
                                                               class="btn btn-link btn-sm text-decoration-none p-0">
                                                                <i class="fas fa-plus-circle me-1"></i>
                                                                إضافة رتبة جديدة
                                                            </a>
                                                        </div>

                                                    </div>
                                                </div>

                                                <!-- الاسم الكامل -->
                                                <div class="row">
                                                    <div class="col-md-6 mb-3">
                                                        <label asp-for="FirstName" class="form-label"></label>
                                                        <input asp-for="FirstName" class="form-control name-field"
                                                               placeholder="أدخل الاسم"
                                                               pattern="[أ-يa-zA-Z\s]*"
                                                               title="يُسمح بالأحرف العربية والإنجليزية والمسافات فقط"
                                                               oninput="validateNameField(this)">
                                                        <span asp-validation-for="FirstName" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-6 mb-3">
                                                        <label asp-for="FatherName" class="form-label"></label>
                                                        <input asp-for="FatherName" class="form-control name-field"
                                                               placeholder="أدخل اسم الأب"
                                                               pattern="[أ-يa-zA-Z\s]*"
                                                               title="يُسمح بالأحرف العربية والإنجليزية والمسافات فقط"
                                                               oninput="validateNameField(this)">
                                                        <span asp-validation-for="FatherName" class="text-danger"></span>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-4 mb-3">
                                                        <label asp-for="GrandFatherName" class="form-label"></label>
                                                        <input asp-for="GrandFatherName" class="form-control name-field"
                                                               placeholder="أدخل اسم الجد"
                                                               pattern="[أ-يa-zA-Z\s]*"
                                                               title="يُسمح بالأحرف العربية والإنجليزية والمسافات فقط"
                                                               oninput="validateNameField(this)">
                                                        <span asp-validation-for="GrandFatherName" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-4 mb-3">
                                                        <label asp-for="GreatGrandFatherName" class="form-label"></label>
                                                        <input asp-for="GreatGrandFatherName" class="form-control name-field"
                                                               placeholder="أدخل اسم والد الجد"
                                                               pattern="[أ-يa-zA-Z\s]*"
                                                               title="يُسمح بالأحرف العربية والإنجليزية والمسافات فقط"
                                                               oninput="validateNameField(this)">
                                                        <span asp-validation-for="GreatGrandFatherName" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-4 mb-3">
                                                        <label asp-for="LastName" class="form-label"></label>
                                                        <input asp-for="LastName" class="form-control name-field"
                                                               placeholder="أدخل اللقب"
                                                               pattern="[أ-يa-zA-Z\s]*"
                                                               title="يُسمح بالأحرف العربية والإنجليزية والمسافات فقط"
                                                               oninput="validateNameField(this)">
                                                        <span asp-validation-for="LastName" class="text-danger"></span>
                                                    </div>
                                                </div>

                                                <!-- البيانات الشخصية الأخرى -->
                                                <div class="row">
                                                    <div class="col-md-3 mb-3">
                                                        <label asp-for="MaritalStatus" class="form-label"></label>
                                                        <select asp-for="MaritalStatus" class="form-select" asp-items="Html.GetEnumSelectList<MaritalStatus>()">
                                                            <option value="">اختر الحالة الاجتماعية</option>
                                                        </select>
                                                        <span asp-validation-for="MaritalStatus" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-3 mb-3">
                                                        <label asp-for="Gender" class="form-label"></label>
                                                        <select asp-for="Gender" class="form-select" asp-items="Html.GetEnumSelectList<Gender>()">
                                                            <option value="">اختر الجنس</option>
                                                        </select>
                                                        <span asp-validation-for="Gender" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-3 mb-3">
                                                        <label asp-for="BloodType" class="form-label"></label>
                                                        <select asp-for="BloodType" class="form-select" asp-items="Html.GetEnumSelectList<BloodType>()">
                                                            <option value="">اختر فصيلة الدم</option>
                                                        </select>
                                                        <span asp-validation-for="BloodType" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-3 mb-3">
                                                        <label asp-for="HealthStatus" class="form-label"></label>
                                                        <select asp-for="HealthStatus" class="form-select" asp-items="Html.GetEnumSelectList<HealthStatus>()">
                                                            <option value="">اختر الحالة الصحية</option>
                                                        </select>
                                                        <span asp-validation-for="HealthStatus" class="text-danger"></span>
                                                    </div>
                                                </div>

                                                <!-- محل الولادة وتاريخ الولادة -->
                                                <div class="row">
                                                    <div class="col-md-4 mb-3">
                                                        <label asp-for="PlaceOfBirth" class="form-label"></label>
                                                        <input asp-for="PlaceOfBirth" class="form-control"
                                                               placeholder="أدخل محل الولادة" />
                                                        <span asp-validation-for="PlaceOfBirth" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-4 mb-3">
                                                        <label asp-for="DateOfBirth" class="form-label"></label>
                                                        <input asp-for="DateOfBirth" type="date" class="form-control"
                                                               max="@DateTime.Now.ToString("yyyy-MM-dd")"
                                                               min="1940-01-01" id="dateOfBirth" />
                                                        <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-4 mb-3">
                                                        <label class="form-label">
                                                            <i class="fas fa-calendar-alt me-1"></i>العمر
                                                        </label>
                                                        <input type="text" class="form-control" id="calculatedAge"
                                                               readonly placeholder="سيتم حساب العمر تلقائياً"
                                                               style="background-color: #f8f9fa; border: 2px dashed #dee2e6; font-weight: 500;" />
                                                        <small class="text-muted">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            يتم حساب العمر تلقائياً عند اختيار تاريخ الولادة
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- معلومات الاتصال والعنوان -->
                                        <div class="card mt-3">
                                            <div class="card-header">
                                                <h6 class="mb-0">معلومات الاتصال والعنوان السكن</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3 mb-3">
                                                        <label asp-for="ProvinceId" class="form-label"></label>
                                                        <select asp-for="ProvinceId" class="form-select">
                                                            <option value="">اختر المحافظة</option>
                                                            @foreach (var province in Model.Provinces)
                                                            {
                                                                <option value="@province.Id">@province.Name</option>
                                                            }
                                                        </select>
                                                        <span asp-validation-for="ProvinceId" class="text-danger"></span>
                                                        <!-- رابط إضافة محافظة جديدة -->
                                                        <div class="mt-2">
                                                            <a href="@Url.Action("CreateProvince", "Constants")"
                                                               target="_blank"
                                                               class="btn btn-link btn-sm text-decoration-none p-0">
                                                                <i class="fas fa-plus-circle me-1"></i>
                                                                إضافة محافظة جديدة
                                                            </a>
                                                        </div>

                                                    </div>
                                                    <div class="col-md-3 mb-3">
                                                        <label asp-for="District" class="form-label"></label>
                                                        <input asp-for="District" class="form-control" placeholder="أدخل القضاء">
                                                        <span asp-validation-for="District" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-3 mb-3">
                                                        <label asp-for="Subdistrict" class="form-label"></label>
                                                        <input asp-for="Subdistrict" class="form-control" placeholder="أدخل الناحية">
                                                        <span asp-validation-for="Subdistrict" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-3 mb-3">
                                                        <label asp-for="Village" class="form-label"></label>
                                                        <input asp-for="Village" class="form-control" placeholder="أدخل القرية">
                                                        <span asp-validation-for="Village" class="text-danger"></span>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-3 mb-3">
                                                        <label asp-for="Neighborhood" class="form-label"></label>
                                                        <input asp-for="Neighborhood" class="form-control" placeholder="أدخل الحي">
                                                        <span asp-validation-for="Neighborhood" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-3 mb-3">
                                                        <label asp-for="Quarter" class="form-label"></label>
                                                        <input asp-for="Quarter" class="form-control" placeholder="أدخل المحلة">
                                                        <span asp-validation-for="Quarter" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-3 mb-3">
                                                        <label asp-for="Alley" class="form-label"></label>
                                                        <input asp-for="Alley" class="form-control" placeholder="أدخل الزقاق">
                                                        <span asp-validation-for="Alley" class="text-danger"></span>
                                                    </div>
                                                    <div class="col-md-3 mb-3">
                                                        <label asp-for="House" class="form-label"></label>
                                                        <input asp-for="House" class="form-control" placeholder="أدخل الدار">
                                                        <span asp-validation-for="House" class="text-danger"></span>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6 mb-3">
                                                        <label asp-for="NearestLandmark" class="form-label"></label>
                                                        <input asp-for="NearestLandmark" class="form-control"
                                                               placeholder="أدخل أقرب نقطة دالة">
                                                        <span asp-validation-for="NearestLandmark" class="text-danger"></span>
                                                    </div>

                                                    <div class="col-md-12 mb-3">
                                                        <label asp-for="FullAddress" class="form-label fw-bold text-primary">
                                                            <i class="fas fa-map-marker-alt me-1"></i>العنوان الكامل
                                                        </label>
                                                        <textarea asp-for="FullAddress" class="form-control" id="fullAddress" rows="3"
                                                                  readonly placeholder="سيتم تكوين العنوان تلقائياً عند ملء حقول العنوان"
                                                                  style="resize: vertical; min-height: 80px; background-color: #f8f9fa; border: 2px dashed #dee2e6; font-weight: 500; font-size: 14px;"></textarea>
                                                        <span asp-validation-for="FullAddress" class="text-danger"></span>
                                                        <small class="text-muted">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            يتم تكوين العنوان تلقائياً بالتنسيق: "محافظة [المحافظة] - قضاء [القضاء] - ناحية [الناحية] - قرية [القرية] - حي [الحي] - محلة [المحلة] - زقاق [الزقاق] - دار [الدار] - قرب [أقرب نقطة دالة]"
                                                        </small>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- البيانات الوظيفية -->
                            <div class="tab-pane fade" id="job-info" role="tabpanel">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">التحصيل الدراسي</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4 mb-3">
                                                <label asp-for="EducationLevel" class="form-label"></label>
                                                <select asp-for="EducationLevel" class="form-select" id="EducationLevel"
                                                        asp-items="Html.GetEnumSelectList<EducationLevel>()"
                                                        onchange="console.log('onchange triggered'); toggleEducationFields();">
                                                    <option value="">اختر التحصيل الدراسي</option>
                                                </select>
                                                <span asp-validation-for="EducationLevel" class="text-danger"></span>
                                            </div>

                                            <!-- حقول الجامعة والكلية (للبكالوريوس وما فوق) -->
                                            <div class="col-md-4 mb-3" id="universityField" style="display: none;">
                                                <label asp-for="UniversityId" class="form-label"></label>
                                                <select asp-for="UniversityId" class="form-select" id="UniversityId" onchange="loadColleges()">
                                                    <option value="">اختر الجامعة</option>
                                                    @foreach (var university in Model.Universities)
                                                    {
                                                        <option value="@university.Id">@university.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="UniversityId" class="text-danger"></span>
                                                <!-- رابط إضافة جامعة جديدة -->
                                                <div class="mt-2">
                                                    <a href="@Url.Action("CreateUniversity", "Constants")"
                                                       target="_blank"
                                                       class="btn btn-link btn-sm text-decoration-none p-0">
                                                        <i class="fas fa-plus-circle me-1"></i>
                                                        إضافة جامعة جديدة
                                                    </a>
                                                </div>
                                            </div>

                                            <div class="col-md-4 mb-3" id="collegeField" style="display: none;">
                                                <label asp-for="CollegeId" class="form-label"></label>
                                                <select asp-for="CollegeId" class="form-select" id="CollegeId">
                                                    <option value="">اختر الكلية</option>
                                                </select>
                                                <span asp-validation-for="CollegeId" class="text-danger"></span>
                                                <!-- رابط إضافة كلية جديدة -->
                                                <div class="mt-2">
                                                    <a href="@Url.Action("CreateCollege", "Constants")"
                                                       target="_blank"
                                                       class="btn btn-link btn-sm text-decoration-none p-0">
                                                        <i class="fas fa-plus-circle me-1"></i>
                                                        إضافة كلية جديدة
                                                    </a>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- حقول المعهد والقسم (للدبلوم) -->
                                        <div class="row">
                                            <div class="col-md-4 mb-3" id="instituteField" style="display: none;">
                                                <label asp-for="InstituteId" class="form-label"></label>
                                                <select asp-for="InstituteId" class="form-select" id="InstituteId" onchange="loadDepartments()">
                                                    <option value="">اختر المعهد</option>
                                                    @foreach (var institute in Model.Institutes)
                                                    {
                                                        <option value="@institute.Id">@institute.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="InstituteId" class="text-danger"></span>
                                                <!-- رابط إضافة معهد جديد -->
                                                <div class="mt-2">
                                                    <a href="@Url.Action("CreateInstitute", "Constants")"
                                                       target="_blank"
                                                       class="btn btn-link btn-sm text-decoration-none p-0">
                                                        <i class="fas fa-plus-circle me-1"></i>
                                                        إضافة معهد جديد
                                                    </a>
                                                </div>
                                            </div>

                                            <div class="col-md-4 mb-3" id="departmentField" style="display: none;">
                                                <label asp-for="DepartmentId" class="form-label"></label>
                                                <select asp-for="DepartmentId" class="form-select" id="DepartmentId">
                                                    <option value="">اختر القسم</option>
                                                </select>
                                                <span asp-validation-for="DepartmentId" class="text-danger"></span>
                                                <!-- رابط إضافة قسم جديد -->
                                                <div class="mt-2">
                                                    <a href="@Url.Action("CreateDepartment", "Constants")"
                                                       target="_blank"
                                                       class="btn btn-link btn-sm text-decoration-none p-0">
                                                        <i class="fas fa-plus-circle me-1"></i>
                                                        إضافة قسم جديد
                                                    </a>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-12 mb-3" id="specializationField" style="display: none;">
                                                <label asp-for="Specialization" class="form-label"></label>
                                                <input asp-for="Specialization" class="form-control" id="Specialization" placeholder="أدخل التخصص" />
                                                <span asp-validation-for="Specialization" class="text-danger"></span>
                                            </div>
                                        </div>


                                    </div>
                                </div>

                                <!-- البنية الهرمية الحكومية -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">البنية الهرمية الحكومية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="MinistryId" class="form-label"></label>
                                                <select asp-for="MinistryId" class="form-select" onchange="loadAgencies()">
                                                    <option value="">اختر الوزارة</option>
                                                    @foreach (var ministry in Model.Ministries)
                                                    {
                                                        <option value="@ministry.Id">@ministry.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="MinistryId" class="text-danger"></span>
                                                <!-- رابط إضافة وزارة جديدة -->
                                                <div class="mt-2">
                                                    <a href="@Url.Action("CreateMinistry", "Constants")"
                                                       target="_blank"
                                                       class="btn btn-link btn-sm text-decoration-none p-0">
                                                        <i class="fas fa-plus-circle me-1"></i>
                                                        إضافة وزارة جديدة
                                                    </a>
                                                </div>

                                            </div>

                                            <div class="col-md-3 mb-3">
                                                <label asp-for="AgencyId" class="form-label"></label>
                                                <select asp-for="AgencyId" class="form-select" onchange="loadDirectorates()" disabled>
                                                    <option value="">اختر الوكالة</option>
                                                </select>
                                                <span asp-validation-for="AgencyId" class="text-danger"></span>
                                                <!-- رابط إضافة وكالة جديدة -->
                                                <div class="mt-2">
                                                    <a href="@Url.Action("CreateAgency", "Constants")"
                                                       target="_blank"
                                                       class="btn btn-link btn-sm text-decoration-none p-0">
                                                        <i class="fas fa-plus-circle me-1"></i>
                                                        إضافة وكالة جديدة
                                                    </a>
                                                </div>

                                            </div>

                                            <div class="col-md-3 mb-3">
                                                <label asp-for="DirectorateId" class="form-label"></label>
                                                <select asp-for="DirectorateId" class="form-select" onchange="loadGovernmentDepartments()" disabled>
                                                    <option value="">اختر المديرية</option>
                                                </select>
                                                <span asp-validation-for="DirectorateId" class="text-danger"></span>
                                                <!-- رابط إضافة مديرية جديدة -->
                                                <div class="mt-2">
                                                    <a href="@Url.Action("CreateDirectorate", "Constants")"
                                                       target="_blank"
                                                       class="btn btn-link btn-sm text-decoration-none p-0">
                                                        <i class="fas fa-plus-circle me-1"></i>
                                                        إضافة مديرية جديدة
                                                    </a>
                                                </div>

                                            </div>

                                            <div class="col-md-3 mb-3">
                                                <label asp-for="GovernmentDepartmentId" class="form-label"></label>
                                                <select asp-for="GovernmentDepartmentId" class="form-select" onchange="loadDivisions()" disabled>
                                                    <option value="">اختر القسم الحكومي</option>
                                                </select>
                                                <span asp-validation-for="GovernmentDepartmentId" class="text-danger"></span>
                                                <!-- رابط إضافة قسم حكومي جديد -->
                                                <div class="mt-2">
                                                    <a href="@Url.Action("CreateGovernmentDepartment", "Constants")"
                                                       target="_blank"
                                                       class="btn btn-link btn-sm text-decoration-none p-0">
                                                        <i class="fas fa-plus-circle me-1"></i>
                                                        إضافة قسم حكومي جديد
                                                    </a>
                                                </div>

                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="DivisionId" class="form-label"></label>
                                                <select asp-for="DivisionId" class="form-select" disabled>
                                                    <option value="">اختر الشعبة</option>
                                                </select>
                                                <span asp-validation-for="DivisionId" class="text-danger"></span>
                                                <!-- رابط إضافة شعبة جديدة -->
                                                <div class="mt-2">
                                                    <a href="@Url.Action("CreateDivision", "Constants")"
                                                       target="_blank"
                                                       class="btn btn-link btn-sm text-decoration-none p-0">
                                                        <i class="fas fa-plus-circle me-1"></i>
                                                        إضافة شعبة جديدة
                                                    </a>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- بيانات الترقية والعلاوة -->
                            <div class="tab-pane fade" id="promotion-allowance" role="tabpanel">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-chart-line me-2"></i>
                                            بيانات الترقية والعلاوة
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- بيانات التعيين -->
                                        <div class="row mb-4">
                                            <div class="col-12">
                                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                                    <i class="fas fa-user-plus me-2"></i>
                                                    بيانات التعيين
                                                </h6>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <!-- أمر التعيين -->
                                            <div class="col-md-3 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-file-contract me-1"></i>
                                                    رقم أمر التعيين
                                                </label>
                                                <input type="text" class="form-control"
                                                       placeholder="أدخل رقم أمر التعيين" />
                                                <small class="text-muted">رقم الأمر الإداري للتعيين</small>
                                            </div>

                                            <!-- تاريخ التعيين -->
                                            <div class="col-md-3 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-calendar-plus me-1"></i>
                                                    تاريخ التعيين
                                                </label>
                                                <input type="date" class="form-control" id="appointmentDate"
                                                       max="@DateTime.Now.ToString("yyyy-MM-dd")"
                                                       min="1980-01-01"
                                                       placeholder="اختر تاريخ التعيين" />
                                                <small class="text-muted">تاريخ بداية الخدمة الرسمية</small>
                                            </div>

                                            <!-- مدة الخدمة -->
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-clock me-1"></i>
                                                    مدة الخدمة
                                                </label>
                                                <input type="text" class="form-control" id="serviceLength"
                                                       readonly placeholder="سيتم حساب مدة الخدمة تلقائياً"
                                                       style="background-color: #f8f9fa; border: 2px dashed #dee2e6; font-weight: 500; color: #0d6efd;" />
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    يتم حساب مدة الخدمة تلقائياً من تاريخ التعيين حتى اليوم
                                                </small>
                                            </div>
                                        </div>

                                        <!-- بيانات الترقية -->
                                        <div class="row mb-4 mt-4">
                                            <div class="col-12">
                                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                                    <i class="fas fa-arrow-up me-2"></i>
                                                    بيانات الترقية
                                                </h6>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <!-- تاريخ التعيين -->
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-calendar-plus me-1 text-primary"></i>
                                                    تاريخ التعيين
                                                </label>
                                                <input asp-for="AppointmentDate" type="date" class="form-control" id="AppointmentDate"
                                                       max="@DateTime.Now.ToString("yyyy-MM-dd")"
                                                       value="@DateTime.Today.ToString("yyyy-MM-dd")"
                                                       required />
                                                <span asp-validation-for="AppointmentDate" class="text-danger"></span>
                                            </div>

                                            <!-- تاريخ آخر ترقية -->
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-arrow-up me-1"></i>
                                                    تاريخ آخر ترقية
                                                </label>
                                                <input asp-for="LastPromotionDate" type="date" class="form-control" id="lastPromotionDate"
                                                       max="@DateTime.Now.ToString("yyyy-MM-dd")"
                                                       placeholder="اختر تاريخ آخر ترقية" />
                                                <small class="text-muted">اتركه فارغاً إذا لم يحصل على ترقية</small>
                                            </div>

                                            <!-- تاريخ استحقاق الترقية المحسوب -->
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-calendar-check me-1 text-success"></i>
                                                    تاريخ استحقاق الترقية
                                                </label>
                                                <input asp-for="CalculatedPromotionDate" type="date" class="form-control" id="calculatedPromotionDate"
                                                       readonly style="background-color: #f8f9fa;" />
                                                <small class="text-muted">يحسب تلقائياً بناءً على البيانات المدخلة</small>
                                            </div>

                                            <!-- الرتبة السابقة -->
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-history me-1"></i>
                                                    الرتبة السابقة
                                                </label>
                                                <select class="form-select">
                                                    <option value="">اختر الرتبة السابقة</option>
                                                    @foreach (var rank in Model.Ranks)
                                                    {
                                                        <option value="@rank.Id">@rank.Name</option>
                                                    }
                                                </select>
                                            </div>
                                        </div>

                                        <!-- بيانات العلاوة -->
                                        <div class="row mb-4 mt-4">
                                            <div class="col-12">
                                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                                    <i class="fas fa-money-bill-wave me-2"></i>
                                                    بيانات العلاوة
                                                </h6>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <!-- تاريخ آخر علاوة -->
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-money-bill-wave me-1"></i>
                                                    تاريخ آخر علاوة
                                                </label>
                                                <input type="date" class="form-control"
                                                       max="@DateTime.Now.ToString("yyyy-MM-dd")"
                                                       placeholder="اختر تاريخ آخر علاوة" />
                                                <small class="text-muted">اتركه فارغاً إذا لم يحصل على علاوة</small>
                                            </div>

                                            <!-- مقدار آخر علاوة -->
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-coins me-1"></i>
                                                    مقدار آخر علاوة (دينار)
                                                </label>
                                                <input type="number" class="form-control"
                                                       min="0" step="1000"
                                                       placeholder="أدخل مقدار العلاوة" />
                                            </div>

                                            <!-- نوع آخر علاوة -->
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-tag me-1"></i>
                                                    نوع آخر علاوة
                                                </label>
                                                <select class="form-select">
                                                    <option value="">اختر نوع العلاوة</option>
                                                    <option value="annual">علاوة سنوية</option>
                                                    <option value="performance">علاوة أداء</option>
                                                    <option value="special">علاوة خاصة</option>
                                                    <option value="promotion">علاوة ترقية</option>
                                                    <option value="qualification">علاوة مؤهل</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- التواريخ المستقبلية -->
                                        <div class="row mb-4 mt-4">
                                            <div class="col-12">
                                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                                    <i class="fas fa-calendar-alt me-2"></i>
                                                    التواريخ المستقبلية
                                                </h6>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <!-- تاريخ استحقاق الترقية القادمة -->
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-calendar-plus me-1"></i>
                                                    تاريخ استحقاق الترقية القادمة
                                                </label>
                                                <input type="date" class="form-control"
                                                       min="@DateTime.Now.ToString("yyyy-MM-dd")"
                                                       placeholder="تاريخ الترقية المتوقعة" />
                                                <small class="text-muted">تاريخ متوقع للترقية القادمة</small>
                                            </div>

                                            <!-- تاريخ استحقاق العلاوة القادمة -->
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-calendar-check me-1"></i>
                                                    تاريخ استحقاق العلاوة القادمة
                                                </label>
                                                <input type="date" class="form-control"
                                                       min="@DateTime.Now.ToString("yyyy-MM-dd")"
                                                       placeholder="تاريخ العلاوة المتوقعة" />
                                                <small class="text-muted">تاريخ متوقع للعلاوة القادمة</small>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <!-- ملاحظات الترقية والعلاوة -->
                                            <div class="col-12 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-sticky-note me-1"></i>
                                                    ملاحظات الترقية والعلاوة
                                                </label>
                                                <textarea class="form-control" rows="3"
                                                          placeholder="أدخل أي ملاحظات متعلقة بالترقيات والعلاوات..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        العودة للقائمة
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ الموظف
                                    </button>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- تضمين ملف الحفظ التلقائي -->
    <script src="~/js/auto-save.js"></script>

    <script>




        // دالة إظهار رسالة النجاح
        function showSuccessMessage(message) {
            // إنشاء عنصر الرسالة
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // إضافة الرسالة إلى الصفحة
            document.body.appendChild(alertDiv);

            // إزالة الرسالة تلقائياً بعد 3 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }



        // ملاحظة: تمت إزالة وظائف الكاميرا. اختيار الصورة يتم فقط بالنقر على الصورة لفتح ملف.

        // دالة التحقق من صحة حقول الأسماء
        function validateNameField(input) {
            // إزالة الأرقام والرموز والعمليات الحسابية
            input.value = input.value.replace(/[^أ-يa-zA-Z\s]/g, '');
        }





        // تبديل حقول التعليم حسب المستوى
        function toggleEducationFields() {
            try {
                console.log('toggleEducationFields called');

                const educationLevel = document.getElementById('EducationLevel');
                if (!educationLevel) {
                    console.error('EducationLevel element not found');
                    return;
                }

                const selectedValue = educationLevel.value;
                console.log('Education level selected:', selectedValue);

                const universityField = document.getElementById('universityField');
                const collegeField = document.getElementById('collegeField');
                const instituteField = document.getElementById('instituteField');
                const departmentField = document.getElementById('departmentField');
                const specializationField = document.getElementById('specializationField');

                console.log('Fields found:', {
                    universityField: !!universityField,
                    collegeField: !!collegeField,
                    instituteField: !!instituteField,
                    departmentField: !!departmentField,
                    specializationField: !!specializationField
                });

                // إخفاء جميع الحقول أولاً
                if (universityField) universityField.style.display = 'none';
                if (collegeField) collegeField.style.display = 'none';
                if (instituteField) instituteField.style.display = 'none';
                if (departmentField) departmentField.style.display = 'none';
                if (specializationField) specializationField.style.display = 'none';

                // مسح القيم
                const universitySelect = document.getElementById('UniversityId');
                const collegeSelect = document.getElementById('CollegeId');
                const instituteSelect = document.getElementById('InstituteId');
                const departmentSelect = document.getElementById('DepartmentId');
                const specializationInput = document.getElementById('Specialization');

                if (universitySelect) universitySelect.value = '';
                if (collegeSelect) collegeSelect.innerHTML = '<option value="">اختر الكلية</option>';
                if (instituteSelect) instituteSelect.value = '';
                if (departmentSelect) departmentSelect.innerHTML = '<option value="">اختر القسم</option>';
                if (specializationInput) specializationInput.value = '';

                // إظهار الحقول المناسبة حسب المستوى التعليمي
                console.log('Checking education level:', selectedValue);

                if (selectedValue === '5' || selectedValue === '6' || selectedValue === '7') {
                    // بكالوريوس، ماجستير، دكتوراه - إظهار حقول الجامعة
                    console.log('University level detected, showing university fields');
                    if (universityField) {
                        universityField.style.display = 'block';
                        console.log('University field shown');
                    }
                    if (collegeField) {
                        collegeField.style.display = 'block';
                        console.log('College field shown');
                    }
                    if (specializationField) {
                        specializationField.style.display = 'block';
                        console.log('Specialization field shown');
                    }
                } else if (selectedValue === '4') {
                    // دبلوم - إظهار حقول المعهد
                    console.log('Diploma level detected, showing institute fields');
                    if (instituteField) {
                        instituteField.style.display = 'block';
                        console.log('Institute field shown');
                    }
                    if (departmentField) {
                        departmentField.style.display = 'block';
                        console.log('Department field shown');
                    }
                    if (specializationField) {
                        specializationField.style.display = 'block';
                        console.log('Specialization field shown');
                    }
                } else {
                    console.log('Other education level, fields hidden');
                }
            } catch (error) {
                console.error('Error in toggleEducationFields:', error);
            }
        }

        // تحميل الكليات حسب الجامعة المختارة
        async function loadColleges() {
            const universityId = document.getElementById('UniversityId').value;
            const collegeSelect = document.getElementById('CollegeId');

            // مسح الكليات الحالية
            collegeSelect.innerHTML = '<option value="">اختر الكلية</option>';

            if (universityId) {
                try {
                    const response = await fetch(`/Employee/GetColleges?universityId=${universityId}`);
                    if (response.ok) {
                        const colleges = await response.json();
                        colleges.forEach(college => {
                            const option = document.createElement('option');
                            option.value = college.id;
                            option.textContent = college.name;
                            collegeSelect.appendChild(option);
                        });
                    }
                } catch (error) {
                    console.error('خطأ في تحميل الكليات:', error);
                }
            }
        }

        // تحميل الأقسام حسب المعهد المختار
        async function loadDepartments() {
            const instituteId = document.getElementById('InstituteId').value;
            const departmentSelect = document.getElementById('DepartmentId');

            // مسح الأقسام الحالية
            departmentSelect.innerHTML = '<option value="">اختر القسم</option>';

            if (instituteId) {
                try {
                    const response = await fetch(`/Constants/GetDepartmentsByInstitute?instituteId=${instituteId}`);
                    if (response.ok) {
                        const departments = await response.json();
                        departments.forEach(department => {
                            const option = document.createElement('option');
                            option.value = department.id;
                            option.textContent = department.name;
                            departmentSelect.appendChild(option);
                        });
                        console.log(`تم تحميل ${departments.length} قسم للمعهد`);
                    } else {
                        console.error('فشل في تحميل الأقسام');
                    }
                } catch (error) {
                    console.error('خطأ في تحميل الأقسام:', error);
                }
            }
        }

        // حساب العمر بالسنوات والشهور والأيام
        function calculateAge() {
            try {
                const dateOfBirthField = document.getElementById('dateOfBirth');
                const calculatedAgeField = document.getElementById('calculatedAge');

                if (!dateOfBirthField || !calculatedAgeField) {
                    console.log('حقول تاريخ الولادة أو العمر غير موجودة');
                    return;
                }

                const birthDate = new Date(dateOfBirthField.value);
                const today = new Date();

                if (!dateOfBirthField.value || isNaN(birthDate.getTime())) {
                    calculatedAgeField.value = '';
                    console.log('تاريخ الولادة غير صحيح');
                    return;
                }

                // التحقق من أن تاريخ الولادة ليس في المستقبل
                if (birthDate > today) {
                    calculatedAgeField.value = 'تاريخ غير صحيح';
                    console.log('تاريخ الولادة في المستقبل');
                    return;
                }

                // حساب الفرق بالسنوات والشهور والأيام
                let years = today.getFullYear() - birthDate.getFullYear();
                let months = today.getMonth() - birthDate.getMonth();
                let days = today.getDate() - birthDate.getDate();

                // تعديل الحسابات إذا كانت الأيام سالبة
                if (days < 0) {
                    months--;
                    const lastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
                    days += lastMonth.getDate();
                }

                // تعديل الحسابات إذا كانت الشهور سالبة
                if (months < 0) {
                    years--;
                    months += 12;
                }

                // تكوين النص النهائي للعمر
                let ageText = '';
                if (years > 0) {
                    ageText += years + (years === 1 ? ' سنة' : ' سنة');
                }
                if (months > 0) {
                    if (ageText) ageText += ' و ';
                    ageText += months + (months === 1 ? ' شهر' : ' شهر');
                }
                if (days > 0) {
                    if (ageText) ageText += ' و ';
                    ageText += days + (days === 1 ? ' يوم' : ' يوم');
                }

                // إذا كان العمر أقل من يوم واحد
                if (!ageText) {
                    ageText = 'أقل من يوم';
                }

                calculatedAgeField.value = ageText;
                console.log('تم حساب العمر:', ageText);

            } catch (error) {
                console.error('خطأ في حساب العمر:', error);
                const calculatedAgeField = document.getElementById('calculatedAge');
                if (calculatedAgeField) {
                    calculatedAgeField.value = 'خطأ في الحساب';
                }
            }
        }

        // حساب مدة الخدمة بالسنوات والشهور والأيام
        function calculateServiceLength() {
            try {
                const appointmentDateField = document.getElementById('appointmentDate');
                const serviceLengthField = document.getElementById('serviceLength');

                if (!appointmentDateField || !serviceLengthField) {
                    console.log('حقول تاريخ التعيين أو مدة الخدمة غير موجودة');
                    return;
                }

                const appointmentDate = new Date(appointmentDateField.value);
                const today = new Date();

                if (!appointmentDateField.value || isNaN(appointmentDate.getTime())) {
                    serviceLengthField.value = '';
                    console.log('تاريخ التعيين غير صحيح');
                    return;
                }

                // التحقق من أن تاريخ التعيين ليس في المستقبل
                if (appointmentDate > today) {
                    serviceLengthField.value = 'تاريخ غير صحيح';
                    console.log('تاريخ التعيين في المستقبل');
                    return;
                }

                // حساب الفرق بالسنوات والشهور والأيام
                let years = today.getFullYear() - appointmentDate.getFullYear();
                let months = today.getMonth() - appointmentDate.getMonth();
                let days = today.getDate() - appointmentDate.getDate();

                // تعديل الحسابات إذا كانت الأيام سالبة
                if (days < 0) {
                    months--;
                    const lastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
                    days += lastMonth.getDate();
                }

                // تعديل الحسابات إذا كانت الشهور سالبة
                if (months < 0) {
                    years--;
                    months += 12;
                }

                // تكوين النص النهائي لمدة الخدمة
                let serviceText = '';
                if (years > 0) {
                    serviceText += years + (years === 1 ? ' سنة' : ' سنة');
                }
                if (months > 0) {
                    if (serviceText) serviceText += ' و ';
                    serviceText += months + (months === 1 ? ' شهر' : ' شهر');
                }
                if (days > 0) {
                    if (serviceText) serviceText += ' و ';
                    serviceText += days + (days === 1 ? ' يوم' : ' يوم');
                }

                // إذا كانت مدة الخدمة أقل من يوم واحد
                if (!serviceText) {
                    serviceText = 'أقل من يوم';
                }

                // إضافة نص "في الخدمة"
                serviceText += ' في الخدمة';

                serviceLengthField.value = serviceText;
                console.log('تم حساب مدة الخدمة:', serviceText);

            } catch (error) {
                console.error('خطأ في حساب مدة الخدمة:', error);
                const serviceLengthField = document.getElementById('serviceLength');
                if (serviceLengthField) {
                    serviceLengthField.value = 'خطأ في الحساب';
                }
            }
        }

        // دوال تحديث العنوان الكامل عند تغيير الحقول












        // تكوين العنوان الكامل تلقائياً بالتنسيق المطلوب
        function updateFullAddress() {
            try {
                console.log('تحديث العنوان الكامل...'); // للتتبع

                // جمع البيانات من جميع حقول العنوان
                const province = document.getElementById('ProvinceId');
                const districtField = document.getElementById('District');
                const subdistrictField = document.getElementById('Subdistrict');
                const villageField = document.getElementById('Village');
                const neighborhoodField = document.getElementById('Neighborhood');
                const quarterField = document.getElementById('Quarter');
                const alleyField = document.getElementById('Alley');
                const houseField = document.getElementById('House');
                const nearestLandmarkField = document.getElementById('NearestLandmark');

                let addressParts = [];

                // إضافة المحافظة
                if (province && province.selectedIndex > 0) {
                    const provinceName = province.options[province.selectedIndex].text;
                    addressParts.push('محافظة ' + provinceName);
                    console.log('المحافظة المختارة:', provinceName);
                }

                // إضافة القضاء
                if (districtField && districtField.value.trim()) {
                    const district = districtField.value.trim();
                    addressParts.push('قضاء ' + district);
                    console.log('القضاء المدخل:', district);
                }

                // إضافة الناحية
                if (subdistrictField && subdistrictField.value.trim()) {
                    const subdistrict = subdistrictField.value.trim();
                    addressParts.push('ناحية ' + subdistrict);
                    console.log('الناحية المدخلة:', subdistrict);
                }

                // إضافة القرية
                if (villageField && villageField.value.trim()) {
                    const village = villageField.value.trim();
                    addressParts.push('قرية ' + village);
                    console.log('القرية المدخلة:', village);
                }

                // إضافة الحي
                if (neighborhoodField && neighborhoodField.value.trim()) {
                    const neighborhood = neighborhoodField.value.trim();
                    addressParts.push('حي ' + neighborhood);
                    console.log('الحي المدخل:', neighborhood);
                }

                // إضافة المحلة
                if (quarterField && quarterField.value.trim()) {
                    const quarter = quarterField.value.trim();
                    addressParts.push('محلة ' + quarter);
                    console.log('المحلة المدخلة:', quarter);
                }

                // إضافة الزقاق
                if (alleyField && alleyField.value.trim()) {
                    const alley = alleyField.value.trim();
                    addressParts.push('زقاق ' + alley);
                    console.log('الزقاق المدخل:', alley);
                }

                // إضافة الدار
                if (houseField && houseField.value.trim()) {
                    const house = houseField.value.trim();
                    addressParts.push('دار ' + house);
                    console.log('الدار المدخلة:', house);
                }

                // إضافة أقرب نقطة دالة
                if (nearestLandmarkField && nearestLandmarkField.value.trim()) {
                    const landmark = nearestLandmarkField.value.trim();
                    addressParts.push('قرب ' + landmark);
                    console.log('أقرب نقطة دالة:', landmark);
                }

                // تكوين العنوان النهائي
                const fullAddress = addressParts.join(' - ');
                console.log('العنوان الكامل:', fullAddress);

                const fullAddressField = document.getElementById('fullAddress');
                if (fullAddressField) {
                    fullAddressField.value = fullAddress;

                    // إضافة تأثير بصري لإظهار التحديث
                    if (fullAddress) {
                        fullAddressField.style.backgroundColor = '#d4edda';
                        fullAddressField.style.borderColor = '#28a745';
                        fullAddressField.style.transition = 'all 0.3s ease';
                        setTimeout(() => {
                            fullAddressField.style.backgroundColor = '#f8f9fa';
                            fullAddressField.style.borderColor = '#dee2e6';
                        }, 1000);
                    } else {
                        // إذا كان العنوان فارغ، أعد التنسيق الأصلي
                        fullAddressField.style.backgroundColor = '#f8f9fa';
                        fullAddressField.style.borderColor = '#dee2e6';
                    }
                }
            } catch (error) {
                console.error('خطأ في تحديث العنوان الكامل:', error);
            }
        }

        // إضافة مستمعي الأحداث
        function initEmployeeCreate() {
            // إضافة مستمع لحقل التحصيل الدراسي
            const educationLevelSelect = document.getElementById('EducationLevel');
            if (educationLevelSelect) {
                educationLevelSelect.addEventListener('change', function() {
                    console.log('onchange triggered');
                    toggleEducationFields();
                });
            }

            // إضافة مستمعات الأحداث للسلسلة الهرمية
            const ministrySelect = document.getElementById('MinistryId');
            const agencySelect = document.getElementById('AgencyId');
            const directorateSelect = document.getElementById('DirectorateId');
            const departmentSelect = document.getElementById('GovernmentDepartmentId');

            if (ministrySelect) {
                ministrySelect.addEventListener('change', function() {
                    loadAgencies();
                });
            }

            if (agencySelect) {
                agencySelect.addEventListener('change', function() {
                    if (this.value) {
                        // تفعيل حقل المديرية مباشرة عند اختيار الوكالة
                        const directorateSelect = document.getElementById('DirectorateId');
                        directorateSelect.disabled = false;
                        directorateSelect.innerHTML = '<option value="">اختر المديرية</option>';
                        directorateSelect.classList.remove('disabled-cascade');
                        directorateSelect.classList.add('enabled');

                        loadDirectorates();
                    } else {
                        // إعادة تعيين الحقول التابعة
                        const directorateSelect = document.getElementById('DirectorateId');
                        const departmentSelect = document.getElementById('GovernmentDepartmentId');
                        const divisionSelect = document.getElementById('DivisionId');

                        directorateSelect.innerHTML = '<option value="">اختر المديرية أولاً</option>';
                        departmentSelect.innerHTML = '<option value="">اختر القسم الحكومي أولاً</option>';
                        divisionSelect.innerHTML = '<option value="">اختر الشعبة أولاً</option>';

                        directorateSelect.disabled = true;
                        departmentSelect.disabled = true;
                        divisionSelect.disabled = true;

                        directorateSelect.classList.add('disabled-cascade');
                        departmentSelect.classList.add('disabled-cascade');
                        divisionSelect.classList.add('disabled-cascade');
                    }
                });
            }

            if (directorateSelect) {
                directorateSelect.addEventListener('change', function() {
                    if (this.value) {
                        // تفعيل حقل القسم الحكومي مباشرة عند اختيار المديرية
                        const departmentSelect = document.getElementById('GovernmentDepartmentId');
                        departmentSelect.disabled = false;
                        departmentSelect.innerHTML = '<option value="">اختر القسم الحكومي</option>';
                        departmentSelect.classList.remove('disabled-cascade');
                        departmentSelect.classList.add('enabled');

                        loadGovernmentDepartments();
                    } else {
                        // إعادة تعيين الحقول التابعة
                        const departmentSelect = document.getElementById('GovernmentDepartmentId');
                        const divisionSelect = document.getElementById('DivisionId');

                        departmentSelect.innerHTML = '<option value="">اختر القسم الحكومي أولاً</option>';
                        divisionSelect.innerHTML = '<option value="">اختر الشعبة أولاً</option>';

                        departmentSelect.disabled = true;
                        divisionSelect.disabled = true;

                        departmentSelect.classList.add('disabled-cascade');
                        divisionSelect.classList.add('disabled-cascade');
                    }
                });
            }

            if (departmentSelect) {
                departmentSelect.addEventListener('change', function() {
                    if (this.value) {
                        // تفعيل حقل الشعبة مباشرة عند اختيار القسم الحكومي
                        const divisionSelect = document.getElementById('DivisionId');
                        divisionSelect.disabled = false;
                        divisionSelect.innerHTML = '<option value="">اختر الشعبة</option>';
                        divisionSelect.classList.remove('disabled-cascade');
                        divisionSelect.classList.add('enabled');

                        loadDivisions();
                    } else {
                        // إعادة تعيين الحقل التابع
                        const divisionSelect = document.getElementById('DivisionId');
                        divisionSelect.innerHTML = '<option value="">اختر الشعبة أولاً</option>';
                        divisionSelect.disabled = true;
                        divisionSelect.classList.add('disabled-cascade');
                    }
                });
            }



            // إعداد تحديث العنوان الكامل تلقائياً
            const provinceField = document.getElementById('ProvinceId');
            const districtField = document.getElementById('District');

            console.log('إعداد مستمعات أحداث العنوان الكامل...');
            console.log('حقل المحافظة:', provinceField ? 'موجود' : 'غير موجود');
            console.log('حقل القضاء:', districtField ? 'موجود' : 'غير موجود');

            if (provinceField) {
                provinceField.addEventListener('change', function() {
                    console.log('تغيير المحافظة - القيمة الجديدة:', this.value);
                    updateFullAddress();
                });
                console.log('تم إضافة مستمع تغيير المحافظة');
            }

            if (districtField) {
                districtField.addEventListener('input', function() {
                    console.log('كتابة في القضاء - القيمة الحالية:', this.value);
                    clearTimeout(this.addressTimeout);
                    this.addressTimeout = setTimeout(updateFullAddress, 300);
                });

                districtField.addEventListener('blur', function() {
                    console.log('فقدان التركيز من القضاء');
                    updateFullAddress();
                });

                // إضافة مستمع للضغط على Enter
                districtField.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        console.log('ضغط Enter في القضاء');
                        updateFullAddress();
                    }
                });

                console.log('تم إضافة مستمعات أحداث القضاء');
            }

            // إضافة مستمعات أحداث لباقي حقول العنوان
            const addressFields = [
                { id: 'Subdistrict', name: 'الناحية' },
                { id: 'Village', name: 'القرية' },
                { id: 'Neighborhood', name: 'الحي' },
                { id: 'Quarter', name: 'المحلة' },
                { id: 'Alley', name: 'الزقاق' },
                { id: 'House', name: 'الدار' },
                { id: 'NearestLandmark', name: 'أقرب نقطة دالة' }
            ];

            addressFields.forEach(field => {
                const fieldElement = document.getElementById(field.id);
                if (fieldElement) {
                    // مستمع للكتابة مع تأخير
                    fieldElement.addEventListener('input', function() {
                        console.log(`كتابة في ${field.name} - القيمة الحالية:`, this.value);
                        clearTimeout(this.addressTimeout);
                        this.addressTimeout = setTimeout(updateFullAddress, 300);
                    });

                    // مستمع لفقدان التركيز
                    fieldElement.addEventListener('blur', function() {
                        console.log(`فقدان التركيز من ${field.name}`);
                        updateFullAddress();
                    });

                    // مستمع للضغط على Enter
                    fieldElement.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            console.log(`ضغط Enter في ${field.name}`);
                            updateFullAddress();
                        }
                    });

                    console.log(`تم إضافة مستمعات أحداث ${field.name}`);
                }
            });

            // تحديث العنوان عند التحميل الأولي
            console.log('تحديث العنوان الأولي...');
            updateFullAddress();

            // تحديث حقول التعليم عند التحميل
            toggleEducationFields();

            // إضافة مستمع أحداث لحقل تاريخ الولادة لحساب العمر
            const dateOfBirthField = document.getElementById('dateOfBirth');
            if (dateOfBirthField) {
                // حساب العمر عند تغيير التاريخ
                dateOfBirthField.addEventListener('change', function() {
                    console.log('تغيير تاريخ الولادة:', this.value);
                    calculateAge();
                });

                // حساب العمر عند الكتابة
                dateOfBirthField.addEventListener('input', function() {
                    console.log('كتابة تاريخ الولادة:', this.value);
                    clearTimeout(this.ageTimeout);
                    this.ageTimeout = setTimeout(calculateAge, 300);
                });

                // حساب العمر عند فقدان التركيز
                dateOfBirthField.addEventListener('blur', function() {
                    console.log('فقدان التركيز من تاريخ الولادة');
                    calculateAge();
                });

                console.log('تم إضافة مستمعات أحداث تاريخ الولادة');
            }

            // إضافة مستمع أحداث لحقل تاريخ التعيين لحساب مدة الخدمة
            const appointmentDateField = document.getElementById('appointmentDate');
            if (appointmentDateField) {
                // حساب مدة الخدمة عند تغيير التاريخ
                appointmentDateField.addEventListener('change', function() {
                    console.log('تغيير تاريخ التعيين:', this.value);
                    calculateServiceLength();
                });

                // حساب مدة الخدمة عند الكتابة
                appointmentDateField.addEventListener('input', function() {
                    console.log('كتابة تاريخ التعيين:', this.value);
                    clearTimeout(this.serviceLengthTimeout);
                    this.serviceLengthTimeout = setTimeout(calculateServiceLength, 300);
                });

                // حساب مدة الخدمة عند فقدان التركيز
                appointmentDateField.addEventListener('blur', function() {
                    console.log('فقدان التركيز من تاريخ التعيين');
                    calculateServiceLength();
                });

                console.log('تم إضافة مستمعات أحداث تاريخ التعيين');
            }

            // التحقق من صحة النموذج
            const form = document.getElementById('employeeForm');
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        }

        // حساب تاريخ استحقاق الترقية
        function calculatePromotionDate() {
            const appointmentDate = document.getElementById('AppointmentDate')?.value;
            const lastPromotionDate = document.getElementById('lastPromotionDate')?.value;
            const currentRankSelect = document.getElementById('RankId');
            const calculatedPromotionDateField = document.getElementById('calculatedPromotionDate');

            if (!calculatedPromotionDateField) return;

            // إذا لم تكن هناك رتبة محددة، امسح الحقل
            if (!currentRankSelect || !currentRankSelect.value) {
                calculatedPromotionDateField.value = '';
                return;
            }

            // الحصول على بيانات الرتبة المحددة
            const selectedOption = currentRankSelect.options[currentRankSelect.selectedIndex];
            const rankDurationYears = parseInt(selectedOption.getAttribute('data-rank-duration')) || 0;

            if (rankDurationYears === 0) {
                calculatedPromotionDateField.value = '';
                return;
            }

            // تحديد تاريخ البداية (آخر ترقية أو تاريخ التعيين)
            let startDate;
            if (lastPromotionDate) {
                startDate = new Date(lastPromotionDate);
            } else if (appointmentDate) {
                startDate = new Date(appointmentDate);
            } else {
                calculatedPromotionDateField.value = '';
                return;
            }

            // حساب تاريخ الاستحقاق بناءً على مدة الرتبة
            let eligibilityDate = new Date(startDate);
            eligibilityDate.setFullYear(eligibilityDate.getFullYear() + rankDurationYears);

            // تنسيق التاريخ للعرض
            const formattedDate = eligibilityDate.toISOString().split('T')[0];
            calculatedPromotionDateField.value = formattedDate;

            // إضافة تأثير بصري للإشارة إلى التحديث
            calculatedPromotionDateField.style.backgroundColor = '#d4edda';
            setTimeout(() => {
                calculatedPromotionDateField.style.backgroundColor = '#f8f9fa';
            }, 1000);
        }

        // تأكد من تشغيل التهيئة بعد تحميل المحتوى بالكامل
        document.addEventListener('DOMContentLoaded', function() {
            initEmployeeCreate();

            // التحقق من تفرد الرقم الإحصائي
            const statisticalNumberField = document.getElementById('StatisticalNumber');
            if (statisticalNumberField) {
                statisticalNumberField.addEventListener('blur', function() {
                    const statisticalNumber = this.value;
                    if (statisticalNumber) {
                        // يمكن إضافة AJAX للتحقق من التفرد هنا
                    }
                });
            }

            // إضافة event listeners لحساب تاريخ الاستحقاق
            const appointmentDateField = document.getElementById('AppointmentDate');
            const lastPromotionDateField = document.getElementById('lastPromotionDate');
            const currentRankField = document.getElementById('RankId');

            if (appointmentDateField) {
                appointmentDateField.addEventListener('change', calculatePromotionDate);
            }

            if (lastPromotionDateField) {
                lastPromotionDateField.addEventListener('change', calculatePromotionDate);
            }

            if (currentRankField) {
                currentRankField.addEventListener('change', calculatePromotionDate);
            }

            // حساب أولي عند تحميل الصفحة
            setTimeout(calculatePromotionDate, 500);
        });
        window.loadAgencies = async function() {
            const ministrySelect = document.getElementById('MinistryId');
            const agencySelect = document.getElementById('AgencyId');
            const directorateSelect = document.getElementById('DirectorateId');
            const departmentSelect = document.getElementById('GovernmentDepartmentId');
            const divisionSelect = document.getElementById('DivisionId');

            if (!ministrySelect || !agencySelect) {
                console.error('عناصر الوزارة أو الوكالة غير موجودة');
                return;
            }

            const ministryId = ministrySelect.value;

            // إعادة تعيين الحقول التابعة مع رسائل محسنة
            agencySelect.innerHTML = '<option value="">اختر الوكالة</option>';
            directorateSelect.innerHTML = '<option value="">اختر المديرية أولاً</option>';
            departmentSelect.innerHTML = '<option value="">اختر القسم الحكومي أولاً</option>';
            divisionSelect.innerHTML = '<option value="">اختر الشعبة أولاً</option>';

            // تعطيل الحقول مع تأثيرات بصرية
            agencySelect.disabled = true;
            directorateSelect.disabled = true;
            departmentSelect.disabled = true;
            divisionSelect.disabled = true;

            // إضافة فئات CSS للتحميل
            agencySelect.classList.add('loading');
            directorateSelect.classList.add('disabled-cascade');
            departmentSelect.classList.add('disabled-cascade');
            divisionSelect.classList.add('disabled-cascade');

            if (ministryId) {
                try {
                    // منع التفاعل أثناء التحميل
                    ministrySelect.disabled = true;

                    // مؤشر تحميل محسن مع أيقونة
                    agencySelect.innerHTML = '<option value="">⏳ جاري تحميل الوكالات...</option>';

                    const agenciesUrl = '@Url.Action("GetAgenciesByMinistry", "Constants")' + '?ministryId=' + encodeURIComponent(ministryId);
                    const response = await fetch(agenciesUrl, { headers: { 'Accept': 'application/json' } });

                    if (response.ok) {
                        let agencies;
                        try {
                            agencies = await response.json();
                        } catch (parseError) {
                            console.error('خطأ في تحليل بيانات الوكالات:', parseError);
                            agencySelect.innerHTML = '<option value="">❌ خطأ في تحليل البيانات</option>';
                            agencySelect.classList.add('error');
                            return;
                        }

                        // إعادة تعيين القائمة مع عدد العناصر
                        agencySelect.innerHTML = '<option value="">اختر الوكالة</option>';

                        if (Array.isArray(agencies) && agencies.length > 0) {
                            for (const agency of agencies) {
                                const option = document.createElement('option');
                                option.value = agency.id;
                                option.textContent = agency.name;
                                agencySelect.appendChild(option);
                            }
                            agencySelect.disabled = false;
                            agencySelect.classList.remove('loading');
                            agencySelect.classList.add('loaded');

                            // إضافة تلميح بعدد الوكالات
                            const firstOption = agencySelect.querySelector('option[value=""]');
                            firstOption.textContent = `اختر الوكالة (${agencies.length} متاح)`;

                            // تفعيل التحديد التلقائي إذا كان هناك وكالة واحدة فقط
                            if (agencies.length === 1) {
                                agencySelect.value = agencies[0].id;

                                // تفعيل حقل المديرية مباشرة
                                directorateSelect.disabled = false;
                                directorateSelect.innerHTML = '<option value="">اختر المديرية</option>';
                                directorateSelect.classList.remove('disabled-cascade');
                                directorateSelect.classList.add('enabled');

                                // تشغيل تحميل المديريات تلقائياً
                                setTimeout(() => loadDirectorates(), 100);
                            }
                        } else {
                            agencySelect.innerHTML = '<option value="">📭 لا توجد وكالات نشطة لهذه الوزارة</option>';
                            agencySelect.disabled = true;
                            agencySelect.classList.add('empty');
                        }
                    } else {
                        console.error('فشل في تحميل الوكالات - كود الاستجابة:', response.status);
                        agencySelect.innerHTML = '<option value="">🚫 خطأ في تحميل البيانات</option>';
                        agencySelect.disabled = true;
                        agencySelect.classList.add('error');
                    }
                } catch (error) {
                    console.error('خطأ في تحميل الوكالات:', error);
                    agencySelect.innerHTML = '<option value="">🌐 خطأ في الاتصال بالخادم</option>';
                    agencySelect.disabled = true;
                    agencySelect.classList.add('error');
                } finally {
                    // إعادة تفعيل حقل الوزارة
                    ministrySelect.disabled = false;
                    agencySelect.classList.remove('loading');
                }
            } else {
                // إزالة فئات التحميل عند عدم اختيار وزارة
                agencySelect.classList.remove('loading', 'loaded', 'error', 'empty');
            }
        }

        window.loadDirectorates = async function() {
            const agencySelect = document.getElementById('AgencyId');
            const agencyId = agencySelect.value;
            const directorateSelect = document.getElementById('DirectorateId');
            const departmentSelect = document.getElementById('GovernmentDepartmentId');
            const divisionSelect = document.getElementById('DivisionId');

            // إعادة تعيين الحقول التابعة مع رسائل محسنة
            directorateSelect.innerHTML = '<option value="">اختر المديرية</option>';
            departmentSelect.innerHTML = '<option value="">اختر القسم الحكومي أولاً</option>';
            divisionSelect.innerHTML = '<option value="">اختر الشعبة أولاً</option>';

            // تطبيق فئات CSS
            directorateSelect.disabled = true;
            departmentSelect.disabled = true;
            divisionSelect.disabled = true;

            directorateSelect.classList.add('loading');
            departmentSelect.classList.add('disabled-cascade');
            divisionSelect.classList.add('disabled-cascade');

            if (agencyId) {
                try {
                    // منع التفاعل أثناء التحميل
                    agencySelect.disabled = true;

                    // مؤشر تحميل محسن
                    directorateSelect.innerHTML = '<option value="">⏳ جاري تحميل المديريات...</option>';

                    const directoratesUrl = '@Url.Action("GetDirectoratesByAgency", "Constants")' + '?agencyId=' + encodeURIComponent(agencyId);
                    const response = await fetch(directoratesUrl, { headers: { 'Accept': 'application/json' } });

                    if (response.ok) {
                        let directorates;
                        try {
                            directorates = await response.json();
                        } catch (parseError) {
                            console.error('خطأ في تحليل بيانات المديريات:', parseError);
                            directorateSelect.innerHTML = '<option value="">❌ خطأ في تحليل البيانات</option>';
                            directorateSelect.classList.add('error');
                            return;
                        }

                        // إعادة تعيين القائمة مع عدد العناصر
                        directorateSelect.innerHTML = '<option value="">اختر المديرية</option>';

                        if (Array.isArray(directorates) && directorates.length > 0) {
                            for (const directorate of directorates) {
                                const option = document.createElement('option');
                                option.value = directorate.id;
                                option.textContent = directorate.name;
                                directorateSelect.appendChild(option);
                            }
                            directorateSelect.disabled = false;
                            directorateSelect.classList.remove('loading');
                            directorateSelect.classList.add('loaded');

                            // إضافة تلميح بعدد المديريات
                            const firstOption = directorateSelect.querySelector('option[value=""]');
                            firstOption.textContent = `اختر المديرية (${directorates.length} متاح)`;

                            // تفعيل التحديد التلقائي إذا كان هناك مديرية واحدة فقط
                            if (directorates.length === 1) {
                                directorateSelect.value = directorates[0].id;

                                // تفعيل حقل القسم الحكومي مباشرة
                                departmentSelect.disabled = false;
                                departmentSelect.innerHTML = '<option value="">اختر القسم الحكومي</option>';
                                departmentSelect.classList.remove('disabled-cascade');
                                departmentSelect.classList.add('enabled');

                                // تشغيل تحميل الأقسام الحكومية تلقائياً
                                setTimeout(() => loadGovernmentDepartments(), 100);
                            }
                        } else {
                            directorateSelect.innerHTML = '<option value="">📭 لا توجد مديريات نشطة لهذه الوكالة</option>';
                            directorateSelect.disabled = true;
                            directorateSelect.classList.add('empty');
                        }
                    } else {
                        console.error('فشل في تحميل المديريات - كود الاستجابة:', response.status);
                        directorateSelect.innerHTML = '<option value="">🚫 خطأ في تحميل البيانات</option>';
                        directorateSelect.disabled = true;
                        directorateSelect.classList.add('error');
                    }
                } catch (error) {
                    console.error('خطأ في تحميل المديريات:', error);
                    directorateSelect.innerHTML = '<option value="">🌐 خطأ في الاتصال بالخادم</option>';
                    directorateSelect.disabled = true;
                    directorateSelect.classList.add('error');
                } finally {
                    // إعادة تفعيل حقل الوكالة
                    agencySelect.disabled = false;
                    directorateSelect.classList.remove('loading');
                }
            } else {
                // إزالة فئات التحميل عند عدم اختيار وكالة
                directorateSelect.classList.remove('loading', 'loaded', 'error', 'empty');
                departmentSelect.classList.remove('disabled-cascade');
                divisionSelect.classList.remove('disabled-cascade');
            }
        }

        window.loadGovernmentDepartments = async function() {
            const directorateSelect = document.getElementById('DirectorateId');
            const directorateId = directorateSelect.value;
            const departmentSelect = document.getElementById('GovernmentDepartmentId');
            const divisionSelect = document.getElementById('DivisionId');

            // إعادة تعيين الحقول التابعة مع رسائل محسنة
            departmentSelect.innerHTML = '<option value="">اختر القسم الحكومي</option>';
            divisionSelect.innerHTML = '<option value="">اختر الشعبة أولاً</option>';

            // تطبيق فئات CSS
            departmentSelect.disabled = true;
            divisionSelect.disabled = true;

            departmentSelect.classList.add('loading');
            divisionSelect.classList.add('disabled-cascade');

            if (directorateId) {
                try {
                    // منع التفاعل أثناء التحميل
                    directorateSelect.disabled = true;

                    // مؤشر تحميل محسن
                    departmentSelect.innerHTML = '<option value="">⏳ جاري تحميل الأقسام الحكومية...</option>';

                    const departmentsUrl = '@Url.Action("GetGovernmentDepartmentsByDirectorate", "Constants")' + '?directorateId=' + encodeURIComponent(directorateId);
                    const response = await fetch(departmentsUrl, { headers: { 'Accept': 'application/json' } });

                    if (response.ok) {
                        let departments;
                        try {
                            departments = await response.json();
                        } catch (parseError) {
                            console.error('خطأ في تحليل بيانات الأقسام الحكومية:', parseError);
                            departmentSelect.innerHTML = '<option value="">❌ خطأ في تحليل البيانات</option>';
                            departmentSelect.classList.add('error');
                            return;
                        }

                        // إعادة تعيين القائمة مع عدد العناصر
                        departmentSelect.innerHTML = '<option value="">اختر القسم الحكومي</option>';

                        if (Array.isArray(departments) && departments.length > 0) {
                            for (const department of departments) {
                                const option = document.createElement('option');
                                option.value = department.id;
                                option.textContent = department.name;
                                departmentSelect.appendChild(option);
                            }
                            departmentSelect.disabled = false;
                            departmentSelect.classList.remove('loading');
                            departmentSelect.classList.add('loaded');

                            // إضافة تلميح بعدد الأقسام
                            const firstOption = departmentSelect.querySelector('option[value=""]');
                            firstOption.textContent = `اختر القسم الحكومي (${departments.length} متاح)`;

                            // تفعيل التحديد التلقائي إذا كان هناك قسم حكومي واحد فقط
                            if (departments.length === 1) {
                                departmentSelect.value = departments[0].id;

                                // تفعيل حقل الشعبة مباشرة
                                divisionSelect.disabled = false;
                                divisionSelect.innerHTML = '<option value="">اختر الشعبة</option>';
                                divisionSelect.classList.remove('disabled-cascade');
                                divisionSelect.classList.add('enabled');

                                // تشغيل تحميل الشعب تلقائياً
                                setTimeout(() => loadDivisions(), 100);
                            }
                        } else {
                            departmentSelect.innerHTML = '<option value="">📭 لا توجد أقسام حكومية نشطة لهذه المديرية</option>';
                            departmentSelect.disabled = true;
                            departmentSelect.classList.add('empty');
                        }
                    } else {
                        console.error('فشل في تحميل الأقسام الحكومية - كود الاستجابة:', response.status);
                        departmentSelect.innerHTML = '<option value="">🚫 خطأ في تحميل البيانات</option>';
                        departmentSelect.disabled = true;
                        departmentSelect.classList.add('error');
                    }
                } catch (error) {
                    console.error('خطأ في تحميل الأقسام الحكومية:', error);
                    departmentSelect.innerHTML = '<option value="">🌐 خطأ في الاتصال بالخادم</option>';
                    departmentSelect.disabled = true;
                    departmentSelect.classList.add('error');
                } finally {
                    // إعادة تفعيل حقل المديرية
                    directorateSelect.disabled = false;
                    departmentSelect.classList.remove('loading');
                }
            } else {
                // إزالة فئات التحميل عند عدم اختيار مديرية
                departmentSelect.classList.remove('loading', 'loaded', 'error', 'empty');
                divisionSelect.classList.remove('disabled-cascade');
            }
        }

        window.loadDivisions = async function() {
            const departmentSelect = document.getElementById('GovernmentDepartmentId');
            const departmentId = departmentSelect.value;
            const divisionSelect = document.getElementById('DivisionId');

            // إعادة تعيين الحقل التابع مع رسائل محسنة
            divisionSelect.innerHTML = '<option value="">اختر الشعبة</option>';
            divisionSelect.disabled = true;
            divisionSelect.classList.add('loading');

            if (departmentId) {
                try {
                    // منع التفاعل أثناء التحميل
                    departmentSelect.disabled = true;

                    // مؤشر تحميل محسن
                    divisionSelect.innerHTML = '<option value="">⏳ جاري تحميل الشعب...</option>';

                    const divisionsUrl = '@Url.Action("GetDivisionsByGovernmentDepartment", "Constants")' + '?governmentDepartmentId=' + encodeURIComponent(departmentId);
                    const response = await fetch(divisionsUrl, { headers: { 'Accept': 'application/json' } });

                    if (response.ok) {
                        let divisions;
                        try {
                            divisions = await response.json();
                        } catch (parseError) {
                            console.error('خطأ في تحليل بيانات الشعب:', parseError);
                            divisionSelect.innerHTML = '<option value="">❌ خطأ في تحليل البيانات</option>';
                            divisionSelect.classList.add('error');
                            return;
                        }

                        // إعادة تعيين القائمة مع عدد العناصر
                        divisionSelect.innerHTML = '<option value="">اختر الشعبة</option>';

                        if (Array.isArray(divisions) && divisions.length > 0) {
                            for (const division of divisions) {
                                const option = document.createElement('option');
                                option.value = division.id;
                                option.textContent = division.name;
                                divisionSelect.appendChild(option);
                            }
                            divisionSelect.disabled = false;
                            divisionSelect.classList.remove('loading');
                            divisionSelect.classList.add('loaded');

                            // إضافة تلميح بعدد الشعب
                            const firstOption = divisionSelect.querySelector('option[value=""]');
                            firstOption.textContent = `اختر الشعبة (${divisions.length} متاح)`;

                            // تفعيل التحديد التلقائي إذا كان هناك شعبة واحدة فقط
                            if (divisions.length === 1) {
                                divisionSelect.value = divisions[0].id;
                                // إضافة تأثير بصري للإكمال
                                divisionSelect.classList.add('auto-selected');
                                setTimeout(() => {
                                    divisionSelect.classList.remove('auto-selected');
                                }, 2000);
                            }
                        } else {
                            divisionSelect.innerHTML = '<option value="">📭 لا توجد شعب نشطة لهذا القسم الحكومي</option>';
                            divisionSelect.disabled = true;
                            divisionSelect.classList.add('empty');
                        }
                    } else {
                        console.error('فشل في تحميل الشعب - كود الاستجابة:', response.status);
                        divisionSelect.innerHTML = '<option value="">🚫 خطأ في تحميل البيانات</option>';
                        divisionSelect.disabled = true;
                        divisionSelect.classList.add('error');
                    }
                } catch (error) {
                    console.error('خطأ في تحميل الشعب:', error);
                    divisionSelect.innerHTML = '<option value="">🌐 خطأ في الاتصال بالخادم</option>';
                    divisionSelect.disabled = true;
                    divisionSelect.classList.add('error');
                } finally {
                    // إعادة تفعيل حقل القسم الحكومي
                    departmentSelect.disabled = false;
                    divisionSelect.classList.remove('loading');
                }
            } else {
                // إزالة فئات التحميل عند عدم اختيار قسم حكومي
                divisionSelect.classList.remove('loading', 'loaded', 'error', 'empty');
            }
        }

        // بدء الحفظ التلقائي
        autoSave.start('#employeeForm');

        // إضافة مؤشر حالة الاتصال
        window.addEventListener('online', function() {
            console.log('تم استعادة الاتصال بالإنترنت');
        });

        window.addEventListener('offline', function() {
            console.log('انقطع الاتصال بالإنترنت. سيتم حفظ البيانات محلياً');
        });


    </script>
}

    <style>
        /* تحسينات التبويبات */
        .enhanced-tabs {
            border-bottom: 3px solid #e9ecef;
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border-radius: 12px 12px 0 0;
            padding: 10px 10px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .enhanced-tabs .nav-link {
            border: none;
            border-radius: 12px 12px 0 0;
            padding: 15px 20px;
            margin: 0 5px;
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 12px;
            min-height: 80px;
            position: relative;
            overflow: hidden;
        }

        .enhanced-tabs .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.5), transparent);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .enhanced-tabs .nav-link:hover::before {
            transform: translateX(0);
        }

        .enhanced-tabs .nav-link.active::before {
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            transform: translateX(0);
        }

        .enhanced-tabs .nav-link:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .enhanced-tabs .nav-link.active {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(255, 255, 255, 0.95));
            border-bottom: 3px solid #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }

        .tab-icon-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.8);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .tab-icon-wrapper i {
            font-size: 20px;
            transition: all 0.3s ease;
        }

        .enhanced-tabs .nav-link:hover .tab-icon-wrapper {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .enhanced-tabs .nav-link.active .tab-icon-wrapper {
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: scale(1.1);
        }

        .enhanced-tabs .nav-link.active .tab-icon-wrapper i {
            color: white !important;
        }

        .tab-content-wrapper {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 2px;
        }

        .tab-title {
            font-weight: 600;
            font-size: 16px;
            color: #2c3e50;
            transition: color 0.3s ease;
        }

        .tab-subtitle {
            font-size: 12px;
            color: #7f8c8d;
            font-weight: 400;
            transition: color 0.3s ease;
        }

        .enhanced-tabs .nav-link.active .tab-title {
            color: #667eea;
        }

        .enhanced-tabs .nav-link.active .tab-subtitle {
            color: #5a6c7d;
        }

        /* تحسينات الاستجابة للتبويبات */
        @@media (max-width: 768px) {
            .enhanced-tabs .nav-link {
                flex-direction: column;
                text-align: center;
                padding: 12px 8px;
                min-height: 70px;
                gap: 8px;
            }

            .tab-icon-wrapper {
                width: 35px;
                height: 35px;
            }

            .tab-icon-wrapper i {
                font-size: 16px;
            }

            .tab-title {
                font-size: 14px;
            }

            .tab-subtitle {
                font-size: 11px;
                display: none;
            }
        }

        @@media (max-width: 576px) {
            .enhanced-tabs {
                padding: 5px 5px 0;
            }

            .enhanced-tabs .nav-link {
                margin: 0 2px;
                padding: 10px 6px;
                min-height: 60px;
            }

            .tab-content-wrapper {
                align-items: center;
            }
        }

        /* تحسينات البنية الهرمية الحكومية */
        .form-select.loading {
            background: linear-gradient(90deg, #f8f9fa 25%, #e9ecef 50%, #f8f9fa 75%);
            background-size: 200% 100%;
            animation: loading-shimmer 1.5s infinite;
            border-color: #007bff;
            color: #007bff;
            position: relative;
        }

        .form-select.loaded {
            border-color: #28a745;
            background-color: #f8fff8;
            transition: all 0.3s ease;
        }

        .form-select.error {
            border-color: #dc3545;
            background-color: #fff5f5;
            color: #dc3545;
        }

        .form-select.empty {
            border-color: #ffc107;
            background-color: #fffbf0;
            color: #856404;
        }

        .form-select.disabled-cascade {
            background-color: #f1f3f4;
            color: #6c757d;
            border-color: #dee2e6;
            opacity: 0.7;
        }

        .form-select.enabled {
            border-color: #007bff;
            background-color: #f8f9ff;
            transition: all 0.3s ease;
            animation: field-enable 0.5s ease-out;
        }

        @@keyframes field-enable {
            0% {
                opacity: 0.5;
                transform: translateY(-5px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @@keyframes loading-shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* تحسين مظهر الخيارات */
        .form-select option:disabled {
            color: #6c757d;
            font-style: italic;
        }

        /* مؤشر التحميل للحقول */
        .loading-indicator {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 8px;
        }

        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسين التركيز */
        .form-select:focus {
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            border-color: #80bdff;
        }

        .form-select.loaded:focus {
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
            border-color: #28a745;
        }

        /* تأثير التحديد التلقائي */
        .form-select.auto-selected {
            border-color: #17a2b8;
            background-color: #e3f7fc;
            animation: auto-select-pulse 2s ease-in-out;
        }

        @@keyframes auto-select-pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(23, 162, 184, 0.7);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 0 10px rgba(23, 162, 184, 0);
                transform: scale(1.02);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(23, 162, 184, 0);
                transform: scale(1);
            }
        }

        /* تحسينات الاختصارات */
        .shortcut-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-top: 0.5rem;
            animation: slideDown 0.3s ease-out;
        }

        @@keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .shortcut-container .btn {
            transition: all 0.2s ease;
            border-width: 1px;
            font-weight: 500;
        }

        .shortcut-container .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .shortcut-container .btn:active {
            transform: translateY(0);
        }



        /* تحسين أزرار الاختصارات */
        .input-group .btn {
            border-left: none;
        }

        .input-group .form-select:focus + .btn {
            border-color: #86b7fe;
        }

        /* تحسين روابط الإضافة السريعة */
        .btn-link.btn-sm {
            color: #0d6efd;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .btn-link.btn-sm:hover {
            color: #0a58ca;
            transform: translateX(-2px);
        }

        .btn-link.btn-sm i {
            color: #28a745;
            transition: transform 0.2s ease;
        }

        .btn-link.btn-sm:hover i {
            transform: scale(1.1);
        }






    </style>
