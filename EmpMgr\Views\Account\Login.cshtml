@model EmpMgr.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول";
    Layout = "_LoginLayout";
}

<!-- الحاوي الرئيسي الجديد -->
<div class="ultra-modern-login-wrapper">
    <!-- خلفية ديناميكية متطورة -->
    <div class="dynamic-background">
        <!-- طبقات الخلفية المتحركة -->
        <div class="bg-layer layer-1"></div>
        <div class="bg-layer layer-2"></div>
        <div class="bg-layer layer-3"></div>

        <!-- جزيئات متحركة -->
        <div class="particles-container">
            <div class="particle particle-1"></div>
            <div class="particle particle-2"></div>
            <div class="particle particle-3"></div>
            <div class="particle particle-4"></div>
            <div class="particle particle-5"></div>
            <div class="particle particle-6"></div>
            <div class="particle particle-7"></div>
            <div class="particle particle-8"></div>
        </div>

        <!-- شبكة هندسية -->
        <div class="geometric-grid">
            <div class="grid-line horizontal"></div>
            <div class="grid-line vertical"></div>
        </div>
    </div>

    <!-- بطاقة تسجيل الدخول الجديدة -->
    <div class="ultra-login-card">
        <!-- قسم الشعار والعنوان -->
        <div class="brand-section">
            <div class="logo-wrapper-new">
                <div class="logo-ring">
                    <div class="logo-inner">
                        @{
                            var settings = ViewBag.SystemSettings as EmpMgr.Models.SystemSettings;
                            var logoPath = settings?.LogoPath ?? "/images/moi-logo.png";
                        }
                        <img src="@logoPath" alt="شعار النظام" class="logo-image"
                             onerror="this.src='/images/default-logo.svg'; this.onerror=null;" />
                    </div>
                    <div class="logo-pulse"></div>
                </div>
            </div>

            <div class="brand-text">
                <h1 class="system-title">
                    @{
                        var systemName = settings?.SystemName ?? "نظام إدارة الضباط والمنتسبين";
                        var organizationName = settings?.OrganizationName ?? "وزارة الداخلية - جمهورية العراق";
                    }
                    @systemName
                </h1>
                <div class="ministry-badge">
                    <i class="fas fa-shield-alt"></i>
                    <span>@organizationName</span>
                </div>
            </div>
        </div>

        <!-- قسم النموذج الجديد -->
        <div class="login-form-section">
            <form asp-action="Login" asp-controller="Account" method="post" class="ultra-login-form">
                <!-- رسائل الخطأ -->
                <div asp-validation-summary="All" class="error-summary"></div>

                <!-- مجموعة حقول الإدخال -->
                <div class="input-fields-group">
                    <!-- حقل البريد الإلكتروني -->
                    <div class="ultra-input-group">
                        <div class="input-container">
                            <div class="input-icon-wrapper">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <input asp-for="Email" type="email" class="ultra-input" placeholder=" " autocomplete="email" required />
                            <label asp-for="Email" class="ultra-label">البريد الإلكتروني</label>
                            <div class="input-highlight"></div>
                            <div class="input-ripple"></div>
                        </div>
                        <span asp-validation-for="Email" class="field-error"></span>
                    </div>

                    <!-- حقل كلمة المرور -->
                    <div class="ultra-input-group">
                        <div class="input-container">
                            <div class="input-icon-wrapper">
                                <i class="fas fa-lock"></i>
                            </div>
                            <input asp-for="Password" type="password" class="ultra-input" placeholder=" " autocomplete="current-password" required />
                            <label asp-for="Password" class="ultra-label">كلمة المرور</label>
                            <button type="button" class="password-reveal" onclick="togglePasswordVisibility()">
                                <i class="fas fa-eye" id="passwordToggleIcon"></i>
                            </button>
                            <div class="input-highlight"></div>
                            <div class="input-ripple"></div>
                        </div>
                        <span asp-validation-for="Password" class="field-error"></span>
                    </div>
                </div>

                <!-- خيار تذكرني المحسن -->
                <div class="remember-section">
                    <div class="modern-checkbox">
                        <input asp-for="RememberMe" type="checkbox" id="RememberMe" class="checkbox-input" />
                        <label for="RememberMe" class="checkbox-label">
                            <div class="checkbox-custom">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="checkbox-text">
                                <span class="checkbox-title">تذكرني</span>
                                <span class="checkbox-subtitle">حفظ بياناتي بأمان</span>
                            </div>
                        </label>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="remember-info" id="rememberInfo" style="display: none;">
                        <div class="info-item" id="lastLoginInfo">
                            <i class="fas fa-clock"></i>
                            <span id="lastLoginText"></span>
                        </div>
                        <div class="info-item security-warning" id="securityWarning">
                            <i class="fas fa-shield-alt"></i>
                            <span>لا تستخدم هذا الخيار على الأجهزة المشتركة</span>
                        </div>
                    </div>
                </div>

                <!-- زر تسجيل الدخول -->
                <button type="submit" class="login-button-modern">
                    <div class="button-content">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>تسجيل الدخول</span>
                    </div>
                    <div class="button-loader" style="display: none;">
                        <div class="spinner"></div>
                    </div>
                </button>

                <!-- الروابط الإضافية -->
                <div class="additional-links">
                    <div class="links-row">
                        <a asp-action="Register" class="link-modern register-link">
                            <i class="fas fa-user-plus"></i>
                            <span>إنشاء حساب جديد</span>
                        </a>
                        <a asp-action="ForgotPassword" class="link-modern forgot-link">
                            <i class="fas fa-key"></i>
                            <span>نسيت كلمة المرور؟</span>
                        </a>
                    </div>

                    <button type="button" id="clearDataBtn" class="clear-data-modern" style="display: none;" onclick="clearSavedData()">
                        <i class="fas fa-trash-alt"></i>
                        <span>مسح البيانات المحفوظة</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="~/css/login.css" asp-append-version="true" />
}

@section Scripts {
    <script>
        // تحسين وظيفة إظهار/إخفاء كلمة المرور
        function togglePasswordVisibility() {
            const passwordInput = document.querySelector('input[name="Password"]');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordInput && toggleIcon) {
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    toggleIcon.classList.remove('fa-eye');
                    toggleIcon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    toggleIcon.classList.remove('fa-eye-slash');
                    toggleIcon.classList.add('fa-eye');
                }
            }
        }

        // دالة للتوافق مع الكود القديم
        function togglePassword() {
            togglePasswordVisibility();
        }

        // إدارة ميزة "تذكرني"
        const REMEMBER_ME_KEY = 'emp_mgr_remember_me';
        const SAVED_EMAIL_KEY = 'emp_mgr_saved_email';
        const SAVED_PASSWORD_KEY = 'emp_mgr_saved_password';
        const LAST_LOGIN_KEY = 'emp_mgr_last_login';
        const ENCRYPTION_KEY = 'emp_mgr_2024_secure';

        // تشفير بسيط للبيانات (Base64 مع مفتاح)
        function simpleEncrypt(text) {
            try {
                const encoded = btoa(unescape(encodeURIComponent(text + ENCRYPTION_KEY)));
                return encoded;
            } catch (e) {
                return text;
            }
        }

        // فك التشفير
        function simpleDecrypt(encoded) {
            try {
                const decoded = decodeURIComponent(escape(atob(encoded)));
                return decoded.replace(ENCRYPTION_KEY, '');
            } catch (e) {
                return '';
            }
        }

        // تحميل البيانات المحفوظة
        function loadSavedCredentials() {
            const rememberMe = localStorage.getItem(REMEMBER_ME_KEY) === 'true';
            const savedEmailEncrypted = localStorage.getItem(SAVED_EMAIL_KEY);
            const savedPasswordEncrypted = localStorage.getItem(SAVED_PASSWORD_KEY);

            if (rememberMe && savedEmailEncrypted && savedPasswordEncrypted) {
                const savedEmail = simpleDecrypt(savedEmailEncrypted);
                const savedPassword = simpleDecrypt(savedPasswordEncrypted);

                if (savedEmail && savedPassword) {
                const emailInput = document.getElementById('Email');
                const passwordInput = document.getElementById('Password');
                const rememberCheckbox = document.getElementById('RememberMe');

                emailInput.value = savedEmail;
                passwordInput.value = savedPassword;
                rememberCheckbox.checked = true;

                // إضافة فئات CSS للتأثيرات البصرية
                emailInput.classList.add('pre-filled');
                passwordInput.classList.add('pre-filled');
                emailInput.parentElement.classList.add('focused');
                passwordInput.parentElement.parentElement.classList.add('focused');

                // تأثير بصري للإشارة إلى تحميل البيانات
                document.querySelector('.remember-me-container').classList.add('credentials-loaded');
                document.querySelector('.btn-login').classList.add('with-saved-data');

                    // عرض معلومات آخر تسجيل دخول
                    showLastLoginInfo();

                    // إظهار زر مسح البيانات
                    document.getElementById('clearSavedDataBtn').style.display = 'flex';

                    // عرض رسالة تأكيد
                    showNotification('تم تحميل بياناتك المحفوظة', 'success');

                    return true;
                }
            }
            return false;
        }

        // حفظ البيانات
        function saveCredentials() {
            const email = document.getElementById('Email').value;
            const password = document.getElementById('Password').value;
            const rememberMe = document.getElementById('RememberMe').checked;

            if (rememberMe && email && password) {
                localStorage.setItem(REMEMBER_ME_KEY, 'true');
                localStorage.setItem(SAVED_EMAIL_KEY, simpleEncrypt(email));
                localStorage.setItem(SAVED_PASSWORD_KEY, simpleEncrypt(password));
                localStorage.setItem(LAST_LOGIN_KEY, new Date().toISOString());
                showNotification('تم حفظ بياناتك بنجاح', 'success');
            } else {
                // مسح البيانات المحفوظة إذا لم يتم اختيار "تذكرني"
                clearSavedCredentials();
                if (!rememberMe) {
                    showNotification('تم مسح البيانات المحفوظة', 'info');
                }
            }
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            // إزالة الإشعارات السابقة
            const existingNotification = document.querySelector('.save-confirmation');
            if (existingNotification) {
                existingNotification.remove();
            }

            // إنشاء إشعار جديد
            const notification = document.createElement('div');
            notification.className = 'save-confirmation';
            notification.innerHTML = `
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'info' ? 'fa-info-circle' : 'fa-exclamation-circle'}"></i>
                ${message}
            `;

            // إضافة الإشعار للصفحة
            document.body.appendChild(notification);

            // إزالة الإشعار بعد 3 ثوان
            setTimeout(() => {
                notification.classList.add('fade-out');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 500);
            }, 3000);
        }

        // مسح البيانات المحفوظة
        function clearSavedCredentials() {
            localStorage.removeItem(REMEMBER_ME_KEY);
            localStorage.removeItem(SAVED_EMAIL_KEY);
            localStorage.removeItem(SAVED_PASSWORD_KEY);

            // إزالة التأثيرات البصرية
            const emailInput = document.getElementById('Email');
            const passwordInput = document.getElementById('Password');

            emailInput.classList.remove('pre-filled');
            passwordInput.classList.remove('pre-filled');
            document.querySelector('.btn-login').classList.remove('with-saved-data');
        }

        // عرض معلومات آخر تسجيل دخول
        function showLastLoginInfo() {
            const lastLogin = localStorage.getItem(LAST_LOGIN_KEY);
            if (lastLogin) {
                const lastLoginDate = new Date(lastLogin);
                const now = new Date();
                const diffTime = Math.abs(now - lastLoginDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                let timeText;
                if (diffDays === 1) {
                    timeText = 'أمس';
                } else if (diffDays < 7) {
                    timeText = `منذ ${diffDays} أيام`;
                } else if (diffDays < 30) {
                    const weeks = Math.floor(diffDays / 7);
                    timeText = `منذ ${weeks} ${weeks === 1 ? 'أسبوع' : 'أسابيع'}`;
                } else {
                    timeText = lastLoginDate.toLocaleDateString('ar-EG');
                }

                document.getElementById('lastLoginText').textContent = `آخر تسجيل دخول: ${timeText}`;
                document.getElementById('lastLoginInfo').style.display = 'flex';
            }
        }

        // التحقق من قوة كلمة المرور
        function checkPasswordStrength(password) {
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            return strength;
        }

        // تأكيد مسح البيانات المحفوظة
        function confirmClearData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات المحفوظة؟\nسيتم حذف البريد الإلكتروني وكلمة المرور المحفوظة.')) {
                clearSavedCredentials();

                // إخفاء العناصر المرتبطة
                document.getElementById('clearSavedDataBtn').style.display = 'none';
                document.getElementById('lastLoginInfo').style.display = 'none';
                document.getElementById('securityWarning').style.display = 'none';
                document.getElementById('RememberMe').checked = false;

                // مسح الحقول
                document.getElementById('Email').value = '';
                document.getElementById('Password').value = '';

                // إزالة الفئات
                document.getElementById('Email').classList.remove('pre-filled');
                document.getElementById('Password').classList.remove('pre-filled');
                document.querySelector('.remember-me-container').classList.remove('credentials-loaded');

                // التركيز على حقل البريد الإلكتروني
                document.getElementById('Email').focus();

                showNotification('تم مسح جميع البيانات المحفوظة', 'info');
            }
        }

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل البيانات المحفوظة أولاً
            loadSavedCredentials();

            // التركيز على حقل البريد الإلكتروني إذا كان فارغاً
            const emailInput = document.getElementById('Email');
            if (!emailInput.value) {
                emailInput.focus();
            } else {
                // إذا كان البريد محفوظ، ركز على زر تسجيل الدخول
                document.querySelector('.btn-login').focus();
            }

            // إضافة تأثيرات للحقول
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (this.value === '') {
                        this.parentElement.classList.remove('focused');
                    }
                });

                // إذا كان الحقل يحتوي على قيمة عند التحميل
                if (input.value !== '') {
                    input.parentElement.classList.add('focused');
                }
            });

            // حفظ البيانات عند تغيير حالة "تذكرني"
            document.getElementById('RememberMe').addEventListener('change', function() {
                const email = document.getElementById('Email').value;
                const password = document.getElementById('Password').value;
                const securityWarning = document.getElementById('securityWarning');
                const dataCleared = document.getElementById('dataCleared');
                const checkbox = this;

                // إضافة تأثير بصري للتبديل
                checkbox.parentElement.classList.add('changing');
                setTimeout(() => {
                    checkbox.parentElement.classList.remove('changing');
                }, 300);

                if (this.checked) {
                    // إظهار التحذير الأمني
                    securityWarning.style.display = 'flex';
                    dataCleared.style.display = 'none';

                    if (email && password) {
                        saveCredentials();
                        showNotification('تم تفعيل "تذكرني"', 'سيتم حفظ بياناتك بأمان', 'success');
                    } else {
                        showNotification('املأ البيانات أولاً', 'أدخل البريد الإلكتروني وكلمة المرور لحفظهما', 'warning');
                    }
                } else {
                    // إخفاء التحذير الأمني وإظهار رسالة المسح
                    securityWarning.style.display = 'none';
                    dataCleared.style.display = 'flex';

                    clearSavedCredentials();
                    showNotification('تم إلغاء "تذكرني"', 'تم مسح البيانات المحفوظة', 'info');

                    // إخفاء رسالة المسح بعد 3 ثوان
                    setTimeout(() => {
                        dataCleared.style.display = 'none';
                    }, 3000);
                }
            });

            // حفظ البيانات عند إرسال النموذج
            document.querySelector('.login-form').addEventListener('submit', function(e) {
                const rememberMe = document.getElementById('RememberMe').checked;
                if (rememberMe) {
                    saveCredentials();
                } else {
                    clearSavedCredentials();
                }
            });

            // تحديث حالة زر "تذكرني" عند تغيير الحقول
            const emailInput = document.getElementById('Email');
            const passwordInput = document.getElementById('Password');

            [emailInput, passwordInput].forEach(input => {
                input.addEventListener('input', function() {
                    const email = emailInput.value;
                    const password = passwordInput.value;
                    const rememberCheckbox = document.getElementById('RememberMe');

                    // إزالة فئة pre-filled عند التعديل
                    this.classList.remove('pre-filled');

                    // تفعيل/تعطيل حفظ البيانات حسب وجود القيم
                    if (email && password && rememberCheckbox.checked) {
                        document.querySelector('.btn-login').classList.add('with-saved-data');
                    } else {
                        document.querySelector('.btn-login').classList.remove('with-saved-data');
                    }
                });
            });

            // إضافة اختصارات لوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                // Enter للانتقال بين الحقول أو إرسال النموذج
                if (e.key === 'Enter') {
                    const activeElement = document.activeElement;
                    if (activeElement.id === 'Email') {
                        e.preventDefault();
                        document.getElementById('Password').focus();
                    } else if (activeElement.id === 'Password') {
                        e.preventDefault();
                        document.querySelector('.btn-login').click();
                    }
                }

                // Ctrl+Enter لتبديل "تذكرني"
                if (e.ctrlKey && e.key === 'Enter') {
                    e.preventDefault();
                    const rememberCheckbox = document.getElementById('RememberMe');
                    rememberCheckbox.checked = !rememberCheckbox.checked;
                    rememberCheckbox.dispatchEvent(new Event('change'));
                }
            });

            // تحسينات إضافية للتجربة المحسنة
            enhanceModernLoginExperience();

            // تحسين تفاعل الشعار
            enhanceLogoInteraction();

            // تحسين تحميل الشعار
            enhanceLogoLoading();
        });

        // تحسين تجربة المستخدم للتصميم الجديد
        function enhanceModernLoginExperience() {
            // تحسين تأثيرات الحقول
            const inputs = document.querySelectorAll('.form-input-modern');
            inputs.forEach(input => {
                // تحسين التركيز
                input.addEventListener('focus', function() {
                    const icon = this.parentElement.querySelector('.input-icon');
                    if (icon) {
                        icon.style.color = '#667eea';
                        icon.style.transform = 'translateY(-50%) scale(1.1)';
                    }
                });

                input.addEventListener('blur', function() {
                    const icon = this.parentElement.querySelector('.input-icon');
                    if (icon) {
                        icon.style.color = '#7f8c8d';
                        icon.style.transform = 'translateY(-50%) scale(1)';
                    }
                });

                // تحسين الكتابة
                input.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        this.style.borderColor = '#28a745';
                        this.style.boxShadow = '0 0 0 4px rgba(40, 167, 69, 0.1)';
                    } else {
                        this.style.borderColor = 'rgba(0, 0, 0, 0.1)';
                        this.style.boxShadow = 'none';
                    }
                });
            });

            // تحسين زر تسجيل الدخول
            const loginForm = document.querySelector('.modern-login-form');
            const loginButton = document.querySelector('.login-button-modern');

            if (loginForm && loginButton) {
                loginForm.addEventListener('submit', function(e) {
                    // إظهار مؤشر التحميل
                    const buttonContent = loginButton.querySelector('.button-content');
                    const buttonLoader = loginButton.querySelector('.button-loader');

                    if (buttonContent && buttonLoader) {
                        buttonContent.style.display = 'none';
                        buttonLoader.style.display = 'flex';
                        loginButton.disabled = true;
                        loginButton.style.cursor = 'not-allowed';
                        loginButton.style.opacity = '0.8';
                    }
                });
            }

            // تحسين تأثيرات الروابط
            const links = document.querySelectorAll('.link-modern');
            links.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.2)';
                });

                link.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });

            // تحسين checkbox تذكرني
            const rememberCheckbox = document.getElementById('RememberMe');
            if (rememberCheckbox) {
                rememberCheckbox.addEventListener('change', function() {
                    const checkboxCustom = this.nextElementSibling.querySelector('.checkbox-custom');
                    if (checkboxCustom) {
                        if (this.checked) {
                            checkboxCustom.style.animation = 'checkboxPulse 0.3s ease';
                        }
                    }
                });
            }

            console.log('تم تحميل تحسينات التصميم الحديث');
        }

        // تحسين تفاعل الشعار
        function enhanceLogoInteraction() {
            const logoWrapper = document.querySelector('.logo-wrapper-new');
            if (logoWrapper) {
                logoWrapper.addEventListener('click', function() {
                    // إضافة تأثير نقر
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);

                    // إضافة تأثير دوران سريع
                    const logoRing = this.querySelector('.logo-ring');
                    const logoImage = this.querySelector('.logo-image');

                    if (logoRing) {
                        logoRing.style.animationDuration = '0.5s';
                        setTimeout(() => {
                            logoRing.style.animationDuration = '15s';
                        }, 1000);
                    }

                    if (logoImage) {
                        logoImage.style.animationDuration = '0.3s';
                        setTimeout(() => {
                            logoImage.style.animationDuration = '12s';
                        }, 1000);
                    }
                });

                // تأثير hover محسن
                logoWrapper.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                });

                logoWrapper.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            }
        }

        // تحسين تحميل الشعار
        function enhanceLogoLoading() {
            const logoImage = document.querySelector('.logo-image');
            if (logoImage) {
                // إضافة مؤشر تحميل
                const logoContainer = logoImage.parentElement;
                const loadingIndicator = document.createElement('div');
                loadingIndicator.className = 'logo-loading';
                loadingIndicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                loadingIndicator.style.cssText = `
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: white;
                    font-size: 24px;
                    z-index: 10;
                `;

                logoContainer.appendChild(loadingIndicator);

                // إخفاء مؤشر التحميل عند تحميل الصورة
                logoImage.addEventListener('load', function() {
                    loadingIndicator.style.display = 'none';
                    this.style.opacity = '1';
                });

                // معالجة خطأ التحميل
                logoImage.addEventListener('error', function() {
                    loadingIndicator.style.display = 'none';
                    console.log('فشل تحميل الشعار، جاري التبديل للشعار الافتراضي');
                });

                // إخفاء الصورة في البداية
                logoImage.style.opacity = '0';
                logoImage.style.transition = 'opacity 0.5s ease';
            }
        }

        // دالة مسح البيانات المحفوظة المحسنة
        function clearSavedData() {
            if (confirm('هل أنت متأكد من مسح البيانات المحفوظة؟')) {
                localStorage.removeItem('savedLoginData');
                sessionStorage.removeItem('savedLoginData');

                // إظهار رسالة تأكيد محسنة
                showNotification('تم مسح البيانات المحفوظة', 'success');

                // إخفاء زر المسح
                const clearBtn = document.getElementById('clearDataBtn');
                if (clearBtn) {
                    clearBtn.style.display = 'none';
                }
            }
        }

        // دالة إظهار الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `modern-notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            `;

            // إضافة CSS للإشعار
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, ${type === 'success' ? '#28a745, #20c997' : '#667eea, #764ba2'});
                color: white;
                padding: 15px 20px;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 10px;
                font-weight: 500;
                animation: slideInRight 0.5s ease-out;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.5s ease-out forwards';
                setTimeout(() => notification.remove(), 500);
            }, 3000);
        }
    </script>
}
