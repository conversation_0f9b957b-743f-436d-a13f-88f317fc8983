using EmpMgr.Models;

namespace EmpMgr.Services
{
    public interface ICacheService
    {
        Task<List<Rank>> GetRanksAsync();
        Task<List<Province>> GetProvincesAsync();
        Task<List<University>> GetUniversitiesAsync();
        Task<List<Institute>> GetInstitutesAsync();
        Task<List<College>> GetCollegesAsync();
        Task<List<Ministry>> GetMinistriesAsync();
        Task<List<Agency>> GetAgenciesAsync();
        Task<List<Directorate>> GetDirectoratesAsync();
        Task<List<GovernmentDepartment>> GetGovernmentDepartmentsAsync();
        Task<List<Division>> GetDivisionsAsync();
        
        void ClearCache();
        void ClearCache(string key);
    }
}
