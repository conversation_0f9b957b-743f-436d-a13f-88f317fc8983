using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using EmpMgr.Data;
using EmpMgr.Models;
using EmpMgr.Services;

namespace EmpMgr.Controllers
{
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly ApplicationDbContext _context;


        public DashboardController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Dashboard
        public async Task<IActionResult> Index()
        {
            var dashboardData = await GetDashboardDataAsync();
            return View(dashboardData);
        }

        // API: إحصائيات عامة
        [HttpGet]
        public async Task<JsonResult> GetGeneralStats()
        {
            var stats = new
            {
                TotalEmployees = await _context.Employees.CountAsync(),
                ActiveEmployees = await _context.Employees.CountAsync(), // يمكن إضافة شرط IsActive لاحقاً
                TotalRanks = await _context.Ranks.CountAsync(r => r.IsActive),
                TotalProvinces = await _context.Provinces.CountAsync(p => p.IsActive),
                NewEmployeesThisMonth = await _context.Employees
                    .CountAsync(e => e.CreatedDate.Month == DateTime.Now.Month && 
                                   e.CreatedDate.Year == DateTime.Now.Year),
                NewEmployeesToday = await _context.Employees
                    .CountAsync(e => e.CreatedDate.Date == DateTime.Now.Date)
            };

            return Json(stats);
        }

        // API: توزيع الموظفين حسب الرتب
        [HttpGet]
        public async Task<JsonResult> GetEmployeesByRank()
        {
            var data = await _context.Employees
                .Include(e => e.Rank)
                .Where(e => e.Rank != null)
                .GroupBy(e => e.Rank!.Name)
                .Select(g => new
                {
                    Rank = g.Key,
                    Count = g.Count(),
                    Percentage = Math.Round((double)g.Count() * 100 / _context.Employees.Count(), 1)
                })
                .OrderByDescending(x => x.Count)
                .ToListAsync();

            return Json(data);
        }

        // API: توزيع الموظفين حسب المحافظات
        [HttpGet]
        public async Task<JsonResult> GetEmployeesByProvince()
        {
            var data = await _context.Employees
                .Include(e => e.Province)
                .Where(e => e.Province != null)
                .GroupBy(e => e.Province!.Name)
                .Select(g => new
                {
                    Province = g.Key,
                    Count = g.Count(),
                    Percentage = Math.Round((double)g.Count() * 100 / _context.Employees.Count(), 1)
                })
                .OrderByDescending(x => x.Count)
                .Take(10) // أعلى 10 محافظات
                .ToListAsync();

            return Json(data);
        }

        // API: توزيع الموظفين حسب التحصيل الدراسي
        [HttpGet]
        public async Task<JsonResult> GetEmployeesByEducation()
        {
            var data = await _context.Employees
                .GroupBy(e => e.EducationLevel)
                .Select(g => new
                {
                    Education = g.Key.ToString(),
                    Count = g.Count(),
                    Percentage = Math.Round((double)g.Count() * 100 / _context.Employees.Count(), 1)
                })
                .ToListAsync();

            // ترجمة أسماء التحصيل الدراسي
            var translatedData = data.Select(d => new
            {
                Education = TranslateEducationLevel(d.Education),
                d.Count,
                d.Percentage
            }).ToList();

            return Json(translatedData);
        }

        // API: إحصائيات الموظفين الجدد (آخر 12 شهر)
        [HttpGet]
        public async Task<JsonResult> GetNewEmployeesChart()
        {
            var data = new List<object>();
            var currentDate = DateTime.Now;

            for (int i = 11; i >= 0; i--)
            {
                var targetDate = currentDate.AddMonths(-i);
                var count = await _context.Employees
                    .CountAsync(e => e.CreatedDate.Month == targetDate.Month && 
                               e.CreatedDate.Year == targetDate.Year);

                data.Add(new
                {
                    Month = targetDate.ToString("yyyy-MM"),
                    MonthName = GetArabicMonthName(targetDate.Month),
                    Count = count
                });
            }

            return Json(data);
        }

        // API: توزيع الموظفين حسب الجنس
        [HttpGet]
        public async Task<JsonResult> GetEmployeesByGender()
        {
            var data = await _context.Employees
                .GroupBy(e => e.Gender)
                .Select(g => new
                {
                    Gender = g.Key == Gender.Male ? "ذكر" : "أنثى",
                    Count = g.Count(),
                    Percentage = Math.Round((double)g.Count() * 100 / _context.Employees.Count(), 1)
                })
                .ToListAsync();

            return Json(data);
        }

        // API: توزيع الموظفين حسب الحالة الاجتماعية
        [HttpGet]
        public async Task<JsonResult> GetEmployeesByMaritalStatus()
        {
            var data = await _context.Employees
                .GroupBy(e => e.MaritalStatus)
                .Select(g => new
                {
                    Status = TranslateMaritalStatus(g.Key),
                    Count = g.Count(),
                    Percentage = Math.Round((double)g.Count() * 100 / _context.Employees.Count(), 1)
                })
                .ToListAsync();

            return Json(data);
        }

        // API: أحدث الموظفين المضافين
        [HttpGet]
        public async Task<JsonResult> GetRecentEmployees(int count = 5)
        {
            var data = await _context.Employees
                .Include(e => e.Rank)
                .Include(e => e.Province)
                .OrderByDescending(e => e.CreatedDate)
                .Take(count)
                .Select(e => new
                {
                    e.Id,
                    e.EmployeeNumber,
                    FullName = e.FullName,
                    Rank = e.Rank != null ? e.Rank.Name : "غير محدد",
                    Province = e.Province != null ? e.Province.Name : "غير محدد",
                    CreatedDate = e.CreatedDate.ToString("yyyy-MM-dd"),
                    HasPhoto = false
                })
                .ToListAsync();

            return Json(data);
        }



        // دالة مساعدة للحصول على بيانات Dashboard
        private async Task<object> GetDashboardDataAsync()
        {
            var totalEmployees = await _context.Employees.CountAsync();
            var totalRanks = await _context.Ranks.CountAsync(r => r.IsActive);
            var totalProvinces = await _context.Provinces.CountAsync(p => p.IsActive);
            var newThisMonth = await _context.Employees
                .CountAsync(e => e.CreatedDate.Month == DateTime.Now.Month && 
                           e.CreatedDate.Year == DateTime.Now.Year);

            return new
            {
                TotalEmployees = totalEmployees,
                TotalRanks = totalRanks,
                TotalProvinces = totalProvinces,
                NewThisMonth = newThisMonth
            };
        }

        // ترجمة مستوى التعليم
        private string TranslateEducationLevel(string educationLevel)
        {
            return educationLevel switch
            {
                "Primary" => "ابتدائية",
                "Intermediate" => "متوسطة",
                "Secondary" => "إعدادية",
                "Diploma" => "دبلوم",
                "Bachelor" => "بكالوريوس",
                "Master" => "ماجستير",
                "PhD" => "دكتوراه",
                _ => educationLevel
            };
        }

        // ترجمة الحالة الاجتماعية
        private string TranslateMaritalStatus(MaritalStatus status)
        {
            return status switch
            {
                MaritalStatus.Single => "أعزب",
                MaritalStatus.Married => "متزوج",
                MaritalStatus.Divorced => "مطلق",
                MaritalStatus.Widowed => "أرمل",
                _ => status.ToString()
            };
        }

        // الحصول على اسم الشهر بالعربية
        private string GetArabicMonthName(int month)
        {
            string[] months = {
                "", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            };
            return months[month];
        }
    }
}
