﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmpMgr.Migrations
{
    /// <inheritdoc />
    public partial class UpdateSeniorityToMonths : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SeniorityRequiredYears",
                table: "Ranks");

            migrationBuilder.AddColumn<int>(
                name: "SeniorityRequiredMonths",
                table: "Ranks",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SeniorityRequiredMonths",
                table: "Ranks");

            migrationBuilder.AddColumn<int>(
                name: "SeniorityRequiredYears",
                table: "Ranks",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }
    }
}
