@model EmpMgr.Models.Employee
@{
    ViewData["Title"] = "حساب الترقية";
    var eligibilityResult = ViewBag.EligibilityResult as EmpMgr.Services.PromotionEligibilityResult;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- رأس الصفحة -->
            <div class="page-header-calculate">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-calculator text-primary me-3"></i>
                            حساب الترقية
                        </h1>
                        <p class="page-subtitle">تفاصيل حساب ترقية الموظف: @Model.FullName</p>
                    </div>
                    <div class="page-actions">
                        <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات الموظف -->
        <div class="col-lg-4">
            <div class="employee-info-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-user text-primary me-2"></i>
                        معلومات الموظف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="employee-details">
                        <div class="detail-item">
                            <label>الاسم الكامل:</label>
                            <span>@Model.FullName</span>
                        </div>
                        <div class="detail-item">
                            <label>الرقم الوظيفي:</label>
                            <span>@Model.EmployeeNumber</span>
                        </div>
                        <div class="detail-item">
                            <label>الرتبة الحالية:</label>
                            <span class="rank-badge">@Model.CurrentRank?.Name</span>
                        </div>
                        <div class="detail-item">
                            <label>تاريخ التعيين:</label>
                            <span>@Model.AppointmentDate.ToString("yyyy/MM/dd")</span>
                        </div>
                        <div class="detail-item">
                            <label>تاريخ آخر ترقية:</label>
                            <span>
                                @if (Model.LastPromotionDate.HasValue)
                                {
                                    @Model.LastPromotionDate.Value.ToString("yyyy/MM/dd")
                                }
                                else
                                {
                                    <span class="text-muted">لا توجد ترقيات سابقة</span>
                                }
                            </span>
                        </div>
                        <div class="detail-item">
                            <label>المحافظة:</label>
                            <span>@Model.Province?.Name</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نتائج حساب الترقية -->
        <div class="col-lg-8">
            @if (eligibilityResult != null)
            {
                <!-- حالة الأهلية -->
                <div class="eligibility-status-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            حالة الأهلية للترقية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="status-display">
                            @if (eligibilityResult.IsEligible)
                            {
                                <div class="status-badge status-eligible">
                                    <i class="fas fa-check-circle"></i>
                                    <span>مؤهل للترقية</span>
                                </div>
                            }
                            else
                            {
                                <div class="status-badge status-not-eligible">
                                    <i class="fas fa-clock"></i>
                                    <span>غير مؤهل حالياً</span>
                                </div>
                            }
                            <p class="status-description">@eligibilityResult.Status</p>
                        </div>

                        <div class="promotion-timeline">
                            <div class="timeline-item">
                                <div class="timeline-icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="timeline-content">
                                    <h6>تاريخ الاستحقاق المتوقع</h6>
                                    <p>
                                        @if (eligibilityResult.NextPromotionDate.HasValue)
                                        {
                                            <strong>@eligibilityResult.NextPromotionDate.Value.ToString("yyyy/MM/dd")</strong>
                                        }
                                        else
                                        {
                                            <span class="text-muted">غير محدد</span>
                                        }
                                    </p>
                                </div>
                            </div>

                            @if (eligibilityResult.DaysUntilEligible > 0)
                            {
                                <div class="timeline-item">
                                    <div class="timeline-icon">
                                        <i class="fas fa-hourglass-half"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>الأيام المتبقية</h6>
                                        <p><strong>@eligibilityResult.DaysUntilEligible يوم</strong></p>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- متطلبات الترقية -->
                <div class="requirements-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-list-check text-info me-2"></i>
                            متطلبات الترقية
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (eligibilityResult.Requirements.Any())
                        {
                            <div class="requirements-list">
                                <h6 class="requirements-title">المتطلبات العامة:</h6>
                                <ul class="requirements-ul">
                                    @foreach (var requirement in eligibilityResult.Requirements)
                                    {
                                        <li class="requirement-item">
                                            <i class="fas fa-check text-success me-2"></i>
                                            @requirement
                                        </li>
                                    }
                                </ul>
                            </div>
                        }

                        @if (eligibilityResult.MissingRequirements.Any())
                        {
                            <div class="missing-requirements">
                                <h6 class="missing-title">المتطلبات المفقودة:</h6>
                                <ul class="missing-ul">
                                    @foreach (var missing in eligibilityResult.MissingRequirements)
                                    {
                                        <li class="missing-item">
                                            <i class="fas fa-times text-danger me-2"></i>
                                            @missing
                                        </li>
                                    }
                                </ul>
                            </div>
                        }
                    </div>
                </div>

                <!-- تفاصيل الحساب -->
                <div class="calculation-details-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-calculator text-warning me-2"></i>
                            تفاصيل الحساب
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="calculation-grid">
                            <div class="calc-item">
                                <label>مدة الرتبة المطلوبة:</label>
                                <span>@Model.CurrentRank?.RankDurationYears سنوات</span>
                            </div>

                            @if (Model.CurrentRank?.MinAgeForPromotion.HasValue == true)
                            {
                                <div class="calc-item">
                                    <label>الحد الأدنى للعمر:</label>
                                    <span>@Model.CurrentRank.MinAgeForPromotion سنة</span>
                                </div>
                            }
                            @if (Model.CurrentRank?.MaxAgeForPromotion.HasValue == true)
                            {
                                <div class="calc-item">
                                    <label>الحد الأقصى للعمر:</label>
                                    <span>@Model.CurrentRank.MaxAgeForPromotion سنة</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    لا يمكن حساب الترقية لهذا الموظف. تأكد من وجود رتبة محددة.
                </div>
            }
        </div>
    </div>
</div>

<style>
    .page-header-calculate {
        background: linear-gradient(135deg, #f8f9fa, #ffffff);
        border-radius: 16px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }

    .employee-info-card,
    .eligibility-status-card,
    .requirements-card,
    .calculation-details-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        margin-bottom: 20px;
        overflow: hidden;
    }

    .employee-info-card .card-header,
    .eligibility-status-card .card-header,
    .requirements-card .card-header,
    .calculation-details-card .card-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 15px 20px;
        border: none;
    }

    .card-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
    }

    .employee-details {
        padding: 10px 0;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .detail-item:last-child {
        border-bottom: none;
    }

    .detail-item label {
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }

    .detail-item span {
        color: #6c757d;
    }

    .rank-badge {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-display {
        text-align: center;
        margin-bottom: 30px;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 10px;
        padding: 15px 25px;
        border-radius: 25px;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
    }

    .status-eligible {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }

    .status-not-eligible {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: white;
    }

    .status-description {
        font-size: 16px;
        color: #6c757d;
        margin: 0;
    }

    .promotion-timeline {
        margin-top: 20px;
    }

    .timeline-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .timeline-item:last-child {
        border-bottom: none;
    }

    .timeline-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
    }

    .timeline-content h6 {
        margin: 0 0 5px 0;
        color: #2c3e50;
        font-weight: 600;
    }

    .timeline-content p {
        margin: 0;
        color: #6c757d;
    }

    .requirements-list,
    .missing-requirements {
        margin-bottom: 20px;
    }

    .requirements-title,
    .missing-title {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 15px;
    }

    .requirements-ul,
    .missing-ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .requirement-item,
    .missing-item {
        padding: 8px 0;
        display: flex;
        align-items: center;
    }

    .calculation-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }

    .calc-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 10px;
    }

    .calc-item label {
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }

    .calc-item span {
        color: #667eea;
        font-weight: 600;
    }

    @@media (max-width: 768px) {
        .calculation-grid {
            grid-template-columns: 1fr;
        }

        .detail-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
        }

        .calc-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
        }
    }
</style>
