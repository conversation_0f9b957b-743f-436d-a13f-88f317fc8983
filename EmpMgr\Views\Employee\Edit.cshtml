@model EmpMgr.ViewModels.EmployeeEditViewModel
@using EmpMgr.Models
@{
    ViewData["Title"] = "تعديل بيانات الموظف";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        تعديل بيانات الموظف
                    </h4>
                </div>

                <div class="card-body">
                    @using (Html.BeginForm("Edit", "Employee", FormMethod.Post, new { enctype = "multipart/form-data", id = "employeeForm" }))
                    {
                        @Html.HiddenFor(m => m.Id)
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>

                        <!-- الصورة الشخصية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-camera me-2"></i>
                                    الصورة الشخصية
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                @{
                                    ViewBag.HasCurrentPhoto = Model.CurrentPhoto != null;
                                    ViewBag.CurrentPhotoBase64 = Model.CurrentPhoto != null ? Convert.ToBase64String(Model.CurrentPhoto) : "";
                                }
                                @await Html.PartialAsync("_PhotoUpload")
                            </div>
                        </div>

                        <!-- التبويبات المحسنة -->
                        <ul class="nav nav-tabs enhanced-tabs" id="employeeTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="basic-info-tab" data-bs-toggle="tab"
                                        data-bs-target="#basic-info" type="button" role="tab">
                                    <div class="tab-icon-wrapper">
                                        <i class="fas fa-id-card text-primary"></i>
                                    </div>
                                    <div class="tab-content-wrapper">
                                        <span class="tab-title">المعلومات الأساسية</span>
                                        <small class="tab-subtitle">البيانات الشخصية والعنوان</small>
                                    </div>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="job-info-tab" data-bs-toggle="tab"
                                        data-bs-target="#job-info" type="button" role="tab">
                                    <div class="tab-icon-wrapper">
                                        <i class="fas fa-briefcase text-success"></i>
                                    </div>
                                    <div class="tab-content-wrapper">
                                        <span class="tab-title">البيانات الوظيفية</span>
                                        <small class="tab-subtitle">التحصيل الدراسي والبنية الهرمية</small>
                                    </div>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="promotion-allowance-tab" data-bs-toggle="tab"
                                        data-bs-target="#promotion-allowance" type="button" role="tab">
                                    <div class="tab-icon-wrapper">
                                        <i class="fas fa-chart-line text-warning"></i>
                                    </div>
                                    <div class="tab-content-wrapper">
                                        <span class="tab-title">الترقية والعلاوة</span>
                                        <small class="tab-subtitle">التعيين والترقيات والعلاوات</small>
                                    </div>
                                </button>
                            </li>
                        </ul>

                        <!-- محتوى التبويبات -->
                        <div class="tab-content mt-4" id="employeeTabsContent">
                            <!-- المعلومات الأساسية -->
                            <div class="tab-pane fade show active" id="basic-info" role="tabpanel">
                                <!-- البيانات الشخصية -->
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">البيانات الشخصية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="EmployeeNumber" class="form-label">رقم الموظف</label>
                                                <input asp-for="EmployeeNumber" class="form-control" readonly style="background-color: #f8f9fa;">
                                                <span asp-validation-for="EmployeeNumber" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="FirstName" class="form-label">الاسم الأول</label>
                                                <input asp-for="FirstName" class="form-control" placeholder="أدخل الاسم الأول" oninput="validateNameField(this)">
                                                <span asp-validation-for="FirstName" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="FatherName" class="form-label">اسم الأب</label>
                                                <input asp-for="FatherName" class="form-control" placeholder="أدخل اسم الأب" oninput="validateNameField(this)">
                                                <span asp-validation-for="FatherName" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="GrandFatherName" class="form-label">اسم الجد</label>
                                                <input asp-for="GrandFatherName" class="form-control" placeholder="أدخل اسم الجد" oninput="validateNameField(this)">
                                                <span asp-validation-for="GrandFatherName" class="text-danger"></span>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="GreatGrandFatherName" class="form-label">اسم والد الجد</label>
                                                <input asp-for="GreatGrandFatherName" class="form-control" placeholder="أدخل اسم والد الجد" oninput="validateNameField(this)">
                                                <span asp-validation-for="GreatGrandFatherName" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="LastName" class="form-label">اللقب</label>
                                                <input asp-for="LastName" class="form-control" placeholder="أدخل اللقب" oninput="validateNameField(this)">
                                                <span asp-validation-for="LastName" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="DateOfBirth" class="form-label">تاريخ الولادة</label>
                                                <input asp-for="DateOfBirth" type="date" class="form-control" id="dateOfBirth" max="@DateTime.Now.ToString("yyyy-MM-dd")" min="1950-01-01">
                                                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">العمر (بالتفصيل)</label>
                                                <input type="text" class="form-control" id="calculatedAge" readonly placeholder="سيتم حساب العمر تلقائياً بالسنة والشهر واليوم" style="background-color: #f8f9fa; font-weight: bold; color: #0d6efd;">
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="PlaceOfBirth" class="form-label">مكان الولادة</label>
                                                <input asp-for="PlaceOfBirth" class="form-control" placeholder="أدخل مكان الولادة">
                                                <span asp-validation-for="PlaceOfBirth" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="Gender" class="form-label">الجنس</label>
                                                <select asp-for="Gender" class="form-select" asp-items="Html.GetEnumSelectList<Gender>()">
                                                    <option value="">اختر الجنس</option>
                                                </select>
                                                <span asp-validation-for="Gender" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="MaritalStatus" class="form-label">الحالة الاجتماعية</label>
                                                <select asp-for="MaritalStatus" class="form-select" asp-items="Html.GetEnumSelectList<MaritalStatus>()">
                                                    <option value="">اختر الحالة الاجتماعية</option>
                                                </select>
                                                <span asp-validation-for="MaritalStatus" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="BloodType" class="form-label">فصيلة الدم</label>
                                                <select asp-for="BloodType" class="form-select" asp-items="Html.GetEnumSelectList<BloodType>()">
                                                    <option value="">اختر فصيلة الدم</option>
                                                </select>
                                                <span asp-validation-for="BloodType" class="text-danger"></span>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="HealthStatus" class="form-label">الحالة الصحية</label>
                                                <select asp-for="HealthStatus" class="form-select" asp-items="Html.GetEnumSelectList<HealthStatus>()">
                                                    <option value="">اختر الحالة الصحية</option>
                                                </select>
                                                <span asp-validation-for="HealthStatus" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="StatisticalNumber" class="form-label">الرقم الإحصائي</label>
                                                <input asp-for="StatisticalNumber" class="form-control" placeholder="أدخل الرقم الإحصائي">
                                                <span asp-validation-for="StatisticalNumber" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="RankId" class="form-label">الرتبة</label>
                                                <select asp-for="RankId" class="form-select" id="RankId">
                                                    <option value="">اختر الرتبة</option>
                                                    @foreach (var rank in Model.Ranks)
                                                    {
                                                        <option value="@rank.Id" data-rank-duration="@rank.RankDurationYears">@rank.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="RankId" class="text-danger"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات الاتصال والعنوان -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">معلومات الاتصال والعنوان السكن</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="ProvinceId" class="form-label">المحافظة</label>
                                                <select asp-for="ProvinceId" class="form-select">
                                                    <option value="">اختر المحافظة</option>
                                                    @foreach (var province in Model.Provinces)
                                                    {
                                                        <option value="@province.Id">@province.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="ProvinceId" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="District" class="form-label">القضاء</label>
                                                <input asp-for="District" class="form-control" placeholder="أدخل القضاء">
                                                <span asp-validation-for="District" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="Subdistrict" class="form-label">الناحية</label>
                                                <input asp-for="Subdistrict" class="form-control" placeholder="أدخل الناحية">
                                                <span asp-validation-for="Subdistrict" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="Village" class="form-label">القرية/المنطقة</label>
                                                <input asp-for="Village" class="form-control" placeholder="أدخل القرية">
                                                <span asp-validation-for="Village" class="text-danger"></span>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="Neighborhood" class="form-label">الحي</label>
                                                <input asp-for="Neighborhood" class="form-control" placeholder="أدخل الحي">
                                                <span asp-validation-for="Neighborhood" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="Quarter" class="form-label">المحلة</label>
                                                <input asp-for="Quarter" class="form-control" placeholder="أدخل المحلة">
                                                <span asp-validation-for="Quarter" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="Alley" class="form-label">الزقاق</label>
                                                <input asp-for="Alley" class="form-control" placeholder="أدخل الزقاق">
                                                <span asp-validation-for="Alley" class="text-danger"></span>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="House" class="form-label">رقم الدار</label>
                                                <input asp-for="House" class="form-control" placeholder="أدخل الدار">
                                                <span asp-validation-for="House" class="text-danger"></span>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label asp-for="NearestLandmark" class="form-label">أقرب معلم</label>
                                                <input asp-for="NearestLandmark" class="form-control" placeholder="أدخل أقرب معلم">
                                                <span asp-validation-for="NearestLandmark" class="text-danger"></span>
                                            </div>

                                            <div class="col-md-12 mb-3">
                                                <label class="form-label fw-bold text-primary">
                                                    <i class="fas fa-map-marker-alt me-1"></i>العنوان الكامل
                                                </label>
                                                <textarea class="form-control" id="fullAddress" rows="3"
                                                          readonly placeholder="سيتم تكوين العنوان تلقائياً عند ملء حقول العنوان"
                                                          style="resize: vertical; min-height: 80px; background-color: #f8f9fa; border: 2px dashed #dee2e6; font-weight: 500; font-size: 14px;"></textarea>
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    يتم تكوين العنوان تلقائياً بالتنسيق: "محافظة [المحافظة] - قضاء [القضاء] - ناحية [الناحية] - قرية [القرية] - حي [الحي] - محلة [المحلة] - زقاق [الزقاق] - دار [الدار] - قرب [أقرب نقطة دالة]"
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- البيانات الوظيفية -->
                            <div class="tab-pane fade" id="job-info" role="tabpanel">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">التحصيل الدراسي</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4 mb-3">
                                                <label asp-for="EducationLevel" class="form-label"></label>
                                                <select asp-for="EducationLevel" class="form-select" id="EducationLevel"
                                                        asp-items="Html.GetEnumSelectList<EducationLevel>()"
                                                        onchange="toggleEducationFields();">
                                                    <option value="">اختر التحصيل الدراسي</option>
                                                </select>
                                                <span asp-validation-for="EducationLevel" class="text-danger"></span>
                                            </div>

                                            <!-- حقول الجامعة والكلية (للبكالوريوس وما فوق) -->
                                            <div class="col-md-4 mb-3" id="universityField" style="display: none;">
                                                <label asp-for="UniversityId" class="form-label"></label>
                                                <select asp-for="UniversityId" class="form-select" id="UniversityId" onchange="loadColleges()">
                                                    <option value="">اختر الجامعة</option>
                                                    @foreach (var university in Model.Universities)
                                                    {
                                                        <option value="@university.Id" selected="@(university.Id == Model.UniversityId)">@university.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="UniversityId" class="text-danger"></span>
                                            </div>

                                            <div class="col-md-4 mb-3" id="collegeField" style="display: none;">
                                                <label asp-for="CollegeId" class="form-label"></label>
                                                <select asp-for="CollegeId" class="form-select" id="CollegeId" data-current-value="@Model.CollegeId" onchange="loadAcademicDepartments()">
                                                    <option value="">اختر الكلية</option>
                                                    @foreach (var college in Model.Colleges)
                                                    {
                                                        <option value="@college.Id" selected="@(college.Id == Model.CollegeId)">@college.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="CollegeId" class="text-danger"></span>
                                            </div>

                                            <div class="col-md-4 mb-3" id="academicDepartmentField" style="display: none;">
                                                <label asp-for="AcademicDepartmentId" class="form-label"></label>
                                                <select asp-for="AcademicDepartmentId" class="form-select" id="AcademicDepartmentId" data-current-value="@Model.AcademicDepartmentId">
                                                    <option value="">اختر القسم الأكاديمي</option>
                                                    @foreach (var academicDepartment in Model.AcademicDepartments)
                                                    {
                                                        <option value="@academicDepartment.Id" selected="@(academicDepartment.Id == Model.AcademicDepartmentId)">@academicDepartment.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="AcademicDepartmentId" class="text-danger"></span>
                                            </div>
                                        </div>

                                        <!-- حقول المعهد والقسم (للدبلوم) -->
                                        <div class="row">
                                            <div class="col-md-4 mb-3" id="instituteField" style="display: none;">
                                                <label asp-for="InstituteId" class="form-label"></label>
                                                <select asp-for="InstituteId" class="form-select" id="InstituteId" onchange="loadDepartments()">
                                                    <option value="">اختر المعهد</option>
                                                    @foreach (var institute in Model.Institutes)
                                                    {
                                                        <option value="@institute.Id" selected="@(institute.Id == Model.InstituteId)">@institute.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="InstituteId" class="text-danger"></span>
                                            </div>

                                            <div class="col-md-4 mb-3" id="departmentField" style="display: none;">
                                                <label asp-for="DepartmentId" class="form-label"></label>
                                                <select asp-for="DepartmentId" class="form-select" id="DepartmentId" data-current-value="@Model.DepartmentId">
                                                    <option value="">اختر القسم</option>
                                                    @foreach (var department in Model.Departments)
                                                    {
                                                        <option value="@department.Id" selected="@(department.Id == Model.DepartmentId)">@department.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="DepartmentId" class="text-danger"></span>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-12 mb-3" id="specializationField" style="display: none;">
                                                <label asp-for="Specialization" class="form-label"></label>
                                                <input asp-for="Specialization" class="form-control" id="Specialization" placeholder="أدخل التخصص" />
                                                <span asp-validation-for="Specialization" class="text-danger"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- البنية الهرمية الحكومية -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">البنية الهرمية الحكومية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="MinistryId" class="form-label"></label>
                                                <select asp-for="MinistryId" class="form-select" onchange="loadAgencies()">
                                                    <option value="">اختر الوزارة</option>
                                                    @foreach (var ministry in Model.Ministries)
                                                    {
                                                        <option value="@ministry.Id" selected="@(ministry.Id == Model.MinistryId)">@ministry.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="MinistryId" class="text-danger"></span>
                                            </div>

                                            <div class="col-md-3 mb-3">
                                                <label asp-for="AgencyId" class="form-label"></label>
                                                <select asp-for="AgencyId" class="form-select" onchange="loadDirectorates()">
                                                    <option value="">اختر الوكالة</option>
                                                    @foreach (var agency in Model.Agencies)
                                                    {
                                                        <option value="@agency.Id" selected="@(agency.Id == Model.AgencyId)">@agency.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="AgencyId" class="text-danger"></span>
                                            </div>

                                            <div class="col-md-3 mb-3">
                                                <label asp-for="DirectorateId" class="form-label"></label>
                                                <select asp-for="DirectorateId" class="form-select" onchange="loadGovernmentDepartments()">
                                                    <option value="">اختر المديرية</option>
                                                    @foreach (var directorate in Model.Directorates)
                                                    {
                                                        <option value="@directorate.Id" selected="@(directorate.Id == Model.DirectorateId)">@directorate.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="DirectorateId" class="text-danger"></span>
                                            </div>

                                            <div class="col-md-3 mb-3">
                                                <label asp-for="GovernmentDepartmentId" class="form-label"></label>
                                                <select asp-for="GovernmentDepartmentId" class="form-select" onchange="loadDivisions()">
                                                    <option value="">اختر القسم الحكومي</option>
                                                    @foreach (var govDept in Model.GovernmentDepartments)
                                                    {
                                                        <option value="@govDept.Id" selected="@(govDept.Id == Model.GovernmentDepartmentId)">@govDept.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="GovernmentDepartmentId" class="text-danger"></span>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-3 mb-3">
                                                <label asp-for="DivisionId" class="form-label"></label>
                                                <select asp-for="DivisionId" class="form-select">
                                                    <option value="">اختر الشعبة</option>
                                                    @foreach (var division in Model.Divisions)
                                                    {
                                                        <option value="@division.Id" selected="@(division.Id == Model.DivisionId)">@division.Name</option>
                                                    }
                                                </select>
                                                <span asp-validation-for="DivisionId" class="text-danger"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الترقية والعلاوة -->
                            <div class="tab-pane fade" id="promotion-allowance" role="tabpanel">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">معلومات التعيين والترقية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <!-- تاريخ التعيين -->
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-calendar-plus me-1 text-primary"></i>
                                                    تاريخ التعيين
                                                </label>
                                                <input asp-for="AppointmentDate" type="date" class="form-control" id="AppointmentDate"
                                                       max="@DateTime.Now.ToString("yyyy-MM-dd")"
                                                       required />
                                                <span asp-validation-for="AppointmentDate" class="text-danger"></span>
                                            </div>

                                            <!-- تاريخ آخر ترقية -->
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-arrow-up me-1"></i>
                                                    تاريخ آخر ترقية
                                                </label>
                                                <input asp-for="LastPromotionDate" type="date" class="form-control" id="lastPromotionDate"
                                                       max="@DateTime.Now.ToString("yyyy-MM-dd")"
                                                       placeholder="اختر تاريخ آخر ترقية" />
                                                <small class="text-muted">اتركه فارغاً إذا لم يحصل على ترقية</small>
                                            </div>

                                            <!-- تاريخ استحقاق الترقية المحسوب -->
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-calendar-check me-1 text-success"></i>
                                                    تاريخ استحقاق الترقية
                                                </label>
                                                <input asp-for="CalculatedPromotionDate" type="date" class="form-control" id="calculatedPromotionDate"
                                                       readonly style="background-color: #f8f9fa;" />
                                                <small class="text-muted">يحسب تلقائياً بناءً على البيانات المدخلة</small>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <!-- ملاحظات الترقية والعلاوة -->
                                            <div class="col-12 mb-3">
                                                <label class="form-label">
                                                    <i class="fas fa-sticky-note me-1"></i>
                                                    ملاحظات الترقية والعلاوة
                                                </label>
                                                <textarea asp-for="PromotionNotes" class="form-control" rows="3"
                                                          placeholder="أدخل أي ملاحظات متعلقة بالترقيات والعلاوات..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        العودة للقائمة
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ التغييرات
                                    </button>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <style>
        /* تحسينات التبويبات */
        .enhanced-tabs {
            border-bottom: 3px solid #e9ecef;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px 10px 0 0;
            padding: 10px 0;
        }

        .enhanced-tabs .nav-link {
            border: none;
            border-radius: 15px;
            margin: 0 5px;
            padding: 15px 20px;
            background: transparent;
            color: #6c757d;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            min-height: 70px;
        }

        .enhanced-tabs .nav-link:hover {
            background: rgba(13, 110, 253, 0.1);
            color: #0d6efd;
            transform: translateY(-2px);
        }

        .enhanced-tabs .nav-link.active {
            background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
        }

        .tab-icon-wrapper {
            font-size: 1.5rem;
            min-width: 30px;
        }

        .tab-content-wrapper {
            display: flex;
            flex-direction: column;
            text-align: right;
        }

        .tab-title {
            font-size: 1rem;
            font-weight: 600;
            line-height: 1.2;
        }

        .tab-subtitle {
            font-size: 0.8rem;
            opacity: 0.8;
            margin-top: 2px;
        }

        /* تحسينات البطاقات */
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 30px rgba(0,0,0,0.12);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 2px solid #dee2e6;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
        }

        .card-header h6 {
            color: #495057;
            font-weight: 600;
            margin: 0;
        }

        /* تحسينات الحقول */
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
            transform: translateY(-1px);
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        /* تحسينات الأزرار */
        .btn {
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        /* تحسينات خاصة بالعناوين */
        .text-primary {
            color: #0d6efd !important;
        }

        .border-bottom {
            border-bottom: 2px solid #e9ecef !important;
        }
    </style>

    <script>
        // دالة إظهار رسالة النجاح
        function showSuccessMessage(message) {
            // إنشاء عنصر الرسالة
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // إضافة الرسالة إلى الصفحة
            document.body.appendChild(alertDiv);

            // إزالة الرسالة تلقائياً بعد 3 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // دالة التحقق من صحة حقول الأسماء
        function validateNameField(input) {
            // إزالة الأرقام والرموز والعمليات الحسابية
            input.value = input.value.replace(/[^أ-يa-zA-Z\s]/g, '');
        }

        // تبديل حقول التعليم حسب المستوى
        function toggleEducationFields() {
            try {
                console.log('toggleEducationFields called');

                const educationLevel = document.getElementById('EducationLevel');
                if (!educationLevel) {
                    console.error('EducationLevel element not found');
                    return;
                }

                const selectedValue = educationLevel.value;
                console.log('Education level selected:', selectedValue, 'Type:', typeof selectedValue);
                console.log('Education level options:', Array.from(educationLevel.options).map(opt => ({value: opt.value, text: opt.text, selected: opt.selected})));

                const universityField = document.getElementById('universityField');
                const collegeField = document.getElementById('collegeField');
                const academicDepartmentField = document.getElementById('academicDepartmentField');
                const instituteField = document.getElementById('instituteField');
                const departmentField = document.getElementById('departmentField');
                const specializationField = document.getElementById('specializationField');

                console.log('Fields found:', {
                    universityField: !!universityField,
                    collegeField: !!collegeField,
                    academicDepartmentField: !!academicDepartmentField,
                    instituteField: !!instituteField,
                    departmentField: !!departmentField,
                    specializationField: !!specializationField
                });

                // إخفاء جميع الحقول أولاً
                console.log('Hiding all education fields');
                if (universityField) {
                    universityField.style.display = 'none';
                    console.log('University field hidden');
                }
                if (collegeField) {
                    collegeField.style.display = 'none';
                    console.log('College field hidden');
                }
                if (academicDepartmentField) {
                    academicDepartmentField.style.display = 'none';
                    console.log('Academic department field hidden');
                }
                if (instituteField) {
                    instituteField.style.display = 'none';
                    console.log('Institute field hidden');
                }
                if (departmentField) {
                    departmentField.style.display = 'none';
                    console.log('Department field hidden');
                }
                if (specializationField) {
                    specializationField.style.display = 'none';
                    console.log('Specialization field hidden');
                }

                // إظهار الحقول المناسبة بناءً على مستوى التعليم
                // القيم الرقمية: Bachelor=5, Master=6, PhD=7, Diploma=4
                console.log('Checking education level value for field display');
                switch (selectedValue) {
                    case 'Bachelor':
                    case 'Master':
                    case 'PhD':
                    case '5': // Bachelor
                    case '6': // Master
                    case '7': // PhD
                        console.log('Showing university fields for value:', selectedValue);
                        if (universityField) {
                            universityField.style.display = 'block';
                            console.log('University field shown');
                        }
                        if (collegeField) {
                            collegeField.style.display = 'block';
                            console.log('College field shown');
                        }
                        if (academicDepartmentField) {
                            academicDepartmentField.style.display = 'block';
                            console.log('Academic department field shown');
                        }
                        if (specializationField) {
                            specializationField.style.display = 'block';
                            console.log('Specialization field shown');
                        }

                        // تحميل الكليات والأقسام الأكاديمية عند تغيير التحصيل الدراسي
                        setTimeout(function() {
                            const universityId = $('#UniversityId').val();
                            console.log('University ID after showing fields:', universityId);
                            if (universityId) {
                                console.log('Loading colleges for university after education level change');
                                // استخدام false لإجبار إعادة التحميل من الخادم
                                loadColleges(false);
                            } else {
                                console.log('No university selected, clearing college and academic department fields');
                                $('#CollegeId').html('<option value="">اختر الكلية</option>');
                                $('#AcademicDepartmentId').html('<option value="">اختر القسم الأكاديمي</option>');
                            }
                        }, 300); // زيادة التأخير أكثر للتأكد من ظهور الحقول

                        break;
                    case 'Diploma':
                    case '4': // Diploma
                        console.log('Showing institute fields for value:', selectedValue);
                        if (instituteField) {
                            instituteField.style.display = 'block';
                            console.log('Institute field shown');
                        }
                        if (departmentField) {
                            departmentField.style.display = 'block';
                            console.log('Department field shown');
                        }
                        if (specializationField) {
                            specializationField.style.display = 'block';
                            console.log('Specialization field shown');
                        }

                        // تحميل الأقسام عند تغيير التحصيل الدراسي
                        setTimeout(function() {
                            const instituteId = $('#InstituteId').val();
                            console.log('Institute ID after showing fields:', instituteId);
                            if (instituteId) {
                                console.log('Loading departments for institute after education level change');
                                // استخدام false لإجبار إعادة التحميل من الخادم
                                loadDepartments(false);
                            } else {
                                console.log('No institute selected, clearing department field');
                                $('#DepartmentId').html('<option value="">اختر القسم</option>');
                            }
                        }, 300); // زيادة التأخير أكثر للتأكد من ظهور الحقول

                        break;
                    default:
                        console.log('No education fields to show for value:', selectedValue);
                        console.log('Available cases: Bachelor, Master, PhD, 5, 6, 7, Diploma, 4');
                }

                console.log('toggleEducationFields completed');
            } catch (error) {
                console.error('Error in toggleEducationFields:', error);
            }
        }

        // تحميل الكليات
        function loadColleges(preserveSelection = false) {
            const universityId = $('#UniversityId').val();
            const collegeSelect = $('#CollegeId');

            // الحصول على الكلية المحددة حالياً
            const currentCollegeId = preserveSelection ?
                (collegeSelect.data('current-value') || '@Model.CollegeId') :
                collegeSelect.val();

            console.log('Loading colleges for university:', universityId, 'Current college:', currentCollegeId, 'Preserve selection:', preserveSelection);

            // التحقق من وجود كليات محملة مسبقاً من الخادم (من Controller)
            const existingOptions = collegeSelect.find('option[value!=""]');
            console.log('Existing college options:', existingOptions.length);

            // إذا كانت الكليات محملة مسبقاً ونريد الحفاظ على التحديد، لا نحمل من جديد
            if (preserveSelection && existingOptions.length > 0) {
                console.log('Colleges already loaded from server, preserving existing selection');
                // التأكد من تحديد الكلية الصحيحة
                if (currentCollegeId) {
                    collegeSelect.val(currentCollegeId);
                    console.log('Set college value to:', currentCollegeId);

                    // التحقق من أن القيمة تم تحديدها بنجاح
                    const selectedValue = collegeSelect.val();
                    console.log('College value after setting:', selectedValue);
                    if (selectedValue == currentCollegeId) {
                        console.log('College selection preserved successfully');
                        return;
                    }
                }
            }

            // مسح الكليات الحالية فقط إذا لم نتمكن من الحفاظ على التحديد
            collegeSelect.html('<option value="">اختر الكلية</option>');

            if (universityId) {
                // إظهار مؤشر التحميل
                collegeSelect.prop('disabled', true);
                collegeSelect.html('<option value="">جاري تحميل الكليات...</option>');

                $.get('/Employee/GetCollegesByUniversity', { universityId: universityId })
                    .done(function (data) {
                        console.log('Colleges loaded from AJAX:', data.length, 'colleges');

                        // مسح مؤشر التحميل
                        collegeSelect.html('<option value="">اختر الكلية</option>');

                        if (data && data.length > 0) {
                            $.each(data, function (index, college) {
                                const option = $('<option></option>').val(college.id).text(college.name);

                                // تحديد الكلية المحددة مسبقاً
                                if (college.id == currentCollegeId) {
                                    option.prop('selected', true);
                                    console.log('Selected college from AJAX:', college.name);
                                }

                                collegeSelect.append(option);
                            });

                            // تحميل الأقسام الأكاديمية للكلية المحددة
                            setTimeout(function() {
                                const selectedCollegeId = collegeSelect.val();
                                if (selectedCollegeId) {
                                    console.log('Loading academic departments for selected college after AJAX');
                                    loadAcademicDepartments(preserveSelection);
                                } else {
                                    console.log('No college selected, clearing academic departments');
                                    $('#AcademicDepartmentId').html('<option value="">اختر القسم الأكاديمي</option>');
                                }
                            }, 100);
                        } else {
                            collegeSelect.html('<option value="">لا توجد كليات متاحة</option>');
                            // مسح الأقسام الأكاديمية أيضاً
                            $('#AcademicDepartmentId').html('<option value="">اختر القسم الأكاديمي</option>');
                        }
                    })
                    .fail(function (xhr, status, error) {
                        console.error('فشل في تحميل الكليات:', error);
                        collegeSelect.html('<option value="">خطأ في تحميل الكليات</option>');
                        // مسح الأقسام الأكاديمية في حالة الفشل
                        $('#AcademicDepartmentId').html('<option value="">خطأ في تحميل الأقسام الأكاديمية</option>');
                    })
                    .always(function() {
                        // إعادة تفعيل الحقل
                        collegeSelect.prop('disabled', false);
                    });
            } else {
                console.log('No university selected, clearing colleges');
            }
        }

        // تحميل الأقسام
        function loadDepartments(preserveSelection = false) {
            const instituteId = $('#InstituteId').val();
            const departmentSelect = $('#DepartmentId');

            // الحصول على القسم المحدد حالياً
            const currentDepartmentId = preserveSelection ?
                (departmentSelect.data('current-value') || '@Model.DepartmentId') :
                departmentSelect.val();

            console.log('Loading departments for institute:', instituteId, 'Current department:', currentDepartmentId, 'Preserve selection:', preserveSelection);

            // التحقق من وجود أقسام محملة مسبقاً من الخادم (من Controller)
            const existingOptions = departmentSelect.find('option[value!=""]');
            console.log('Existing department options:', existingOptions.length);

            // إذا كانت الأقسام محملة مسبقاً ونريد الحفاظ على التحديد، لا نحمل من جديد
            if (preserveSelection && existingOptions.length > 0) {
                console.log('Departments already loaded from server, preserving existing selection');
                // التأكد من تحديد القسم الصحيح
                if (currentDepartmentId) {
                    departmentSelect.val(currentDepartmentId);
                    console.log('Set department value to:', currentDepartmentId);

                    // التحقق من أن القيمة تم تحديدها بنجاح
                    const selectedValue = departmentSelect.val();
                    console.log('Department value after setting:', selectedValue);
                    if (selectedValue == currentDepartmentId) {
                        console.log('Department selection preserved successfully');
                        return;
                    }
                }
            }

            // مسح الأقسام الحالية فقط إذا لم نتمكن من الحفاظ على التحديد
            departmentSelect.html('<option value="">اختر القسم</option>');

            if (instituteId) {
                // إظهار مؤشر التحميل
                departmentSelect.prop('disabled', true);
                departmentSelect.html('<option value="">جاري تحميل الأقسام...</option>');

                $.get('/Employee/GetDepartmentsByInstitute', { instituteId: instituteId })
                    .done(function (data) {
                        console.log('Departments loaded from AJAX:', data.length, 'departments');

                        // مسح مؤشر التحميل
                        departmentSelect.html('<option value="">اختر القسم</option>');

                        if (data && data.length > 0) {
                            $.each(data, function (index, department) {
                                const option = $('<option></option>').val(department.id).text(department.name);

                                // تحديد القسم المحدد مسبقاً
                                if (department.id == currentDepartmentId) {
                                    option.prop('selected', true);
                                    console.log('Selected department from AJAX:', department.name);
                                }

                                departmentSelect.append(option);
                            });
                        } else {
                            departmentSelect.html('<option value="">لا توجد أقسام متاحة</option>');
                        }
                    })
                    .fail(function (xhr, status, error) {
                        console.error('فشل في تحميل الأقسام:', error);
                        departmentSelect.html('<option value="">خطأ في تحميل الأقسام</option>');
                    })
                    .always(function() {
                        // إعادة تفعيل الحقل
                        departmentSelect.prop('disabled', false);
                    });
            } else {
                console.log('No institute selected, clearing departments');
            }
        }

        // تحميل الأقسام الأكاديمية
        function loadAcademicDepartments(preserveSelection = false) {
            const collegeId = $('#CollegeId').val();
            const academicDepartmentSelect = $('#AcademicDepartmentId');

            // الحصول على القسم الأكاديمي المحدد حالياً
            const currentAcademicDepartmentId = preserveSelection ?
                (academicDepartmentSelect.data('current-value') || '@Model.AcademicDepartmentId') :
                academicDepartmentSelect.val();

            console.log('Loading academic departments for college:', collegeId);
            console.log('Current academic department ID:', currentAcademicDepartmentId);
            console.log('Preserve selection:', preserveSelection);

            // تعطيل الحقل أثناء التحميل
            academicDepartmentSelect.prop('disabled', true);

            // التحقق من وجود أقسام أكاديمية محملة مسبقاً من الخادم (من Controller)
            const existingOptions = academicDepartmentSelect.find('option[value!=""]');
            console.log('Existing academic department options:', existingOptions.length);

            // إذا كانت الأقسام الأكاديمية محملة مسبقاً ونريد الحفاظ على التحديد، لا نحمل من جديد
            if (preserveSelection && existingOptions.length > 0) {
                console.log('Academic departments already loaded from server, preserving existing selection');
                // التأكد من تحديد القسم الأكاديمي الصحيح
                if (currentAcademicDepartmentId) {
                    academicDepartmentSelect.val(currentAcademicDepartmentId);
                    console.log('Set academic department value to:', currentAcademicDepartmentId);

                    // التحقق من أن القيمة تم تحديدها بنجاح
                    const selectedValue = academicDepartmentSelect.val();
                    console.log('Academic department value after setting:', selectedValue);
                    if (selectedValue == currentAcademicDepartmentId) {
                        console.log('Academic department selection preserved successfully');
                        return;
                    }
                }
            }

            // مسح الأقسام الأكاديمية الحالية فقط إذا لم نتمكن من الحفاظ على التحديد
            academicDepartmentSelect.html('<option value="">اختر القسم الأكاديمي</option>');

            if (collegeId) {
                $.get('/Employee/GetAcademicDepartmentsByCollege', { collegeId: collegeId })
                    .done(function (data) {
                        console.log('Academic departments loaded:', data);
                        if (data && data.length > 0) {
                            $.each(data, function (index, academicDepartment) {
                                const option = $('<option></option>').val(academicDepartment.id).text(academicDepartment.name);

                                // تحديد القسم الأكاديمي المحفوظ إذا كان موجوداً
                                if (currentAcademicDepartmentId && academicDepartment.id == currentAcademicDepartmentId) {
                                    option.prop('selected', true);
                                    console.log('Selected academic department from AJAX:', academicDepartment.name);
                                }

                                academicDepartmentSelect.append(option);
                            });
                        } else {
                            academicDepartmentSelect.html('<option value="">لا توجد أقسام أكاديمية متاحة</option>');
                        }
                    })
                    .fail(function (xhr, status, error) {
                        console.error('فشل في تحميل الأقسام الأكاديمية:', error);
                        academicDepartmentSelect.html('<option value="">خطأ في تحميل الأقسام الأكاديمية</option>');
                    })
                    .always(function() {
                        // إعادة تفعيل الحقل
                        academicDepartmentSelect.prop('disabled', false);
                    });
            } else {
                console.log('No college selected, clearing academic departments');
            }
        }

        // تحميل الوكالات
        function loadAgencies() {
            const ministryId = $('select[name="MinistryId"]').val();
            const agencySelect = $('select[name="AgencyId"]');

            agencySelect.html('<option value="">اختر الوكالة</option>');
            agencySelect.prop('disabled', !ministryId);

            if (ministryId) {
                $.get('/Employee/GetAgenciesByMinistry', { ministryId: ministryId })
                    .done(function (data) {
                        $.each(data, function (index, agency) {
                            agencySelect.append($('<option></option>').val(agency.id).text(agency.name));
                        });
                        enableGovernmentHierarchyFields();
                    })
                    .fail(function () {
                        console.error('فشل في تحميل الوكالات');
                    });
            }
            enableGovernmentHierarchyFields();
        }

        // تحميل المديريات
        function loadDirectorates() {
            const agencyId = $('select[name="AgencyId"]').val();
            const directorateSelect = $('select[name="DirectorateId"]');

            directorateSelect.html('<option value="">اختر المديرية</option>');
            directorateSelect.prop('disabled', !agencyId);

            if (agencyId) {
                $.get('/Employee/GetDirectoratesByAgency', { agencyId: agencyId })
                    .done(function (data) {
                        $.each(data, function (index, directorate) {
                            directorateSelect.append($('<option></option>').val(directorate.id).text(directorate.name));
                        });
                        enableGovernmentHierarchyFields();
                    })
                    .fail(function () {
                        console.error('فشل في تحميل المديريات');
                    });
            }
            enableGovernmentHierarchyFields();
        }

        // تحميل الأقسام الحكومية
        function loadGovernmentDepartments() {
            const directorateId = $('select[name="DirectorateId"]').val();
            const govDeptSelect = $('select[name="GovernmentDepartmentId"]');

            govDeptSelect.html('<option value="">اختر القسم الحكومي</option>');
            govDeptSelect.prop('disabled', !directorateId);

            if (directorateId) {
                $.get('/Employee/GetGovernmentDepartmentsByDirectorate', { directorateId: directorateId })
                    .done(function (data) {
                        $.each(data, function (index, govDept) {
                            govDeptSelect.append($('<option></option>').val(govDept.id).text(govDept.name));
                        });
                        enableGovernmentHierarchyFields();
                    })
                    .fail(function () {
                        console.error('فشل في تحميل الأقسام الحكومية');
                    });
            }
            enableGovernmentHierarchyFields();
        }

        // تحميل الشعب
        function loadDivisions() {
            const govDeptId = $('select[name="GovernmentDepartmentId"]').val();
            const divisionSelect = $('select[name="DivisionId"]');

            divisionSelect.html('<option value="">اختر الشعبة</option>');
            divisionSelect.prop('disabled', !govDeptId);

            if (govDeptId) {
                $.get('/Employee/GetDivisionsByGovernmentDepartment', { governmentDepartmentId: govDeptId })
                    .done(function (data) {
                        $.each(data, function (index, division) {
                            divisionSelect.append($('<option></option>').val(division.id).text(division.name));
                        });
                        enableGovernmentHierarchyFields();
                    })
                    .fail(function () {
                        console.error('فشل في تحميل الشعب');
                    });
            }
            enableGovernmentHierarchyFields();
        }

        // وظيفة حساب العمر بالتفصيل (سنة وشهر ويوم)
        function calculateAge() {
            var birthDate = $('#dateOfBirth').val();
            if (birthDate) {
                var today = new Date();
                var birth = new Date(birthDate);

                // حساب الفرق بالتفصيل
                var years = today.getFullYear() - birth.getFullYear();
                var months = today.getMonth() - birth.getMonth();
                var days = today.getDate() - birth.getDate();

                // تعديل الحسابات إذا كانت الأيام سالبة
                if (days < 0) {
                    months--;
                    // الحصول على عدد أيام الشهر السابق
                    var lastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
                    days += lastMonth.getDate();
                }

                // تعديل الحسابات إذا كانت الشهور سالبة
                if (months < 0) {
                    years--;
                    months += 12;
                }

                // بناء النص بالعربية
                var ageText = '';
                var parts = [];

                if (years > 0) {
                    if (years === 1) {
                        parts.push('سنة واحدة');
                    } else if (years === 2) {
                        parts.push('سنتان');
                    } else if (years >= 3 && years <= 10) {
                        parts.push(years + ' سنوات');
                    } else {
                        parts.push(years + ' سنة');
                    }
                }

                if (months > 0) {
                    if (months === 1) {
                        parts.push('شهر واحد');
                    } else if (months === 2) {
                        parts.push('شهران');
                    } else if (months >= 3 && months <= 10) {
                        parts.push(months + ' أشهر');
                    } else {
                        parts.push(months + ' شهر');
                    }
                }

                if (days > 0) {
                    if (days === 1) {
                        parts.push('يوم واحد');
                    } else if (days === 2) {
                        parts.push('يومان');
                    } else if (days >= 3 && days <= 10) {
                        parts.push(days + ' أيام');
                    } else {
                        parts.push(days + ' يوم');
                    }
                }

                // إذا لم يكن هناك أي عمر (مولود اليوم)
                if (parts.length === 0) {
                    ageText = 'مولود اليوم';
                } else {
                    ageText = parts.join(' و ');
                }

                $('#calculatedAge').val(ageText);
            } else {
                $('#calculatedAge').val('');
            }
        }

        // وظيفة تكوين العنوان الكامل
        function updateFullAddress() {
            var addressParts = [];

            // الحصول على اسم المحافظة
            var provinceText = $('select[name="ProvinceId"] option:selected').text();
            if (provinceText && provinceText !== 'اختر المحافظة') {
                addressParts.push('محافظة ' + provinceText);
            }

            // باقي حقول العنوان
            var district = $('input[name="District"]').val();
            if (district) addressParts.push('قضاء ' + district);

            var subdistrict = $('input[name="Subdistrict"]').val();
            if (subdistrict) addressParts.push('ناحية ' + subdistrict);

            var village = $('input[name="Village"]').val();
            if (village) addressParts.push('قرية ' + village);

            var neighborhood = $('input[name="Neighborhood"]').val();
            if (neighborhood) addressParts.push('حي ' + neighborhood);

            var quarter = $('input[name="Quarter"]').val();
            if (quarter) addressParts.push('محلة ' + quarter);

            var alley = $('input[name="Alley"]').val();
            if (alley) addressParts.push('زقاق ' + alley);

            var house = $('input[name="House"]').val();
            if (house) addressParts.push('دار ' + house);

            var landmark = $('input[name="NearestLandmark"]').val();
            if (landmark) addressParts.push('قرب ' + landmark);

            // تكوين العنوان الكامل
            var fullAddress = addressParts.join(' - ');
            $('#fullAddress').val(fullAddress);
        }

        // حساب تاريخ الترقية
        function calculatePromotionDate() {
            const appointmentDate = document.getElementById('AppointmentDate');
            const lastPromotionDate = document.getElementById('lastPromotionDate');
            const calculatedPromotionDate = document.getElementById('calculatedPromotionDate');
            const rankSelect = document.getElementById('RankId');

            if (appointmentDate && calculatedPromotionDate && rankSelect) {
                const appointment = new Date(appointmentDate.value);
                const lastPromotion = lastPromotionDate && lastPromotionDate.value ? new Date(lastPromotionDate.value) : null;
                const selectedOption = rankSelect.options[rankSelect.selectedIndex];
                const rankDuration = selectedOption ? parseInt(selectedOption.getAttribute('data-rank-duration')) : 0;

                if (appointment && rankDuration > 0) {
                    const baseDate = lastPromotion || appointment;
                    const nextPromotionDate = new Date(baseDate);
                    nextPromotionDate.setFullYear(nextPromotionDate.getFullYear() + rankDuration);

                    calculatedPromotionDate.value = nextPromotionDate.toISOString().split('T')[0];
                }
            }
        }

        // تفعيل/تعطيل حقول البنية الهرمية الحكومية
        function enableGovernmentHierarchyFields() {
            // تفعيل الوكالة إذا كانت الوزارة محددة
            var ministryId = $('select[name="MinistryId"]').val();
            $('select[name="AgencyId"]').prop('disabled', !ministryId);

            // تفعيل المديرية إذا كانت الوكالة محددة
            var agencyId = $('select[name="AgencyId"]').val();
            $('select[name="DirectorateId"]').prop('disabled', !agencyId);

            // تفعيل القسم الحكومي إذا كانت المديرية محددة
            var directorateId = $('select[name="DirectorateId"]').val();
            $('select[name="GovernmentDepartmentId"]').prop('disabled', !directorateId);

            // تفعيل الشعبة إذا كان القسم الحكومي محدد
            var govDeptId = $('select[name="GovernmentDepartmentId"]').val();
            $('select[name="DivisionId"]').prop('disabled', !govDeptId);
        }

        // تحميل البيانات الأولية
        function loadInitialData() {
            console.log('loadInitialData called');

            // التحقق من وجود كليات محملة مسبقاً من الخادم
            var collegeSelect = $('#CollegeId');
            var existingCollegeOptions = collegeSelect.find('option[value!=""]');
            console.log('Existing college options from server:', existingCollegeOptions.length);

            // إذا كانت هناك كليات محملة من الخادم، تأكد من تحديد الكلية الصحيحة
            if (existingCollegeOptions.length > 0) {
                var currentCollegeId = '@Model.CollegeId';
                console.log('Setting college value from model:', currentCollegeId);
                if (currentCollegeId) {
                    collegeSelect.val(currentCollegeId);
                    console.log('College value set to:', collegeSelect.val());
                }
            } else {
                // تحميل الكليات إذا كانت الجامعة محددة (مع الحفاظ على التحديد)
                var universityId = $('#UniversityId').val();
                console.log('Initial university ID:', universityId);
                if (universityId) {
                    console.log('Loading colleges for initial university');
                    loadColleges(true); // true = الحفاظ على التحديد المسبق
                }
            }

            // التحقق من وجود أقسام أكاديمية محملة مسبقاً من الخادم
            var academicDepartmentSelect = $('#AcademicDepartmentId');
            var existingAcademicDepartmentOptions = academicDepartmentSelect.find('option[value!=""]');
            console.log('Existing academic department options from server:', existingAcademicDepartmentOptions.length);

            // إذا كانت هناك أقسام أكاديمية محملة من الخادم، تأكد من تحديد القسم الأكاديمي الصحيح
            if (existingAcademicDepartmentOptions.length > 0) {
                var currentAcademicDepartmentId = '@Model.AcademicDepartmentId';
                console.log('Setting academic department value from model:', currentAcademicDepartmentId);
                if (currentAcademicDepartmentId) {
                    academicDepartmentSelect.val(currentAcademicDepartmentId);
                    console.log('Academic department value set to:', academicDepartmentSelect.val());
                }
            } else {
                // تحميل الأقسام الأكاديمية إذا كانت الكلية محددة (مع الحفاظ على التحديد)
                var collegeId = $('#CollegeId').val();
                console.log('Initial college ID:', collegeId);
                if (collegeId) {
                    console.log('Loading academic departments for initial college');
                    loadAcademicDepartments(true); // true = الحفاظ على التحديد المسبق
                }
            }

            // التحقق من وجود أقسام محملة مسبقاً من الخادم
            var departmentSelect = $('#DepartmentId');
            var existingDepartmentOptions = departmentSelect.find('option[value!=""]');
            console.log('Existing department options from server:', existingDepartmentOptions.length);

            // إذا كانت هناك أقسام محملة من الخادم، تأكد من تحديد القسم الصحيح
            if (existingDepartmentOptions.length > 0) {
                var currentDepartmentId = '@Model.DepartmentId';
                console.log('Setting department value from model:', currentDepartmentId);
                if (currentDepartmentId) {
                    departmentSelect.val(currentDepartmentId);
                    console.log('Department value set to:', departmentSelect.val());
                }
            } else {
                // تحميل الأقسام إذا كان المعهد محدد (مع الحفاظ على التحديد)
                var instituteId = $('#InstituteId').val();
                console.log('Initial institute ID:', instituteId);
                if (instituteId) {
                    console.log('Loading departments for initial institute');
                    loadDepartments(true); // true = الحفاظ على التحديد المسبق
                }
            }

            // تفعيل حقول البنية الهرمية الحكومية
            console.log('Enabling government hierarchy fields');
            enableGovernmentHierarchyFields();

            console.log('loadInitialData completed');
        }

        $(document).ready(function() {
            console.log('Document ready - starting initialization');
            console.log('Model data:', {
                EducationLevel: '@Model.EducationLevel',
                UniversityId: '@Model.UniversityId',
                CollegeId: '@Model.CollegeId',
                InstituteId: '@Model.InstituteId',
                DepartmentId: '@Model.DepartmentId',
                Specialization: '@Model.Specialization'
            });

            // حساب تاريخ الترقية عند تحميل الصفحة
            calculatePromotionDate();

            // حساب العمر عند تحميل الصفحة
            calculateAge();

            // تكوين العنوان الكامل عند تحميل الصفحة
            updateFullAddress();

            // إظهار/إخفاء حقول التعليم بناءً على القيمة الحالية - يجب أن يكون قبل تحميل البيانات
            console.log('Calling toggleEducationFields first');
            console.log('Current education level value:', $('#EducationLevel').val());
            toggleEducationFields();

            // تحميل البيانات المرتبطة بناءً على القيم الحالية - بعد إظهار الحقول
            setTimeout(function() {
                console.log('Loading initial data after fields are shown');
                loadInitialData();
            }, 500); // تأخير قصير للتأكد من ظهور الحقول

            // ربط الأحداث
            $('#dateOfBirth').on('change', calculateAge);

            // ربط أحداث تغيير حقول العنوان
            $('select[name="ProvinceId"], input[name="District"], input[name="Subdistrict"], input[name="Village"], input[name="Neighborhood"], input[name="Quarter"], input[name="Alley"], input[name="House"], input[name="NearestLandmark"]')
                .on('change keyup', updateFullAddress);

            // ربط أحداث الرتبة والترقية
            $('#RankId').on('change', function() {
                calculatePromotionDate();
            });

            $('#AppointmentDate, #lastPromotionDate').on('change', calculatePromotionDate);

            // ربط حدث تغيير مستوى التعليم
            $('#EducationLevel').on('change', function() {
                console.log('Education level changed to:', $(this).val());
                toggleEducationFields();
            });
        });
    </script>
}
