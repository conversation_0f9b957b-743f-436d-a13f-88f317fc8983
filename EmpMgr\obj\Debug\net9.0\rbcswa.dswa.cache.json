{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["/mrpeuoAbEXF7E1S7QNWycTSkShLtRTPL+bSHKnyu7g=", "GkuW/+6AaR7APqTtC2cTQMol91hQ8ql5fTqpZcx4QbE=", "sFDS3HCkfUoITSRopQAXrMwFqTeY5JY/Bk6TS7vz2bM=", "a5a64XX7Q7ohLZCtC0Y4YauSKn11F9UrnMDqiEcQiT0=", "pjGVnHLniPMhJweUYohJ9R+nSBZBTJ49Ty02oJA5C1o=", "MMP2P+z+R8qFuJLi584LtDRrPrrv8uq6vqzjorNyxkE=", "UYaxWVT59KZMqhGWdgh3p8nnSZsWCQ0a+gfMZoxvb2g=", "xnwOWSYHbpglsffOfKkwMq79YY6/6aENbeBqIGesIic=", "nLcfO07n4i9HetJFYCEsg3upevc0swM9P/1t2W+5TzY=", "vC0oLxerwbmrLmBaiIMVNJfOj1zhvRmnROS5jP/DgzY=", "bBF6guEFebliJBOJY1TouxC4TG/Iiio5rdvYmx1cX44=", "dWVln/XkQcOpgWoufsiIwc0d877Y9ebE7w+2HgdxKag=", "8Y4JGeIdhPeE2UbsND3TGvoQT3EvyQJlTOygf8zNPUk=", "8AoPiulSi1EHtODNDx4dIWJMtSdBKw2hF/3SGTcTKyo=", "p+6+OOEJ7nJEj7Ff4Ol4P8DwOlOCRX0jEIk+KuI0/8o=", "HChBN6FGITJenZyIBZMfN7gC7hF40CfuYbVqQsVNf/Y=", "9L8IcP5H1OemHw6eNnwJ2FfJLNWOvBVqCDRXg0OZ/zk=", "4kE6yu2V5WJBlgQwHCksiqBQY12aCfiSVdgSpW79BW0=", "pskFul94x+fmR/OGqlj1hmshr12JnytrOBttN++N94I=", "2BAjuLoOMorVUQ3b+hhbCShedr62yy5l6+hcYYH38bM=", "xs9n8LFMGnlz8VRVjIWnZwWcS/FWQDSQUyGa+8vrpjU=", "b+H9s/XsruGG9Lk/s55Ds93WzDcb+qYHcPJcT8TyoIQ=", "60/lHqcpOvm0t0l/UZRwRagnXDJe8lmmayDnLdXh4cQ=", "kV44JWNy1ypL7mbFXtqSIBJhWdkVl/E+QHJykA8MMpA=", "9by5q95Dyx4jLYS4/VOM6IRXaHef3Cs8syc/wRd3w/8=", "ejjw0fPncdmBMOM4GtpEaeidnAylkOeTgjksAN/ZOPY=", "eVlxyXmahfGaP5duBRKRbMPXzJrXqA/hiOj/EJzZvCk=", "iiNux7oNZaK/IRG+oFx9HAacpryWWSGfzHyK6fSZcrI=", "6gFEGfuWxFN7+Jvkf/lzMd9rR8QJoMz3GeOt2xg7dr8=", "QMxtEOaqpJPedzAPF8koJl3zFnyUs8QWnPJYTlZ/u/U=", "yFXSMr+f+f10Cd+n1p+uLtfO43tlaiG3qqygz6SOnEw=", "l5iFd93sxAWbeUMgwjSb8zziJwFE/gHQmKIQGH0IICw=", "9iJ8jk85SHwfp7AuPzhrCunkwtcmIi2iYNVd4Hn5+2A=", "DmegZx22a2uoFviWojsm9VWJqST6Jp6bsWbcUHAQTtQ=", "hqR9cGa9xbM5I17sSHvC6KQ/qjK8TMNh1WX+QyPlp5I=", "3HXhjsCvZInU2qFWB0YEVdIWAUrbv5NyPJojEtBTn3E=", "EOV+lhQWfQ9tS4uNGMBkXA3emS9q/kS7OelHB8Z4Zq4=", "qEVoZsmCgnLDS9aeUxYrYdD8y8x9dTuZfK1aynBm2Xg=", "BJQqYGw1qyWDabIZ2EaW6jO5x/rxaQ1nfF8ULJtqBFo=", "v/Wvyrz5U93JvPrk7XPQGgTPZK976IbBbzRM5OnaADA=", "EwCvw26mwz0yTifkEb9FzaWXLW15Kz2x97HcbvY1/nA=", "DOoT+mCWmGSIXEJaiatW8/YnoU160yQmwV2qDPU+YZw=", "7K6ioGi6TmTHlKCUG0/eO9jcWGXx86CGppzyMJGOXN0=", "i/RRLmgCOPKUmPKqaRncUqvAJh/PVjuHgbI5AFcefBc=", "+3EKCu3zn0qwpUOTTlIEOLkjMoYXrrTAcY78cOaXto4=", "snD110V14TIPr+9CR8CxCLrvoEyaCATSI1jmrBtg3IA=", "eVIrUAtX6vJ+dO/Ln2rcDvuh+U6UHjAqEcF1mnn7F3o=", "7UEvLTppz0n+flncPPOiA5OhIiH5Z8ueD9JwlLyrM9I=", "QccBXPN0N12lXWn/WiG2eTGuWQBSj0NcgOMAkLtvXes=", "rw8qcR8S7GrTOwS03ykKzQrLAtJ2WXgiqiZH2RxdbHk=", "mrJuEOYMK2YUBshg2BEiO8rFwW14tx5EAn8xCNyC/W4=", "uCDOGkB8JDw3UtI8UNLKAbI0E4Q+tGQexOqjLIuli7I=", "sJpGmb8X3rN90gSPFqPkR4fDv8gKgJJNkfuC4MIaICI=", "SHuMFn9qtFsDl6s9+uFt0nnSEppOor8jzrFG+uYuwIE=", "1/TFqMKPMwc704iIk0HF8h03lY8xGJp/XVv46Fx4T24=", "GLR7IMUjTmoEnHzyXEAxFtDgiQa1TnxwrngDfJ8wQG8=", "trRkFp7uKDYwi1EfSlzXdclAB4lrc+NX5VoBYmcy1Ts=", "R3NbtYhT2p5vDML0AHk/JXWXT8JvM/oae892NM8zyOU=", "PUuo2yCpWn0xcnJJBaZ0XLDdA1bd4I5/Lh8BYEJ8BTE=", "W8dD51e/s2kC2H+5d2WG3tb0E1M5kx/l7j9QGJOGBO0=", "I5LVCY7C/YHBjJxEQPfk+3P3s11PE2gAzH7WNH9mD7c=", "XNy6eTa2CgUho6Iw1ztYCmRKR83+XEDMNiUs/fd9XnQ=", "7dQTdsEAbuE5EYlTT/R4ZY5kWTE5w58avFTCuZQV4Tw=", "RDJai/+upHWU7G67OyHeCCgMNVgqHlSMH4eO5FgXY24=", "yjnShK4oCqkdSkRGe7+1raA/UJSOFWbIngnOVvhb4LA=", "LgY+Bo69V8eoJDbZ8Iei2r/BFuNH2tBIV4mU5Cy5U8I=", "GTFVJvoKRGUYQPMx7lgJpQkxboCwiKLLbFIqPNsMKWs=", "9tV89fou9zEC7fWbdKkhnOL+AkaLchfSSG+E6gxc2IE=", "gfwWT0JAndnuvmcaSkapXr3yNPSynmP344qxVg48zHU=", "PviEmXfGSIW+mEz+7FNj2pboHethqqlW5xdK5hRL6Gc=", "dNKtHS1AOfdAdePuXIyJUdk/o5Mr2ahNjZrljdG71zs=", "ahaO79foKqi63zBQvnsxzJeRYhUc4iFHstbBMPl9ySo=", "Qv0gcaqJXjWd9nHoYkl7e2NXn02dFyCnEISEtdZohmo=", "veThv3XbQ1PbHJS0jDKDmk7XTLLDmsqSOpdHI8DneNY=", "zkCdSzbqpmj/ExeH6Kx9Yk6OcsZO0slsMupzbd3wNX4=", "kLkhpXwqjsMyu3YFhO8rooZiV/fnJfeLLnVkhl/jDXk=", "LUeJ93hcLLtHK+9kQVMAujwJuNlwB+9K8xQjrfBseIk=", "Y09LHuC+rdY0xOtoePVTxIqjHJ/iRvZ3Hp7VmFBafNg=", "IIIY1MbOYklWn6Sib19sxyb9i9rSBU3bB/IZLu1AJ+0=", "jxoy0mTBdaizq9d7+OYxk9nT1u9npI1WgcKgp1PZvv8=", "UYX2QhQ6EhaSnJ9B8dCqVJAgRL4lB0l73LbVHC4FsZU=", "fbJjVo2eAqJ1u8kxI5wqXARY0YrXGKh3RoaJTgj3Z+s=", "Ypr33NItUYgotu6YauMqI1gJ9nU/rRp9q8Ayr99PyPE=", "zHBJjpZ5b5Epm8i5N++gu7NNxPHxkTg1lPGKgO0y+CY=", "nQrQKRgZTlZNZu4Kad/2BPuuwJnjonyNnWZwkYXZTOs=", "vos/mUDXr8V4oWygcNh8gcfjGmDFrGfn6fvpZ4O32hU=", "BTanbfRbax41nDtXXPHNY+Ep1DsutPMLJ6bRKVZXKug=", "Jp5gKLlCjnYDOaOWBH6HyF15Qqo3H8Z6cNE8h3WZeIQ=", "htZQya4JPpUxtDPOuSsyAAQqClUTWpsUljMq1kjqAmc=", "6VKbCYQMiMjKAqgDVCpVXN+bHfeB/G+MKhqYBWDlxco=", "pPH33j5MpTIn9TrB1ZEb/Dw0WUcYa2BMGiJT5GqwbXg=", "u3fEQ+dahEyjLhCZaNMb5VHOLT6aHE17J8RmrxqSnIk=", "v4C/R1usSsXXoAwOPyQ12/MLNwTbQa+Pr75gEcM4BCs=", "7n0w07c+viSoaOjsP2Ohw4Hfwcl5Pe027c3wytAJm00=", "Vww2X5jC3iFOBZdb4Df1LbXLxm0zFsxYHg/BdrTNzL0=", "1f4YlvPNPpYIZOYNfgItv1w9WQRrarT12GcyENt5Es8=", "/u98Ew4RebpcQIJ0p0EQ9FY7W/TZwYbWu7GJiOLvuFk=", "pLDhl0QEeTBfUVhI3EjfitpSXHal0JUQHAUMXVYC3/s=", "pNhctlPooDy1ZxUj/wkp2Qtu3OIcrWlbWu9j+MUizo4=", "ZkHKGIZY6TFsV0YhhpcyT7hNiP53QMbg8JuM5M+Z0PE=", "SBxj0N/iFRpiLpWDDjoRoIKmve1BFeTGwAzLcNfnRQc=", "BtLJnG+Ul3W21sSEuYIkUZso3g9SoBeEo+UJ0gOaVM4=", "18mMTYYZQ6ocXZmYcnhTsM66rkcHM31UHBhUDb1zK1o=", "SuQyl4QGNfKzRcFwkMXSZQt0N98CBEroB6t+7QDi4MA=", "a0d2NRDPmhCGBh5CfURd7aL3PKwExWxweYXPbfphPHQ=", "GXESTt8MFGGxfonh4nHMFXOJx/e9lRTHzgw4vXvzNNY=", "XEUjFIgCDjXNfmDdDCsHSBvT+6pxK6iWKQgseQAQVtc=", "+JYZHBY+w0tTq0EGQ7ltRgtF/QaVqqzBW/ZTivaXT5c=", "AWOf/cCdU/e3E7a8ZVMFKXMxemzU2uB4Y+eDMRFzc50=", "wSBg4Tnb7CrbNDG2pWrAy/RkVy5nQMLcPx9UVe+BUkQ=", "6HG9dy1EGofwT/+hBnmZ+x1GtaxVoTr1g+cVXL227F4=", "dyzxcLWenyVmgsVpn4vOKuKKuWHiIHm7gBjz+4V0MR0=", "ej3Iz0Jlvr62DeOjPK0LVjX+JPewIghxLo6hab9C23Y=", "zxUoRwCeVqs8TXVbRAaPIGBeQ46KsCGzPNoJJWbow2E=", "NQ5b7kHy7Jy+6HlNEo960umI1t95q0BgY0gCDRzZXAc=", "hk+nPUqPwBi6USjrJn8PVc/D0oJDt4Ayz5YaEX/eH4Q=", "RAEVv+WL18rdjXt3LVKbqd8rUuahE5qzDdcX/Ka5k24=", "XysByCWAVEtP6daKqeMEANo8iD8xAby+KVXihPgkW2g=", "ZXySz92CTFHkKtjn24pwcJ1Ect4B8fdSXl8fBusC9to=", "OHnLuWaCPAt0yho7qZWTZOBhOVFOogwShxz2qvJB+R4=", "K6dEcbeAyJQzuMrYspjDeUj31AMA0aiV2KX+6IMrEcI=", "yhiN3naRc6/oPH/Gb+3dC967nDNNqdtfBTblxdIfiFo=", "TWzYxCwXGz+65sUdGfoTHWm3y88fYOonVX26PutniX0=", "UUfWklLsIAzqhpooiPRH8jb1QIzRoBMnb7rmpmZS+5g=", "Vn2P6/q0cUQ+i8c7w6cmYsvqTVWC25zCawcvRB48JOo=", "WJESemjIIaWak8I+/1MSfUiTSGBSvZCtDR+NP4EyklE=", "tuARVwuHx/ymgCjgpFRtcym+WxVdB915Yu9NIIIUMaM=", "c5t0qOSUMCFCSKCLzJnmqeM34K56PIhze4OxItwm4+4=", "VE9dHQbsuqhGlrklCgFQ4OxFmSSSIjhjWsdKfh/AE4o=", "hBqcyCPPhhG1W/Sn+mPcMUufEx2m2x4E9bqs5S0HdCE=", "YFDEro2j5fYqpDddIWAPlr6chxCuyWJRYj0sFcJqY9M=", "qzJqTst4s23Bt+mIej6itwlneCtf2Pg8VgsdEhL8Sno=", "7z0kfMhwki0DuMHf6uT2zZcK0kbTVTkc9MvqYWfpB28=", "haFY6PMSP0CN/z+LBalv+mI4Qmvywgba61ZNWTCVyzc=", "y2/T6JNVUzm93nkc3zgCVyjxLFCCoVNyBAG2WlSGW3w=", "ZlZBsonqyV8dLhFlkcq2ILRMyeUX1UlLY3WCqS4CtBk=", "B3tX1O0vEnMttlH851SQnKFsguzBZ2jrQv8AZyqxYoE=", "WHycbh5kIycwzUdJ7gaGMM37x6QVnSq0bZW1G3Jl96g="], "CachedAssets": {"/mrpeuoAbEXF7E1S7QNWycTSkShLtRTPL+bSHKnyu7g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-09-03T21:05:35.6484744+00:00"}, "vC0oLxerwbmrLmBaiIMVNJfOj1zhvRmnROS5jP/DgzY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-09-03T21:05:35.7229372+00:00"}, "bBF6guEFebliJBOJY1TouxC4TG/Iiio5rdvYmx1cX44=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-09-03T21:05:35.7454259+00:00"}, "dWVln/XkQcOpgWoufsiIwc0d877Y9ebE7w+2HgdxKag=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-09-03T21:05:35.7658974+00:00"}, "8Y4JGeIdhPeE2UbsND3TGvoQT3EvyQJlTOygf8zNPUk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-09-03T21:05:35.7814093+00:00"}, "8AoPiulSi1EHtODNDx4dIWJMtSdBKw2hF/3SGTcTKyo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-09-03T21:05:35.7839152+00:00"}, "p+6+OOEJ7nJEj7Ff4Ol4P8DwOlOCRX0jEIk+KuI0/8o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-09-03T21:05:35.7947412+00:00"}, "HChBN6FGITJenZyIBZMfN7gC7hF40CfuYbVqQsVNf/Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-09-03T21:05:35.7992453+00:00"}, "9L8IcP5H1OemHw6eNnwJ2FfJLNWOvBVqCDRXg0OZ/zk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-09-03T21:05:35.8213428+00:00"}, "4kE6yu2V5WJBlgQwHCksiqBQY12aCfiSVdgSpW79BW0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-09-03T21:05:35.6494699+00:00"}, "pskFul94x+fmR/OGqlj1hmshr12JnytrOBttN++N94I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-09-03T21:05:35.6586911+00:00"}, "2BAjuLoOMorVUQ3b+hhbCShedr62yy5l6+hcYYH38bM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-09-03T21:05:35.6826968+00:00"}, "xs9n8LFMGnlz8VRVjIWnZwWcS/FWQDSQUyGa+8vrpjU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-09-03T21:05:35.717444+00:00"}, "b+H9s/XsruGG9Lk/s55Ds93WzDcb+qYHcPJcT8TyoIQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-09-03T21:05:35.7494138+00:00"}, "60/lHqcpOvm0t0l/UZRwRagnXDJe8lmmayDnLdXh4cQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-09-03T21:05:35.7658974+00:00"}, "kV44JWNy1ypL7mbFXtqSIBJhWdkVl/E+QHJykA8MMpA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-09-03T21:05:35.7774166+00:00"}, "9by5q95Dyx4jLYS4/VOM6IRXaHef3Cs8syc/wRd3w/8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-09-03T21:05:35.7809103+00:00"}, "ejjw0fPncdmBMOM4GtpEaeidnAylkOeTgjksAN/ZOPY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-09-03T21:05:35.7932377+00:00"}, "eVlxyXmahfGaP5duBRKRbMPXzJrXqA/hiOj/EJzZvCk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-09-03T21:05:35.8017987+00:00"}, "iiNux7oNZaK/IRG+oFx9HAacpryWWSGfzHyK6fSZcrI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-09-03T21:05:35.824337+00:00"}, "6gFEGfuWxFN7+Jvkf/lzMd9rR8QJoMz3GeOt2xg7dr8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-09-03T21:05:35.8533817+00:00"}, "QMxtEOaqpJPedzAPF8koJl3zFnyUs8QWnPJYTlZ/u/U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-09-03T21:05:35.8603992+00:00"}, "yFXSMr+f+f10Cd+n1p+uLtfO43tlaiG3qqygz6SOnEw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-09-03T21:05:35.8777929+00:00"}, "l5iFd93sxAWbeUMgwjSb8zziJwFE/gHQmKIQGH0IICw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-09-03T21:05:35.8863157+00:00"}, "9iJ8jk85SHwfp7AuPzhrCunkwtcmIi2iYNVd4Hn5+2A=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-09-03T21:05:35.8223404+00:00"}, "DmegZx22a2uoFviWojsm9VWJqST6Jp6bsWbcUHAQTtQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-09-03T21:05:35.8338502+00:00"}, "hqR9cGa9xbM5I17sSHvC6KQ/qjK8TMNh1WX+QyPlp5I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-09-03T21:05:35.6837282+00:00"}, "3HXhjsCvZInU2qFWB0YEVdIWAUrbv5NyPJojEtBTn3E=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-09-03T21:05:35.717444+00:00"}, "EOV+lhQWfQ9tS4uNGMBkXA3emS9q/kS7OelHB8Z4Zq4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-09-03T21:05:35.7579135+00:00"}, "qEVoZsmCgnLDS9aeUxYrYdD8y8x9dTuZfK1aynBm2Xg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-09-03T21:05:35.7809103+00:00"}, "BJQqYGw1qyWDabIZ2EaW6jO5x/rxaQ1nfF8ULJtqBFo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-09-03T21:05:35.8087861+00:00"}, "v/Wvyrz5U93JvPrk7XPQGgTPZK976IbBbzRM5OnaADA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-09-03T21:05:35.8328272+00:00"}, "EwCvw26mwz0yTifkEb9FzaWXLW15Kz2x97HcbvY1/nA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-09-03T21:05:35.8493305+00:00"}, "DOoT+mCWmGSIXEJaiatW8/YnoU160yQmwV2qDPU+YZw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-09-03T21:05:35.8328272+00:00"}, "7K6ioGi6TmTHlKCUG0/eO9jcWGXx86CGppzyMJGOXN0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-09-03T21:05:35.8448384+00:00"}, "i/RRLmgCOPKUmPKqaRncUqvAJh/PVjuHgbI5AFcefBc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-09-03T21:05:35.8523833+00:00"}, "+3EKCu3zn0qwpUOTTlIEOLkjMoYXrrTAcY78cOaXto4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-09-03T21:05:35.8655175+00:00"}, "snD110V14TIPr+9CR8CxCLrvoEyaCATSI1jmrBtg3IA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-09-03T21:05:35.8698078+00:00"}, "eVIrUAtX6vJ+dO/Ln2rcDvuh+U6UHjAqEcF1mnn7F3o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-09-03T21:05:35.8787914+00:00"}, "7UEvLTppz0n+flncPPOiA5OhIiH5Z8ueD9JwlLyrM9I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-09-03T21:05:35.8827908+00:00"}, "QccBXPN0N12lXWn/WiG2eTGuWQBSj0NcgOMAkLtvXes=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-09-03T21:05:35.8853165+00:00"}, "rw8qcR8S7GrTOwS03ykKzQrLAtJ2WXgiqiZH2RxdbHk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-09-03T21:05:35.8878114+00:00"}, "mrJuEOYMK2YUBshg2BEiO8rFwW14tx5EAn8xCNyC/W4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-09-03T21:05:35.8909044+00:00"}, "uCDOGkB8JDw3UtI8UNLKAbI0E4Q+tGQexOqjLIuli7I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-09-03T21:05:35.6515319+00:00"}, "sJpGmb8X3rN90gSPFqPkR4fDv8gKgJJNkfuC4MIaICI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-09-03T21:05:35.6671819+00:00"}, "SHuMFn9qtFsDl6s9+uFt0nnSEppOor8jzrFG+uYuwIE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-09-03T21:05:35.6732094+00:00"}, "1/TFqMKPMwc704iIk0HF8h03lY8xGJp/XVv46Fx4T24=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-09-03T21:05:35.690225+00:00"}, "GLR7IMUjTmoEnHzyXEAxFtDgiQa1TnxwrngDfJ8wQG8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-09-03T21:05:35.7279296+00:00"}, "trRkFp7uKDYwi1EfSlzXdclAB4lrc+NX5VoBYmcy1Ts=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-09-03T21:05:35.7474177+00:00"}, "R3NbtYhT2p5vDML0AHk/JXWXT8JvM/oae892NM8zyOU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-09-03T21:05:35.7653989+00:00"}, "PUuo2yCpWn0xcnJJBaZ0XLDdA1bd4I5/Lh8BYEJ8BTE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-09-03T21:05:35.7937367+00:00"}, "W8dD51e/s2kC2H+5d2WG3tb0E1M5kx/l7j9QGJOGBO0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-09-03T21:05:35.8017987+00:00"}, "I5LVCY7C/YHBjJxEQPfk+3P3s11PE2gAzH7WNH9mD7c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-09-03T21:05:35.821842+00:00"}, "XNy6eTa2CgUho6Iw1ztYCmRKR83+XEDMNiUs/fd9XnQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-09-03T21:05:35.8233392+00:00"}, "7dQTdsEAbuE5EYlTT/R4ZY5kWTE5w58avFTCuZQV4Tw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\qsbhzt1df6-tr8hjhsy6q.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "css/employee-management#[.{fingerprint=tr8hjhsy6q}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\employee-management.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "srme5bvwsj", "Integrity": "OHT9eTCvSamcD6PwVropUOHipOb4T9gh+MIoflagXiQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\employee-management.css", "FileLength": 1918, "LastWriteTime": "2025-09-03T21:05:35.824337+00:00"}, "RDJai/+upHWU7G67OyHeCCgMNVgqHlSMH4eO5FgXY24=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\8pz28qrki4-c4kdtxbn41.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "css/form-enhancements#[.{fingerprint=c4kdtxbn41}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\form-enhancements.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x70sscdf2z", "Integrity": "HcxYeSxtgC9P1wlNaLqGXP6QdKGJmTx6GYmaZo6yZo4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\form-enhancements.css", "FileLength": 2033, "LastWriteTime": "2025-09-03T21:05:35.8273312+00:00"}, "ZlZBsonqyV8dLhFlkcq2ILRMyeUX1UlLY3WCqS4CtBk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\uyqeku5coa-mlv21k5csn.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-09-03T21:05:35.8223404+00:00"}, "yjnShK4oCqkdSkRGe7+1raA/UJSOFWbIngnOVvhb4LA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\03hd1whjk0-dis2psivg1.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "css/login#[.{fingerprint=dis2psivg1}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\login.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ea846bh1hq", "Integrity": "9iSpzBBWasM8brhU5LxNWpKhO7LNYEU67AmZUz71D04=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\login.css", "FileLength": 8272, "LastWriteTime": "2025-09-03T21:05:35.8338502+00:00"}, "LgY+Bo69V8eoJDbZ8Iei2r/BFuNH2tBIV4mU5Cy5U8I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\7xzul22b9q-huq8638ofr.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "css/logo-white#[.{fingerprint=huq8638ofr}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\logo-white.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jay2ab5sdg", "Integrity": "5T/xBuD3XT3ki8Gm8pB0NoCE3RpGi3AQ3DURwIZ9Vs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\logo-white.css", "FileLength": 1076, "LastWriteTime": "2025-09-03T21:05:35.8358551+00:00"}, "GTFVJvoKRGUYQPMx7lgJpQkxboCwiKLLbFIqPNsMKWs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\mtcxtrh8nl-bi5zyb4xrg.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "css/site#[.{fingerprint=bi5zyb4xrg}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g9wj74zl21", "Integrity": "7G4jVn1OXuPm9FhzKWlsIOj9Z6WsAA2XAZuA+8WViy0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\site.css", "FileLength": 2555, "LastWriteTime": "2025-09-03T21:05:35.8238379+00:00"}, "kLkhpXwqjsMyu3YFhO8rooZiV/fnJfeLLnVkhl/jDXk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\e2h6q6b0lp-xtxxf3hu2r.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-09-03T21:05:35.6826968+00:00"}, "LUeJ93hcLLtHK+9kQVMAujwJuNlwB+9K8xQjrfBseIk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\5dnelliqu1-9mph4dokiv.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/theme-manager#[.{fingerprint=9mph4dokiv}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\theme-manager.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vpg0pp4vuf", "Integrity": "Xe5k/oLi0fncEfX5iHwIV5CpBGyUTZLbEPk1RrwDVzI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\theme-manager.js", "FileLength": 6904, "LastWriteTime": "2025-09-03T21:05:35.6932188+00:00"}, "Y09LHuC+rdY0xOtoePVTxIqjHJ/iRvZ3Hp7VmFBafNg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ltockhsibq-bqjiyaj88i.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-09-03T21:05:35.7013012+00:00"}, "IIIY1MbOYklWn6Sib19sxyb9i9rSBU3bB/IZLu1AJ+0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\u6m8bioa4e-c2jlpeoesf.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-09-03T21:05:35.7244358+00:00"}, "jxoy0mTBdaizq9d7+OYxk9nT1u9npI1WgcKgp1PZvv8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\1hcsytk711-erw9l3u2r3.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-09-03T21:05:35.7329247+00:00"}, "UYX2QhQ6EhaSnJ9B8dCqVJAgRL4lB0l73LbVHC4FsZU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\icz8vgktcm-aexeepp0ev.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-09-03T21:05:35.747917+00:00"}, "fbJjVo2eAqJ1u8kxI5wqXARY0YrXGKh3RoaJTgj3Z+s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\wq5km40p6y-d7shbmvgxk.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-09-03T21:05:35.7554176+00:00"}, "Ypr33NItUYgotu6YauMqI1gJ9nU/rRp9q8Ayr99PyPE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\73dnc7it4h-ausgxo2sd3.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-09-03T21:05:35.7809103+00:00"}, "zHBJjpZ5b5Epm8i5N++gu7NNxPHxkTg1lPGKgO0y+CY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\be9g0u61us-k8d9w2qqmf.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-09-03T21:05:35.7877471+00:00"}, "nQrQKRgZTlZNZu4Kad/2BPuuwJnjonyNnWZwkYXZTOs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\r3de5q5ra6-cosvhxvwiu.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-09-03T21:05:35.7927386+00:00"}, "vos/mUDXr8V4oWygcNh8gcfjGmDFrGfn6fvpZ4O32hU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\zbvp3xg0de-ub07r2b239.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-09-03T21:05:35.6484744+00:00"}, "BTanbfRbax41nDtXXPHNY+Ep1DsutPMLJ6bRKVZXKug=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\lupukxohww-fvhpjtyr6v.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-09-03T21:05:35.6556957+00:00"}, "Jp5gKLlCjnYDOaOWBH6HyF15Qqo3H8Z6cNE8h3WZeIQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\t3pdg8q3wr-b7pk76d08c.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-09-03T21:05:35.658192+00:00"}, "htZQya4JPpUxtDPOuSsyAAQqClUTWpsUljMq1kjqAmc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ze0rzqqdpe-fsbi9cje9m.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-09-03T21:05:35.6661765+00:00"}, "6VKbCYQMiMjKAqgDVCpVXN+bHfeB/G+MKhqYBWDlxco=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\5bksauz26z-rzd6atqjts.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-09-03T21:05:35.669715+00:00"}, "pPH33j5MpTIn9TrB1ZEb/Dw0WUcYa2BMGiJT5GqwbXg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\xxb0dek9bm-ee0r1s7dh0.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-09-03T21:05:35.6862306+00:00"}, "u3fEQ+dahEyjLhCZaNMb5VHOLT6aHE17J8RmrxqSnIk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\m3qxu7fmoz-dxx9fxp4il.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-09-03T21:05:35.690225+00:00"}, "v4C/R1usSsXXoAwOPyQ12/MLNwTbQa+Pr75gEcM4BCs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\5xncts6395-jd9uben2k1.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-09-03T21:05:35.6992068+00:00"}, "7n0w07c+viSoaOjsP2Ohw4Hfwcl5Pe027c3wytAJm00=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\8fdwrzbsn7-khv3u5hwcm.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-09-03T21:05:35.7033011+00:00"}, "Vww2X5jC3iFOBZdb4Df1LbXLxm0zFsxYHg/BdrTNzL0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\r6knf4se69-r4e9w2rdcm.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-09-03T21:05:35.7304269+00:00"}, "1f4YlvPNPpYIZOYNfgItv1w9WQRrarT12GcyENt5Es8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ovf3g74ouy-lcd1t2u6c8.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-09-03T21:05:35.7354318+00:00"}, "/u98Ew4RebpcQIJ0p0EQ9FY7W/TZwYbWu7GJiOLvuFk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\wwveklz2bz-c2oey78nd0.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-09-03T21:05:35.7584133+00:00"}, "pLDhl0QEeTBfUVhI3EjfitpSXHal0JUQHAUMXVYC3/s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\4uxiymxczk-tdbxkamptv.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-09-03T21:05:35.7658974+00:00"}, "pNhctlPooDy1ZxUj/wkp2Qtu3OIcrWlbWu9j+MUizo4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\llpferqm7o-j5mq2jizvt.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-09-03T21:05:35.7744226+00:00"}, "ZkHKGIZY6TFsV0YhhpcyT7hNiP53QMbg8JuM5M+Z0PE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\hknt7h4ilw-06098lyss8.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-09-03T21:05:35.7774166+00:00"}, "SBxj0N/iFRpiLpWDDjoRoIKmve1BFeTGwAzLcNfnRQc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\q1a7i788qr-nvvlpmu67g.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-09-03T21:05:35.7932377+00:00"}, "BtLJnG+Ul3W21sSEuYIkUZso3g9SoBeEo+UJ0gOaVM4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ylixe7zkmk-s35ty4nyc5.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-09-03T21:05:35.8082863+00:00"}, "18mMTYYZQ6ocXZmYcnhTsM66rkcHM31UHBhUDb1zK1o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\oqhreanw7m-pj5nd1wqec.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-09-03T21:05:35.6826968+00:00"}, "SuQyl4QGNfKzRcFwkMXSZQt0N98CBEroB6t+7QDi4MA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\sapn6tyfxr-46ein0sx1k.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-09-03T21:05:35.7179469+00:00"}, "a0d2NRDPmhCGBh5CfURd7aL3PKwExWxweYXPbfphPHQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\7588g70b0f-v0zj4ognzu.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-09-03T21:05:35.7514211+00:00"}, "GXESTt8MFGGxfonh4nHMFXOJx/e9lRTHzgw4vXvzNNY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ms94dy0zhx-37tfw0ft22.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-09-03T21:05:35.7809103+00:00"}, "XEUjFIgCDjXNfmDdDCsHSBvT+6pxK6iWKQgseQAQVtc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\lclqetud77-hrwsygsryq.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-09-03T21:05:35.8133417+00:00"}, "+JYZHBY+w0tTq0EGQ7ltRgtF/QaVqqzBW/ZTivaXT5c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\95aor8niql-pk9g2wxc8p.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-09-03T21:05:35.8193458+00:00"}, "AWOf/cCdU/e3E7a8ZVMFKXMxemzU2uB4Y+eDMRFzc50=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\wiyafr2usj-ft3s53vfgj.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-09-03T21:05:35.8478327+00:00"}, "wSBg4Tnb7CrbNDG2pWrAy/RkVy5nQMLcPx9UVe+BUkQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\rzhl4ird3i-6cfz1n2cew.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-09-03T21:05:35.806789+00:00"}, "6HG9dy1EGofwT/+hBnmZ+x1GtaxVoTr1g+cVXL227F4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\4hdu0n3z1i-6pdc2jztkx.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-09-03T21:05:35.8363547+00:00"}, "dyzxcLWenyVmgsVpn4vOKuKKuWHiIHm7gBjz+4V0MR0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\z3wr01gbdi-493y06b0oq.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-09-03T21:05:35.8428424+00:00"}, "ej3Iz0Jlvr62DeOjPK0LVjX+JPewIghxLo6hab9C23Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\4pyy1armbb-iovd86k7lj.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-09-03T21:05:35.857916+00:00"}, "zxUoRwCeVqs8TXVbRAaPIGBeQ46KsCGzPNoJJWbow2E=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\et5sxto7xf-vr1egmr9el.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-09-03T21:05:35.863517+00:00"}, "NQ5b7kHy7Jy+6HlNEo960umI1t95q0BgY0gCDRzZXAc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\jmwtazhcf8-kbrnm935zg.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-09-03T21:05:35.8752986+00:00"}, "hk+nPUqPwBi6USjrJn8PVc/D0oJDt4Ayz5YaEX/eH4Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ujn2mfnjhz-jj8uyg4cgr.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-09-03T21:05:35.8792901+00:00"}, "RAEVv+WL18rdjXt3LVKbqd8rUuahE5qzDdcX/Ka5k24=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\vvn6ldwjcr-y7v9cxd14o.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-09-03T21:05:35.8878114+00:00"}, "XysByCWAVEtP6daKqeMEANo8iD8xAby+KVXihPgkW2g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\vvogrlz59y-notf2xhcfb.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-09-03T21:05:35.8930122+00:00"}, "ZXySz92CTFHkKtjn24pwcJ1Ect4B8fdSXl8fBusC9to=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\mu8mel097m-h1s4sie4z3.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-09-03T21:05:35.9033682+00:00"}, "OHnLuWaCPAt0yho7qZWTZOBhOVFOogwShxz2qvJB+R4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\7djawnmx7m-63fj8s7r0e.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-09-03T21:05:35.648972+00:00"}, "K6dEcbeAyJQzuMrYspjDeUj31AMA0aiV2KX+6IMrEcI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\2hvf644ell-0j3bgjxly4.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-09-03T21:05:35.6837282+00:00"}, "yhiN3naRc6/oPH/Gb+3dC967nDNNqdtfBTblxdIfiFo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\p05qf7n4pl-47otxtyo56.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-09-03T21:05:35.6872302+00:00"}, "TWzYxCwXGz+65sUdGfoTHWm3y88fYOonVX26PutniX0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\z6mqu3168w-4v8eqarkd7.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-09-03T21:05:35.6922201+00:00"}, "UUfWklLsIAzqhpooiPRH8jb1QIzRoBMnb7rmpmZS+5g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\iswagyvus1-356vix0kms.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-09-03T21:05:35.6992068+00:00"}, "Vn2P6/q0cUQ+i8c7w6cmYsvqTVWC25zCawcvRB48JOo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\tnnl6kleqi-83jwlth58m.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-09-03T21:05:35.7087911+00:00"}, "WJESemjIIaWak8I+/1MSfUiTSGBSvZCtDR+NP4EyklE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\yhkgu7ckf9-mrlpezrjn3.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-09-03T21:05:35.7149379+00:00"}, "tuARVwuHx/ymgCjgpFRtcym+WxVdB915Yu9NIIIUMaM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\9yzgjok0kn-lzl9nlhx6b.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-09-03T21:05:35.7264319+00:00"}, "c5t0qOSUMCFCSKCLzJnmqeM34K56PIhze4OxItwm4+4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\xabnt4su6a-ag7o75518u.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-09-03T21:05:35.7319202+00:00"}, "VE9dHQbsuqhGlrklCgFQ4OxFmSSSIjhjWsdKfh/AE4o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\29dzasscuj-x0q3zqp4vz.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-09-03T21:05:35.7359307+00:00"}, "hBqcyCPPhhG1W/Sn+mPcMUufEx2m2x4E9bqs5S0HdCE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\71kv86x8x7-0i3buxo5is.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-09-03T21:05:35.7852516+00:00"}, "YFDEro2j5fYqpDddIWAPlr6chxCuyWJRYj0sFcJqY9M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\9oiix6evld-o1o13a6vjx.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-09-03T21:05:35.7917422+00:00"}, "qzJqTst4s23Bt+mIej6itwlneCtf2Pg8VgsdEhL8Sno=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\7xs15mvaqs-ttgo8qnofa.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-09-03T21:05:35.8092856+00:00"}, "7z0kfMhwki0DuMHf6uT2zZcK0kbTVTkc9MvqYWfpB28=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\gnr73nxpcw-2z0ns9nrw6.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-09-03T21:05:35.8283294+00:00"}, "haFY6PMSP0CN/z+LBalv+mI4Qmvywgba61ZNWTCVyzc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\jrwjrbrbrk-muycvpuwrr.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-09-03T21:05:35.8343527+00:00"}, "B3tX1O0vEnMttlH851SQnKFsguzBZ2jrQv8AZyqxYoE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\0896su1vwj-i78plulxfw.gz", "SourceId": "EmpMgr", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "EmpMgr#[.{fingerprint=i78plulxfw}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\scopedcss\\bundle\\EmpMgr.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c53z5wyf9d", "Integrity": "sH9AKm6gnPMa8nGOq2pbikhYPiVHlbwuEOQKo+ZcEgY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\scopedcss\\bundle\\EmpMgr.styles.css", "FileLength": 538, "LastWriteTime": "2025-09-03T21:05:35.648972+00:00"}, "WHycbh5kIycwzUdJ7gaGMM37x6QVnSq0bZW1G3Jl96g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\inx3g92fnj-i78plulxfw.gz", "SourceId": "EmpMgr", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "EmpMgr#[.{fingerprint=i78plulxfw}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\EmpMgr.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c53z5wyf9d", "Integrity": "sH9AKm6gnPMa8nGOq2pbikhYPiVHlbwuEOQKo+ZcEgY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\EmpMgr.bundle.scp.css", "FileLength": 538, "LastWriteTime": "2025-09-03T21:05:35.6515319+00:00"}, "gfwWT0JAndnuvmcaSkapXr3yNPSynmP344qxVg48zHU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\1fcl9j2tzp-ldq9zywrnr.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "images/default-logo#[.{fingerprint=ldq9zywrnr}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\images\\default-logo.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "prbpsofs99", "Integrity": "1fP82FQF/XYDC3Xr8JKa4yC3E070aASfU9GzMY148xM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\images\\default-logo.svg", "FileLength": 649, "LastWriteTime": "2025-09-03T21:05:35.6494699+00:00"}, "9tV89fou9zEC7fWbdKkhnOL+AkaLchfSSG+E6gxc2IE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\42a32m7hmx-61n19gt1b8.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-09-03T21:05:35.8273312+00:00"}, "ahaO79foKqi63zBQvnsxzJeRYhUc4iFHstbBMPl9ySo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\grsbdfe5s3-fm3hgmrtyz.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/form-fixes#[.{fingerprint=fm3hgmrtyz}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\form-fixes.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fktw1irz80", "Integrity": "r/bROCER3/kTCbOWTPej5s7BZqffS6aWMU7TVZS/F98=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\form-fixes.js", "FileLength": 2404, "LastWriteTime": "2025-09-03T21:05:35.6687167+00:00"}, "Qv0gcaqJXjWd9nHoYkl7e2NXn02dFyCnEISEtdZohmo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\tk5dntihez-vdiodh0fwa.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/loading#[.{fingerprint=vdiodh0fwa}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\loading.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wasz7wi5o5", "Integrity": "CMFDe5OW2HYzmKpC978UzA7kPLviVu1PbsyShUdblf4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\loading.js", "FileLength": 1497, "LastWriteTime": "2025-09-03T21:05:35.6727096+00:00"}, "sFDS3HCkfUoITSRopQAXrMwFqTeY5JY/Bk6TS7vz2bM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-09-03T21:05:35.6616859+00:00"}, "a5a64XX7Q7ohLZCtC0Y4YauSKn11F9UrnMDqiEcQiT0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-09-03T21:05:35.6576921+00:00"}, "nLcfO07n4i9HetJFYCEsg3upevc0swM9P/1t2W+5TzY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-09-03T21:05:35.7057958+00:00"}, "zkCdSzbqpmj/ExeH6Kx9Yk6OcsZO0slsMupzbd3wNX4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\qdvgdypax6-8n3ths2svu.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/performance-optimizer#[.{fingerprint=8n3ths2svu}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\performance-optimizer.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "817s5zppsz", "Integrity": "ZIwivQfRS9nYmbxUTqy7XULkWRAZJUuKTa1bhNn1wj0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\performance-optimizer.js", "FileLength": 3696, "LastWriteTime": "2025-09-03T21:05:35.6816925+00:00"}, "y2/T6JNVUzm93nkc3zgCVyjxLFCCoVNyBAG2WlSGW3w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\1gg0zrd2hz-87fc7y1x7t.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-09-03T21:05:35.8213428+00:00"}, "veThv3XbQ1PbHJS0jDKDmk7XTLLDmsqSOpdHI8DneNY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\5ls8oujwu8-hqc3cwchdn.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/notification-system#[.{fingerprint=hqc3cwchdn}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\notification-system.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pzld4twenz", "Integrity": "lUgBl9txpBYZQkqXO6K9CxdZFzEOG0SEDJgUA4AVMcs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\notification-system.js", "FileLength": 3749, "LastWriteTime": "2025-09-03T21:05:35.674208+00:00"}, "xnwOWSYHbpglsffOfKkwMq79YY6/6aENbeBqIGesIic=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-09-03T21:05:35.68473+00:00"}, "UYaxWVT59KZMqhGWdgh3p8nnSZsWCQ0a+gfMZoxvb2g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-09-03T21:05:35.747917+00:00"}, "MMP2P+z+R8qFuJLi584LtDRrPrrv8uq6vqzjorNyxkE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-09-03T21:05:35.7319202+00:00"}, "pjGVnHLniPMhJweUYohJ9R+nSBZBTJ49Ty02oJA5C1o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-09-03T21:05:35.7149379+00:00"}, "dNKtHS1AOfdAdePuXIyJUdk/o5Mr2ahNjZrljdG71zs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\jl25rv1rp1-afw5n2hlcd.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/auto-save#[.{fingerprint=afw5n2hlcd}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\auto-save.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gl8o4p8j2g", "Integrity": "hAlrmF2D/9gHkrXWzHFkgbBl7ASxFeP9wn/2Fkpk1Ek=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\auto-save.js", "FileLength": 2844, "LastWriteTime": "2025-09-03T21:05:35.6651786+00:00"}, "GkuW/+6AaR7APqTtC2cTQMol91hQ8ql5fTqpZcx4QbE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-09-03T21:05:35.6566949+00:00"}, "PviEmXfGSIW+mEz+7FNj2pboHethqqlW5xdK5hRL6Gc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\wzoe3r2s97-kloo121ts9.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/advanced-search#[.{fingerprint=kloo121ts9}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\advanced-search.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "16ssdp2f6a", "Integrity": "mY6tD07yocYbILBZMnlJD6TYhoL2BfIOi7969Oy0KaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\advanced-search.js", "FileLength": 5687, "LastWriteTime": "2025-09-03T21:05:35.6540284+00:00"}}, "CachedCopyCandidates": {}}