{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["8gyV4d10Ft9FD38pzgeDXJ2DJwdiATdccMYkblqFDOI=", "4L8up5X8LuJW6siHvD9w5lfkbc9mLQMPVSkpI6VMo8Y=", "e5BR6zTh9LkCgLPgaqQCMk75C9u1x9L+3YcERjbJc6Q=", "c7N1SeBNju2LwDGlfO7AFFcVc8Yu+1aSCUHkEPqNlsI=", "/G5rZk9jCzMo+o1Ou/Ol1LMVqPe3y6dKjvCIEikzC7U=", "oEF3ZeBHS66psmlLYIWsKfaY8EYVyMarRG/gdFuppFg=", "NVR8HRiwO/BpmHKal1yz76vU+7XESlzqfzrFtTGrJdI=", "QKN4aBptOA4b07kBZFKxq26HEvjaXpa2A0roEujb26Q=", "XjfrF84JhYCVTTqhtgPmjB0kOW1cb+fV8OUiFLqCOC8=", "KEN6SPVCelLJX2s4PtgLs7hLc3xpAjerDi3QlkKjTXc=", "KhAQJvvhALXB2ruCNt8psucDFuzjh8lirzCLmpbwB1o=", "kMds8HPZdGBVA/NFzSq/SxAYPDP+OXYPFjARQzlu5Ng=", "bVKUweVXN5KmGB6oa/q89+nyPHDWUgCZR0CZQEeajmg=", "hhbylOg4CQQEl0tv+ZJtAgK2d+HlLOG0tu1DT1qhp6Q=", "tDIt0qutR+zO3bekb9bqLJKq2DU7jRidKm6JfATtsGI=", "QxAbYZ+6CtPw1EdWQeXU7Z3ipbutrjoGU2SGj1XBles=", "JtZv5T0dyt/XX2u5xFTM6I5dQB/YZyn8b3BYdWx8KgE=", "/bGjzp7199wfpB12bWqEaeMUW5rV8zwaiO5AMA/p8d0=", "EmbX6kYMujdqD2Ez2GMNPBEyKEgknCP3ihE1Y0ib8ic=", "WemYvkbOng4CFuVw12YxKO1VyehXhxRA9U/bgZpcA0k=", "vUP/WJD55lhHxZjleKGgeWUy0FjkFf8SQOKAvFpnNwQ=", "VigPW0QbaD5sgrB/5Uw810zL1dW2Pmf0DRSlvTu4iiw=", "gbIsVSQuvkEWqbIunoUyFP0qgI33L+FfCHSczVKj9Cs=", "KqlBDu3nptZbRXiXx6XQzdKkODDaqpMvZGSZ5eoIVPY=", "AzVPoftDc9ubRfDV6jnZt692Ao/mj4FqxUIlB3Z3rZU=", "/pWaQn7Z93IFH341ZEAsPXMhfY4SNBO4NlPPApVwWWY=", "8HSaa6CCmoZmJjXLml/6x+iAKoMs2jg60aMGv/GDK2A=", "2xjNe3S75g51KMK6cHflL40XYWYo7izhpiWsof9g9OI=", "sHiNGJ/HuqqPw9dmMxFeUKi4/X0RnEDE0HpDqhEUZhU=", "Qj+T2aRmkU9qzbciQPfWY2XvgIUG5jJIFRusCvAiMuc=", "xQ+sLSKA718tGNnuUPlMFIeDQk4ycv1VF7vhNU7MH6U=", "MyACmQbrFABEm7XgNEMIOBPDoRLt/6zG5h2LhTbtpx8=", "sCaKQkaR9/uFtMq81on/YyUkgr3WFYER7i0kQgXkx54=", "z2Q1vRmkdH7+3W4VSwKDOP6SqlA9XKxnllEekpvj/lA=", "dE3yTg6Xrx439P/PAclbsWPBpza6TtdLHM62jGeSX1M=", "8/F0Yk5TlUkyqcijA1GXYVCncP/p4aZe/4olEzcH30U=", "tAIU+iuxU3mAFHYuQcc81yEsFSetXzHqqqQ6SyK59Go=", "3ZpUPf1j0Lynk7umE6s7Lc9hSlquLnSJ9Gwi6sIuiJg=", "lVXWYVGa92PZsHGZTbEHBooaD5XJYMBWSbu2UTekjLs=", "ozK3TBiygLYVdfesq61LP09+jnqtTw1d64tRsX9kqBE=", "TtrWARox2WEhxfjG8yaNCgVC01WbPqH1YSb6jpjDP8w=", "G+bJjoL0uAYhF5oFa7YV727QifDOd1sWLkdIGRhUKgM=", "EoVXo9w/Gik7d4913+ydunwXArbfzNVCIsWB6FBihsg=", "gKZXumqn4X809m3UZUO3oQKskCLVlD0Z0YRUmzDCSVs=", "POl4/m5fatXkPxGULKdDHv23H47W5lfkVcKaPbbJ3q4=", "yL8SVJMCwW89lbO3N5Gut5ANO2ulfjXyBUKV5AdE+PQ=", "ovhW7dM9+Z02Yt7QI/LN+ev8tYJ+vcj5HIGK3/a4Hbk=", "ZPF2R0YXm5ms37jTEnhf7na4hpCS4FTKlW+BA6dsMcg=", "rWb4bfPjjENLolV7xdtkRkK++PfNVfksQDEgWRUvMH8=", "C+n9YhKMgY5uHrEuEq9CDJ+wChvA3b/LaUcmypDZRUk=", "Imv5WJfFCjgXlRlUx/DcnCYs/ZMadtwzods/V0VCSYU=", "vlcPSy2kHTHZ05UnDmmWYST4TuHvVxleAZLTxLbk+JI=", "VyvIUY1PEhhlQxoj5yMPBqzlM1neuB1RQvc3AaWR/Qw=", "HQDhWu0w+7EvqB1obYY+18HmX0r/tyuRdQK/jggEI/s=", "2m5QXFzJl10alQ7YgvVABdzsO68jLgYnGyxitJfA9uA=", "iqLVOTWY6Ghm6cohFUjulbU+u/pxcDPrJLP8kYiAMKw=", "/mVr3gEQ4loAUI8YcIiLqrD0v933Wem8IQ619UQXWWs=", "beFn0jNuqDTdrm8ltqt9LX87fdS1Yj8oMKsnI6gKh7c=", "Pnisinrb2zUiCqo8ZtXwpdyY4QAagIOfFBNAcKN1BJc=", "maaqKe+vYEWAvEfjBy+Ujm9buTzjA0o0r/8pMjpPjyE=", "tHoNvC0f9+Gju3ZOIUK9QlArmhU02Dj5Pj4HhjoH8gg=", "o0bt9ioEXkJ2AVKDC7P41yQcD+BeqIcxPXhPNBheNsk=", "/3vG/S8JNMbIL88NOUy1U39Fcd2WXnotX65MjftH6vc=", "GLM6dKYi6l8keuxCkxBi+f+Yf69noEJ3L4QZqjh2GNU=", "d+OZYCvuo1dgP76+s2KPHReyI2pgAXmmSLX2S844kOQ=", "6PHHO5WUH7wCEOI2MnT8MNrQBNbD1Vx0/2rKGQZXzqw=", "UcWNS+SM7pvf7b58IHw8+C3ufICOQXalipyZuj3+ze0=", "IzVEOmFHWOQp9XFkiAzyMCwTg5bbvYz2dYEMl9M26ss=", "YegSh+oLG3kSfYSOSWHGVvLD3XBmPDloZZ9vlHt0PyI=", "BC9/yB4VNYpHKRLzHz6ZJcg2yNl+8Gpsk2185lc+/Ns=", "EoCb7mDsbu6Y7TkU1wNfGLlB7EeJFsbngd9OjgEKjSU=", "6YVUs5bvlWDBLYLe6soDtIXyUPHXEBwWemG3jet+sdQ=", "TzKdr9175hRz/VtPt+Hj04eFlo1AayWaMA1qvzm3d2w=", "I4cI6T7PSFF+Ko+uhefwnGemdYJCN89CBExY6B+46wE=", "LbEtXPa6wmi+nXwxJ6khqtPq1x2uYXkmA1G2+XV2a5g=", "9/LeCwHrc+c3TWKquxgDbjsFi9Pd5DIXdfRIlBopZXs=", "ReGHhp/tpqJGOTH5ofoHjKjSAo5l8awWa6NHfBeZ2+0=", "JlpmPAX41yLCyEODVRYIaw7RdSuWKuQSAs/dZVNY5KE=", "JVAKo7M1IC1YC62H1w2s/gKznveLHAqO5Oqs4cctF7w=", "VdgnW/fmm7srbFQniNEY2KQhHHYUmv8vQpbHTKUv+vk=", "V/hQ/yVY/cqnkgk52S/wxP4O6s6nCIB6z2uqfEfbtlQ=", "ASgVU3U7w7k60ZIpLCBc+rAUOb4pZcEyORawZvusIvo=", "whAZvY5SXX67Qan3o/aaS31DAl8I5Vu1IOQGd45e/ao=", "Lg8ZJFKTJxyR4KaGiqJb+PWrYxIF3tKLctFH4Xryens=", "C7t0GqK5meB3+hl0GhgsWbCii69qxpd2Ig/QlgzIjoM=", "qMm3JmvrwiI80vnMkTs5sruRKw4+MFZu3lABuLOKCgQ=", "jaCqwf5K7tQW5VlcoSgHqjVdgGCHwxysTYMyACs6hr0=", "Y8Jdk8/psEoD0va5Aw9o/tv0rE1FqWjo0Zb4kOxERHM=", "8pJd7FDDm3fYzlZuCDVpDRZkM5sMZI/Iadw7V6E7T5M=", "OetjkAO9yBS/p8O3ZMmR2v4EVxEkOiP7Xog6RGTCIUs=", "oYK1PxN4ELv7fo7js+JRoF6Bt9dscOQl04Lhwwxy0dQ=", "BcP3OW0kgv+4axprWGM7u80I4c7J0Q03AWaLyn2J1h4=", "FXLgygiGCnpLTiiBsuzlvLIS9ai8ihfzjLPIKhZb95c=", "VEyFsgzBeH/c7v2nHeTM0a27mdqz7hrmJVblRilj9vA=", "+0yheehNnSu8sWVHXH3Bp+87itkJUIN7ci6fnWe4JtA=", "q09u0uRpwQWmsiEtbciuvdXfghpBMQ6/p/TrWgqcymA=", "TuwCFrMKX544G8AOzwOF+bcMPYrQcXaetXjUoYkgZ/0=", "fIZEPINSPnPmQXBxJRnu28jivuk2RyzCq8blkxz6kwQ=", "e3xmUdzyr+sIGpk75uQmgPqIUIDz808SSYUl3LBTqqQ=", "S6nfvBHMRAypW5U6rX1Wjlr2Yblo9YzVNi2EzkFtBxc=", "As6RBG9D4DxchIAVq2lzdaoguTpYpXIzE6rg7Ge3TgM=", "5OP8cyWLkJJ/k6BDLxER+RiorUI+Eqfs4O9rhg5TmAA=", "6HDLnu1pvZGgMLfsojBHvceNmOoa3IT1Y9bEt4kOrvo=", "Xy7e0n7o7+9yaF0T7yis6tjBQyYPIhNttw0+xAYnKRg=", "hLLkPwM1n7yw4nPgpCYz+zWKuYh3N0S6ElRtdkgCH/s=", "VdmPdsQO/gJfyBxcVeFn17NlhKlJhpuAInoxKJ/8OEs=", "7X+cJSUAUxqjixOWZT7AMcwBgjG7ePUsRcqmKmtYggg=", "8JkElX9pfTPjlmrmDzP2oCTK8X4f2E18Frw36WP7cZA=", "TepdT7rumhGfJOuB4OgEiCi7OGrIApjSAGtKJY8Kp9E=", "oXt8QBGHTOZp+PLp46dCqk83tjT/3mGnsVpR97Epujo=", "oka1/A+MBl2o/g28u942fpAH0yhVFDn9EtDAvv3YBzA=", "cY4eLL32avak/7MSRa9rAEIZTWluXFbnbEcYSyLR34w=", "SlKO8PGFz8UJ+0R48lrv8jK4sIk0r/aY2u4JQNjpIrM=", "65FPFImDtvG4Gw32zPjwNG7QmHe7JLh3wJ6FUnPRc1Y=", "PnLeiDgySYmzvKR3nMd55aY//y/uKbZtC43nRmKb628=", "sGVXhF3RRwOgxeer6yK5mSMYhh6jat5Jne4Ic+vTrXs=", "AAv7Cpwvff9g2R09CnEDd7/8cI8IPKDCfJTyHFb1cvQ=", "LIgtAUYW2R+I5bayNe4CoSPrwjP7MRZoYh7KNb2Bkq0=", "fImNv6U1VUnuN1ADUhPi/opjzWu/GaXXKUxmn/1RfBY=", "45D9J70zz32Aw7fyYprbXpvjGY19qsMkXJMRDgM2/5c=", "XRlJIisUlBVL6K9t1JJtAUSBNS7oq86z0QvP9Dy1lXU=", "/cO827OcC0V6PxWcR9S3bQUEKRc+i+njajaDzyJDLaY=", "x5FJphr8n/P5e4mpEI5mo/6HIynfLMDDkr0MZBGs9is=", "yQDn8bpOvMewy+YYfWeseGoZfJl2uV0EmnlNvJPDJlw=", "nZHlk6sL5sBH4iE0Im8Uo4usiviu6+mNWLJEcvwoBZY=", "PaZpLpfaOxU1F+sBovXFOgJeLue9lalDxxNWRM7lVzI=", "0B+niMVpcp/wpqIKa/Z+BJRUjyMHuRwwi3nGMTuhJLQ=", "qTr6ZLLOT+pwFjApldfohmcNCxFFhPFYWq7R3jtNU8s=", "iKrvR5u1Y7oQK5HzqPsV98WSyew5qGhWUNIwGcomMCc=", "W/ugf+e28Wh6cz4IrtZ30nGQentguacMs1lWMdnQ2H4=", "vs+9Nu43OE4u3LAYXN1rrCF5XFnnBvVLIDD+H7rvNQ0=", "O5eHugfEDjpa6RUgKY5Cgi0aYQizux14w/8nG5SRvIw=", "XI/M7TklMMiNqqCh3HFAyrP0RMMgjAsZIv0IYen18c8=", "+JvT3AcxXgTmqbQNQQ/Qz902VT6BWZfNcVkH/4gMXQM=", "2fG2AGChH+0DgO7QL3T+LN2HrMnv3JcC5vLo+NRbP4M=", "6+pRChoNiLL/FkIUaL3VQ5Bzc5oE3ZFJ6Udf8xo7LyE=", "cTCexjCPrXQzMQCl1NW9r26yF1z4NvTq4WUBytT6De8=", "50dBQpGxX8z1sKg7kx1AKFUZkHCXIKEnHSwrfKHEFJM="], "CachedAssets": {"50dBQpGxX8z1sKg7kx1AKFUZkHCXIKEnHSwrfKHEFJM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\inx3g92fnj-i78plulxfw.gz", "SourceId": "EmpMgr", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "EmpMgr#[.{fingerprint=i78plulxfw}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\EmpMgr.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c53z5wyf9d", "Integrity": "sH9AKm6gnPMa8nGOq2pbikhYPiVHlbwuEOQKo+ZcEgY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\EmpMgr.bundle.scp.css", "FileLength": 538, "LastWriteTime": "2025-09-06T14:47:49.7180788+00:00"}, "iKrvR5u1Y7oQK5HzqPsV98WSyew5qGhWUNIwGcomMCc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\29dzasscuj-x0q3zqp4vz.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-09-06T14:47:49.8063244+00:00"}, "qTr6ZLLOT+pwFjApldfohmcNCxFFhPFYWq7R3jtNU8s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\xabnt4su6a-ag7o75518u.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-09-06T14:47:49.7983182+00:00"}, "0B+niMVpcp/wpqIKa/Z+BJRUjyMHuRwwi3nGMTuhJLQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\9yzgjok0kn-lzl9nlhx6b.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-09-06T14:47:49.7743466+00:00"}, "PaZpLpfaOxU1F+sBovXFOgJeLue9lalDxxNWRM7lVzI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\yhkgu7ckf9-mrlpezrjn3.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-09-06T14:47:49.7662705+00:00"}, "nZHlk6sL5sBH4iE0Im8Uo4usiviu6+mNWLJEcvwoBZY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\tnnl6kleqi-83jwlth58m.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-09-06T14:47:49.757645+00:00"}, "yQDn8bpOvMewy+YYfWeseGoZfJl2uV0EmnlNvJPDJlw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\iswagyvus1-356vix0kms.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-09-06T14:47:49.7531317+00:00"}, "x5FJphr8n/P5e4mpEI5mo/6HIynfLMDDkr0MZBGs9is=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\z6mqu3168w-4v8eqarkd7.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-09-06T14:47:49.7441487+00:00"}, "/cO827OcC0V6PxWcR9S3bQUEKRc+i+njajaDzyJDLaY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\p05qf7n4pl-47otxtyo56.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-09-06T14:47:49.7411093+00:00"}, "XRlJIisUlBVL6K9t1JJtAUSBNS7oq86z0QvP9Dy1lXU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\2hvf644ell-0j3bgjxly4.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-09-06T14:47:49.7391127+00:00"}, "45D9J70zz32Aw7fyYprbXpvjGY19qsMkXJMRDgM2/5c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\7djawnmx7m-63fj8s7r0e.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-09-06T14:47:49.7175802+00:00"}, "fImNv6U1VUnuN1ADUhPi/opjzWu/GaXXKUxmn/1RfBY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\mu8mel097m-h1s4sie4z3.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-09-06T14:47:49.9515489+00:00"}, "LIgtAUYW2R+I5bayNe4CoSPrwjP7MRZoYh7KNb2Bkq0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\vvogrlz59y-notf2xhcfb.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-09-06T14:47:49.9421044+00:00"}, "AAv7Cpwvff9g2R09CnEDd7/8cI8IPKDCfJTyHFb1cvQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\vvn6ldwjcr-y7v9cxd14o.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-09-06T14:47:49.9384876+00:00"}, "sGVXhF3RRwOgxeer6yK5mSMYhh6jat5Jne4Ic+vTrXs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ujn2mfnjhz-jj8uyg4cgr.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-09-06T14:47:49.9328311+00:00"}, "PnLeiDgySYmzvKR3nMd55aY//y/uKbZtC43nRmKb628=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\jmwtazhcf8-kbrnm935zg.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-09-06T14:47:49.9294503+00:00"}, "65FPFImDtvG4Gw32zPjwNG7QmHe7JLh3wJ6FUnPRc1Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\et5sxto7xf-vr1egmr9el.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-09-06T14:47:49.9167694+00:00"}, "SlKO8PGFz8UJ+0R48lrv8jK4sIk0r/aY2u4JQNjpIrM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\4pyy1armbb-iovd86k7lj.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-09-06T14:47:49.9095815+00:00"}, "cY4eLL32avak/7MSRa9rAEIZTWluXFbnbEcYSyLR34w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\z3wr01gbdi-493y06b0oq.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-09-06T14:47:49.8883274+00:00"}, "oka1/A+MBl2o/g28u942fpAH0yhVFDn9EtDAvv3YBzA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\4hdu0n3z1i-6pdc2jztkx.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-09-06T14:47:49.8813299+00:00"}, "oXt8QBGHTOZp+PLp46dCqk83tjT/3mGnsVpR97Epujo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\rzhl4ird3i-6cfz1n2cew.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-09-06T14:47:49.8487412+00:00"}, "TepdT7rumhGfJOuB4OgEiCi7OGrIApjSAGtKJY8Kp9E=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\wiyafr2usj-ft3s53vfgj.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-09-06T14:47:49.9085839+00:00"}, "8JkElX9pfTPjlmrmDzP2oCTK8X4f2E18Frw36WP7cZA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\95aor8niql-pk9g2wxc8p.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-09-06T14:47:49.8833265+00:00"}, "7X+cJSUAUxqjixOWZT7AMcwBgjG7ePUsRcqmKmtYggg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\lclqetud77-hrwsygsryq.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-09-06T14:47:49.8712319+00:00"}, "VdmPdsQO/gJfyBxcVeFn17NlhKlJhpuAInoxKJ/8OEs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ms94dy0zhx-37tfw0ft22.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-09-06T14:47:49.8297072+00:00"}, "hLLkPwM1n7yw4nPgpCYz+zWKuYh3N0S6ElRtdkgCH/s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\7588g70b0f-v0zj4ognzu.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-09-06T14:47:49.8078177+00:00"}, "Xy7e0n7o7+9yaF0T7yis6tjBQyYPIhNttw0+xAYnKRg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\sapn6tyfxr-46ein0sx1k.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-09-06T14:47:49.76677+00:00"}, "6HDLnu1pvZGgMLfsojBHvceNmOoa3IT1Y9bEt4kOrvo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\oqhreanw7m-pj5nd1wqec.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-09-06T14:47:49.7551417+00:00"}, "5OP8cyWLkJJ/k6BDLxER+RiorUI+Eqfs4O9rhg5TmAA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ylixe7zkmk-s35ty4nyc5.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-09-06T14:47:49.8392155+00:00"}, "As6RBG9D4DxchIAVq2lzdaoguTpYpXIzE6rg7Ge3TgM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\q1a7i788qr-nvvlpmu67g.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-09-06T14:47:49.8222172+00:00"}, "S6nfvBHMRAypW5U6rX1Wjlr2Yblo9YzVNi2EzkFtBxc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\hknt7h4ilw-06098lyss8.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-09-06T14:47:49.8151637+00:00"}, "e3xmUdzyr+sIGpk75uQmgPqIUIDz808SSYUl3LBTqqQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\llpferqm7o-j5mq2jizvt.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-09-06T14:47:49.8083163+00:00"}, "fIZEPINSPnPmQXBxJRnu28jivuk2RyzCq8blkxz6kwQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\4uxiymxczk-tdbxkamptv.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-09-06T14:47:49.7993156+00:00"}, "TuwCFrMKX544G8AOzwOF+bcMPYrQcXaetXjUoYkgZ/0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\wwveklz2bz-c2oey78nd0.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-09-06T14:47:49.7903328+00:00"}, "q09u0uRpwQWmsiEtbciuvdXfghpBMQ6/p/TrWgqcymA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ovf3g74ouy-lcd1t2u6c8.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-09-06T14:47:49.7833297+00:00"}, "+0yheehNnSu8sWVHXH3Bp+87itkJUIN7ci6fnWe4JtA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\r6knf4se69-r4e9w2rdcm.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-09-06T14:47:49.777341+00:00"}, "VEyFsgzBeH/c7v2nHeTM0a27mdqz7hrmJVblRilj9vA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\8fdwrzbsn7-khv3u5hwcm.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-09-06T14:47:49.7607809+00:00"}, "FXLgygiGCnpLTiiBsuzlvLIS9ai8ihfzjLPIKhZb95c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\5xncts6395-jd9uben2k1.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-09-06T14:47:49.7556457+00:00"}, "BcP3OW0kgv+4axprWGM7u80I4c7J0Q03AWaLyn2J1h4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\m3qxu7fmoz-dxx9fxp4il.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-09-06T14:47:49.7476421+00:00"}, "oYK1PxN4ELv7fo7js+JRoF6Bt9dscOQl04Lhwwxy0dQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\xxb0dek9bm-ee0r1s7dh0.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-09-06T14:47:49.742651+00:00"}, "OetjkAO9yBS/p8O3ZMmR2v4EVxEkOiP7Xog6RGTCIUs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\5bksauz26z-rzd6atqjts.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-09-06T14:47:49.7345991+00:00"}, "8pJd7FDDm3fYzlZuCDVpDRZkM5sMZI/Iadw7V6E7T5M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ze0rzqqdpe-fsbi9cje9m.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-09-06T14:47:49.7271135+00:00"}, "Y8Jdk8/psEoD0va5Aw9o/tv0rE1FqWjo0Zb4kOxERHM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\t3pdg8q3wr-b7pk76d08c.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-09-06T14:47:49.7246172+00:00"}, "jaCqwf5K7tQW5VlcoSgHqjVdgGCHwxysTYMyACs6hr0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\lupukxohww-fvhpjtyr6v.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-09-06T14:47:49.7221009+00:00"}, "qMm3JmvrwiI80vnMkTs5sruRKw4+MFZu3lABuLOKCgQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\zbvp3xg0de-ub07r2b239.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-09-06T14:47:49.7165823+00:00"}, "C7t0GqK5meB3+hl0GhgsWbCii69qxpd2Ig/QlgzIjoM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\r3de5q5ra6-cosvhxvwiu.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-09-06T14:47:49.8297072+00:00"}, "Lg8ZJFKTJxyR4KaGiqJb+PWrYxIF3tKLctFH4Xryens=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\be9g0u61us-k8d9w2qqmf.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-09-06T14:47:49.8171681+00:00"}, "whAZvY5SXX67Qan3o/aaS31DAl8I5Vu1IOQGd45e/ao=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\73dnc7it4h-ausgxo2sd3.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-09-06T14:47:49.8083163+00:00"}, "ASgVU3U7w7k60ZIpLCBc+rAUOb4pZcEyORawZvusIvo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\wq5km40p6y-d7shbmvgxk.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-09-06T14:47:49.7993156+00:00"}, "V/hQ/yVY/cqnkgk52S/wxP4O6s6nCIB6z2uqfEfbtlQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\icz8vgktcm-aexeepp0ev.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-09-06T14:47:49.7793364+00:00"}, "VdgnW/fmm7srbFQniNEY2KQhHHYUmv8vQpbHTKUv+vk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\1hcsytk711-erw9l3u2r3.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-09-06T14:47:49.7682667+00:00"}, "JVAKo7M1IC1YC62H1w2s/gKznveLHAqO5Oqs4cctF7w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\u6m8bioa4e-c2jlpeoesf.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-09-06T14:47:49.7607809+00:00"}, "JlpmPAX41yLCyEODVRYIaw7RdSuWKuQSAs/dZVNY5KE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ltockhsibq-bqjiyaj88i.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-09-06T14:47:49.742115+00:00"}, "ReGHhp/tpqJGOTH5ofoHjKjSAo5l8awWa6NHfBeZ2+0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\5dnelliqu1-9mph4dokiv.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/theme-manager#[.{fingerprint=9mph4dokiv}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\theme-manager.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vpg0pp4vuf", "Integrity": "Xe5k/oLi0fncEfX5iHwIV5CpBGyUTZLbEPk1RrwDVzI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\theme-manager.js", "FileLength": 6904, "LastWriteTime": "2025-09-06T14:47:49.7350978+00:00"}, "9/LeCwHrc+c3TWKquxgDbjsFi9Pd5DIXdfRIlBopZXs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\e2h6q6b0lp-xtxxf3hu2r.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-09-06T14:47:49.7316049+00:00"}, "LbEtXPa6wmi+nXwxJ6khqtPq1x2uYXkmA1G2+XV2a5g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\qdvgdypax6-8n3ths2svu.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/performance-optimizer#[.{fingerprint=8n3ths2svu}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\performance-optimizer.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "817s5zppsz", "Integrity": "ZIwivQfRS9nYmbxUTqy7XULkWRAZJUuKTa1bhNn1wj0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\performance-optimizer.js", "FileLength": 3696, "LastWriteTime": "2025-09-06T14:47:49.7276121+00:00"}, "e5BR6zTh9LkCgLPgaqQCMk75C9u1x9L+3YcERjbJc6Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-09-06T14:47:49.7246172+00:00"}, "I4cI6T7PSFF+Ko+uhefwnGemdYJCN89CBExY6B+46wE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\5ls8oujwu8-hqc3cwchdn.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/notification-system#[.{fingerprint=hqc3cwchdn}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\notification-system.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pzld4twenz", "Integrity": "lUgBl9txpBYZQkqXO6K9CxdZFzEOG0SEDJgUA4AVMcs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\notification-system.js", "FileLength": 3749, "LastWriteTime": "2025-09-06T14:47:49.7251166+00:00"}, "TzKdr9175hRz/VtPt+Hj04eFlo1AayWaMA1qvzm3d2w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\tk5dntihez-vdiodh0fwa.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/loading#[.{fingerprint=vdiodh0fwa}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\loading.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wasz7wi5o5", "Integrity": "CMFDe5OW2HYzmKpC978UzA7kPLviVu1PbsyShUdblf4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\loading.js", "FileLength": 1497, "LastWriteTime": "2025-09-06T14:47:49.7236193+00:00"}, "6YVUs5bvlWDBLYLe6soDtIXyUPHXEBwWemG3jet+sdQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\grsbdfe5s3-fm3hgmrtyz.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/form-fixes#[.{fingerprint=fm3hgmrtyz}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\form-fixes.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fktw1irz80", "Integrity": "r/bROCER3/kTCbOWTPej5s7BZqffS6aWMU7TVZS/F98=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\form-fixes.js", "FileLength": 2404, "LastWriteTime": "2025-09-06T14:47:49.7221009+00:00"}, "/3vG/S8JNMbIL88NOUy1U39Fcd2WXnotX65MjftH6vc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\qsbhzt1df6-tr8hjhsy6q.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "css/employee-management#[.{fingerprint=tr8hjhsy6q}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\employee-management.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "srme5bvwsj", "Integrity": "OHT9eTCvSamcD6PwVropUOHipOb4T9gh+MIoflagXiQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\employee-management.css", "FileLength": 1918, "LastWriteTime": "2025-09-06T14:47:49.8883274+00:00"}, "o0bt9ioEXkJ2AVKDC7P41yQcD+BeqIcxPXhPNBheNsk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-09-06T14:47:49.8853214+00:00"}, "tHoNvC0f9+Gju3ZOIUK9QlArmhU02Dj5Pj4HhjoH8gg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-09-06T14:47:49.8777428+00:00"}, "maaqKe+vYEWAvEfjBy+Ujm9buTzjA0o0r/8pMjpPjyE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-09-06T14:47:49.8592423+00:00"}, "Pnisinrb2zUiCqo8ZtXwpdyY4QAagIOfFBNAcKN1BJc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-09-06T14:47:49.8346989+00:00"}, "beFn0jNuqDTdrm8ltqt9LX87fdS1Yj8oMKsnI6gKh7c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-09-06T14:47:49.8068157+00:00"}, "/mVr3gEQ4loAUI8YcIiLqrD0v933Wem8IQ619UQXWWs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-09-06T14:47:49.7758434+00:00"}, "iqLVOTWY6Ghm6cohFUjulbU+u/pxcDPrJLP8kYiAMKw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-09-06T14:47:49.7607809+00:00"}, "2m5QXFzJl10alQ7YgvVABdzsO68jLgYnGyxitJfA9uA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-09-06T14:47:49.7365947+00:00"}, "HQDhWu0w+7EvqB1obYY+18HmX0r/tyuRdQK/jggEI/s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-09-06T14:47:49.7345991+00:00"}, "VyvIUY1PEhhlQxoj5yMPBqzlM1neuB1RQvc3AaWR/Qw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-09-06T14:47:49.7205747+00:00"}, "vlcPSy2kHTHZ05UnDmmWYST4TuHvVxleAZLTxLbk+JI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-09-06T14:47:49.7165823+00:00"}, "Imv5WJfFCjgXlRlUx/DcnCYs/ZMadtwzods/V0VCSYU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-09-06T14:47:49.9369735+00:00"}, "C+n9YhKMgY5uHrEuEq9CDJ+wChvA3b/LaUcmypDZRUk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-09-06T14:47:49.9340859+00:00"}, "rWb4bfPjjENLolV7xdtkRkK++PfNVfksQDEgWRUvMH8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-09-06T14:47:49.9322627+00:00"}, "ZPF2R0YXm5ms37jTEnhf7na4hpCS4FTKlW+BA6dsMcg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-09-06T14:47:49.9289529+00:00"}, "ovhW7dM9+Z02Yt7QI/LN+ev8tYJ+vcj5HIGK3/a4Hbk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-09-06T14:47:49.9252385+00:00"}, "yL8SVJMCwW89lbO3N5Gut5ANO2ulfjXyBUKV5AdE+PQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-09-06T14:47:49.9176293+00:00"}, "POl4/m5fatXkPxGULKdDHv23H47W5lfkVcKaPbbJ3q4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-09-06T14:47:49.912577+00:00"}, "gKZXumqn4X809m3UZUO3oQKskCLVlD0Z0YRUmzDCSVs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-09-06T14:47:49.8993115+00:00"}, "EoVXo9w/Gik7d4913+ydunwXArbfzNVCIsWB6FBihsg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-09-06T14:47:49.8813299+00:00"}, "G+bJjoL0uAYhF5oFa7YV727QifDOd1sWLkdIGRhUKgM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-09-06T14:47:49.8542373+00:00"}, "TtrWARox2WEhxfjG8yaNCgVC01WbPqH1YSb6jpjDP8w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-09-06T14:47:49.8727517+00:00"}, "ozK3TBiygLYVdfesq61LP09+jnqtTw1d64tRsX9kqBE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-09-06T14:47:49.8527334+00:00"}, "lVXWYVGa92PZsHGZTbEHBooaD5XJYMBWSbu2UTekjLs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-09-06T14:47:49.8407119+00:00"}, "3ZpUPf1j0Lynk7umE6s7Lc9hSlquLnSJ9Gwi6sIuiJg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-09-06T14:47:49.8018108+00:00"}, "tAIU+iuxU3mAFHYuQcc81yEsFSetXzHqqqQ6SyK59Go=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-09-06T14:47:49.7928278+00:00"}, "8/F0Yk5TlUkyqcijA1GXYVCncP/p4aZe/4olEzcH30U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-09-06T14:47:49.7592833+00:00"}, "dE3yTg6Xrx439P/PAclbsWPBpza6TtdLHM62jGeSX1M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-09-06T14:47:49.7391127+00:00"}, "z2Q1vRmkdH7+3W4VSwKDOP6SqlA9XKxnllEekpvj/lA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-09-06T14:47:49.8833265+00:00"}, "sCaKQkaR9/uFtMq81on/YyUkgr3WFYER7i0kQgXkx54=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-09-06T14:47:49.8702271+00:00"}, "MyACmQbrFABEm7XgNEMIOBPDoRLt/6zG5h2LhTbtpx8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-09-06T14:47:49.9085839+00:00"}, "xQ+sLSKA718tGNnuUPlMFIeDQk4ycv1VF7vhNU7MH6U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-09-06T14:47:49.9003095+00:00"}, "Qj+T2aRmkU9qzbciQPfWY2XvgIUG5jJIFRusCvAiMuc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-09-06T14:47:49.8843238+00:00"}, "sHiNGJ/HuqqPw9dmMxFeUKi4/X0RnEDE0HpDqhEUZhU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-09-06T14:47:49.8762456+00:00"}, "2xjNe3S75g51KMK6cHflL40XYWYo7izhpiWsof9g9OI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-09-06T14:47:49.8432508+00:00"}, "8HSaa6CCmoZmJjXLml/6x+iAKoMs2jg60aMGv/GDK2A=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-09-06T14:47:49.8282097+00:00"}, "/pWaQn7Z93IFH341ZEAsPXMhfY4SNBO4NlPPApVwWWY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-09-06T14:47:49.812668+00:00"}, "AzVPoftDc9ubRfDV6jnZt692Ao/mj4FqxUIlB3Z3rZU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-09-06T14:47:49.7983182+00:00"}, "KqlBDu3nptZbRXiXx6XQzdKkODDaqpMvZGSZ5eoIVPY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-09-06T14:47:49.7692724+00:00"}, "gbIsVSQuvkEWqbIunoUyFP0qgI33L+FfCHSczVKj9Cs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-09-06T14:47:49.7581438+00:00"}, "VigPW0QbaD5sgrB/5Uw810zL1dW2Pmf0DRSlvTu4iiw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-09-06T14:47:49.7491393+00:00"}, "vUP/WJD55lhHxZjleKGgeWUy0FjkFf8SQOKAvFpnNwQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-09-06T14:47:49.7436493+00:00"}, "WemYvkbOng4CFuVw12YxKO1VyehXhxRA9U/bgZpcA0k=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-09-06T14:47:49.7321043+00:00"}, "EmbX6kYMujdqD2Ez2GMNPBEyKEgknCP3ihE1Y0ib8ic=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-09-06T14:47:49.7200754+00:00"}, "/bGjzp7199wfpB12bWqEaeMUW5rV8zwaiO5AMA/p8d0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-09-06T14:47:49.7160836+00:00"}, "JtZv5T0dyt/XX2u5xFTM6I5dQB/YZyn8b3BYdWx8KgE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-09-06T14:47:49.8381995+00:00"}, "QxAbYZ+6CtPw1EdWQeXU7Z3ipbutrjoGU2SGj1XBles=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-09-06T14:47:49.8048184+00:00"}, "tDIt0qutR+zO3bekb9bqLJKq2DU7jRidKm6JfATtsGI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-09-06T14:47:49.796821+00:00"}, "hhbylOg4CQQEl0tv+ZJtAgK2d+HlLOG0tu1DT1qhp6Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-09-06T14:47:49.7848271+00:00"}, "bVKUweVXN5KmGB6oa/q89+nyPHDWUgCZR0CZQEeajmg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-09-06T14:47:49.7808338+00:00"}, "kMds8HPZdGBVA/NFzSq/SxAYPDP+OXYPFjARQzlu5Ng=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-09-06T14:47:49.7586432+00:00"}, "KhAQJvvhALXB2ruCNt8psucDFuzjh8lirzCLmpbwB1o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-09-06T14:47:49.8078177+00:00"}, "KEN6SPVCelLJX2s4PtgLs7hLc3xpAjerDi3QlkKjTXc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-09-06T14:47:49.786823+00:00"}, "XjfrF84JhYCVTTqhtgPmjB0kOW1cb+fV8OUiFLqCOC8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-09-06T14:47:49.7843278+00:00"}, "QKN4aBptOA4b07kBZFKxq26HEvjaXpa2A0roEujb26Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-09-06T14:47:49.7586432+00:00"}, "NVR8HRiwO/BpmHKal1yz76vU+7XESlzqfzrFtTGrJdI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-09-06T14:47:49.7506356+00:00"}, "oEF3ZeBHS66psmlLYIWsKfaY8EYVyMarRG/gdFuppFg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-09-06T14:47:49.7261146+00:00"}, "/G5rZk9jCzMo+o1Ou/Ol1LMVqPe3y6dKjvCIEikzC7U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-09-06T14:47:49.7541384+00:00"}, "4L8up5X8LuJW6siHvD9w5lfkbc9mLQMPVSkpI6VMo8Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-09-06T14:47:49.7205747+00:00"}, "8gyV4d10Ft9FD38pzgeDXJ2DJwdiATdccMYkblqFDOI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-09-06T14:47:49.7236193+00:00"}, "BC9/yB4VNYpHKRLzHz6ZJcg2yNl+8Gpsk2185lc+/Ns=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\wzoe3r2s97-kloo121ts9.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/advanced-search#[.{fingerprint=kloo121ts9}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\advanced-search.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "16ssdp2f6a", "Integrity": "mY6tD07yocYbILBZMnlJD6TYhoL2BfIOi7969Oy0KaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\advanced-search.js", "FileLength": 5687, "LastWriteTime": "2025-09-06T14:47:49.719077+00:00"}, "EoCb7mDsbu6Y7TkU1wNfGLlB7EeJFsbngd9OjgEKjSU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\jl25rv1rp1-afw5n2hlcd.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "js/auto-save#[.{fingerprint=afw5n2hlcd}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\auto-save.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gl8o4p8j2g", "Integrity": "hAlrmF2D/9gHkrXWzHFkgbBl7ASxFeP9wn/2Fkpk1Ek=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\js\\auto-save.js", "FileLength": 2844, "LastWriteTime": "2025-09-06T14:47:49.7205747+00:00"}, "UcWNS+SM7pvf7b58IHw8+C3ufICOQXalipyZuj3+ze0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\mtcxtrh8nl-bi5zyb4xrg.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "css/site#[.{fingerprint=bi5zyb4xrg}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g9wj74zl21", "Integrity": "7G4jVn1OXuPm9FhzKWlsIOj9Z6WsAA2XAZuA+8WViy0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\site.css", "FileLength": 2555, "LastWriteTime": "2025-09-06T14:47:49.8552405+00:00"}, "6PHHO5WUH7wCEOI2MnT8MNrQBNbD1Vx0/2rKGQZXzqw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\7xzul22b9q-huq8638ofr.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "css/logo-white#[.{fingerprint=huq8638ofr}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\logo-white.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jay2ab5sdg", "Integrity": "5T/xBuD3XT3ki8Gm8pB0NoCE3RpGi3AQ3DURwIZ9Vs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\logo-white.css", "FileLength": 1076, "LastWriteTime": "2025-09-06T14:47:49.8983134+00:00"}, "6+pRChoNiLL/FkIUaL3VQ5Bzc5oE3ZFJ6Udf8xo7LyE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\uyqeku5coa-mlv21k5csn.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-09-06T14:47:49.8532327+00:00"}, "2fG2AGChH+0DgO7QL3T+LN2HrMnv3JcC5vLo+NRbP4M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\1gg0zrd2hz-87fc7y1x7t.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-09-06T14:47:49.8502382+00:00"}, "W/ugf+e28Wh6cz4IrtZ30nGQentguacMs1lWMdnQ2H4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\71kv86x8x7-0i3buxo5is.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-09-06T14:47:49.848242+00:00"}, "GLM6dKYi6l8keuxCkxBi+f+Yf69noEJ3L4QZqjh2GNU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\8pz28qrki4-c4kdtxbn41.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "css/form-enhancements#[.{fingerprint=c4kdtxbn41}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\form-enhancements.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x70sscdf2z", "Integrity": "HcxYeSxtgC9P1wlNaLqGXP6QdKGJmTx6GYmaZo6yZo4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\form-enhancements.css", "FileLength": 2033, "LastWriteTime": "2025-09-06T14:47:49.8898293+00:00"}, "c7N1SeBNju2LwDGlfO7AFFcVc8Yu+1aSCUHkEPqNlsI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-09-06T14:47:49.7316049+00:00"}, "d+OZYCvuo1dgP76+s2KPHReyI2pgAXmmSLX2S844kOQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\03hd1whjk0-dis2psivg1.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "css/login#[.{fingerprint=dis2psivg1}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\login.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ea846bh1hq", "Integrity": "9iSpzBBWasM8brhU5LxNWpKhO7LNYEU67AmZUz71D04=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\css\\login.css", "FileLength": 8272, "LastWriteTime": "2025-09-06T14:47:49.8973159+00:00"}, "vs+9Nu43OE4u3LAYXN1rrCF5XFnnBvVLIDD+H7rvNQ0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\9oiix6evld-o1o13a6vjx.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-09-06T14:47:49.8587435+00:00"}, "O5eHugfEDjpa6RUgKY5Cgi0aYQizux14w/8nG5SRvIw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\7xs15mvaqs-ttgo8qnofa.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-09-06T14:47:49.8732513+00:00"}, "XI/M7TklMMiNqqCh3HFAyrP0RMMgjAsZIv0IYen18c8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\gnr73nxpcw-2z0ns9nrw6.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-09-06T14:47:49.8913266+00:00"}, "+JvT3AcxXgTmqbQNQQ/Qz902VT6BWZfNcVkH/4gMXQM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\jrwjrbrbrk-muycvpuwrr.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-09-06T14:47:49.9003095+00:00"}, "IzVEOmFHWOQp9XFkiAzyMCwTg5bbvYz2dYEMl9M26ss=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\42a32m7hmx-61n19gt1b8.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-09-06T14:47:49.8567478+00:00"}, "cTCexjCPrXQzMQCl1NW9r26yF1z4NvTq4WUBytT6De8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\0896su1vwj-i78plulxfw.gz", "SourceId": "EmpMgr", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "EmpMgr#[.{fingerprint=i78plulxfw}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\scopedcss\\bundle\\EmpMgr.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c53z5wyf9d", "Integrity": "sH9AKm6gnPMa8nGOq2pbikhYPiVHlbwuEOQKo+ZcEgY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\scopedcss\\bundle\\EmpMgr.styles.css", "FileLength": 538, "LastWriteTime": "2025-09-06T14:47:49.7160836+00:00"}, "YegSh+oLG3kSfYSOSWHGVvLD3XBmPDloZZ9vlHt0PyI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\1fcl9j2tzp-ldq9zywrnr.gz", "SourceId": "EmpMgr", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/EmpMgr", "RelativePath": "images/default-logo#[.{fingerprint=ldq9zywrnr}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\images\\default-logo.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "prbpsofs99", "Integrity": "1fP82FQF/XYDC3Xr8JKa4yC3E070aASfU9GzMY148xM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\EMP_MGR\\EmpMgr\\wwwroot\\images\\default-logo.svg", "FileLength": 649, "LastWriteTime": "2025-09-06T14:47:49.7155842+00:00"}}, "CachedCopyCandidates": {}}