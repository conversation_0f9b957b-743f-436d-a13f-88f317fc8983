using OfficeOpenXml;

namespace EmpMgr.Services
{
    /// <summary>
    /// خدمة إعداد ترخيص EPPlus
    /// </summary>
    public static class ExcelLicenseService
    {
        private static bool _isInitialized = false;
        private static readonly object _lock = new object();

        /// <summary>
        /// تهيئة ترخيص EPPlus
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized)
                return;

            lock (_lock)
            {
                if (_isInitialized)
                    return;

                try
                {
                    // تعيين ترخيص EPPlus للاستخدام غير التجاري
                    // للإصدارات الأقدم من EPPlus
                    #pragma warning disable CS0618
                    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                    #pragma warning restore CS0618
                    _isInitialized = true;
                }
                catch (Exception)
                {
                    // إذا فشل تعيين الترخيص، نتجاهل الخطأ
                    // EPPlus سيعمل ولكن مع تحذيرات
                    _isInitialized = true;
                }
            }
        }

        /// <summary>
        /// التحقق من حالة الترخيص
        /// </summary>
        public static bool IsLicenseSet => _isInitialized;
    }
}
