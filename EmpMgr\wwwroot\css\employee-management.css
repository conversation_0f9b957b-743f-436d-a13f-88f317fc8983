/* تحسينات إدارة الموظفين */

/* بطاقات الإحصائيات */
.stat-card {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark, #0056b3) 100%);
    border-radius: 15px;
    padding: 20px;
    color: white;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.stat-card.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.stat-card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.stat-card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
}

.stat-card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
    margin-bottom: 10px;
}

.stat-content h3 {
    font-size: 2.2rem;
    font-weight: bold;
    margin: 0;
    line-height: 1;
}

.stat-content p {
    margin: 5px 0 0 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

/* حاوية البحث والفلاتر */
.search-filters-container {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.search-input-wrapper {
    position: relative;
    margin-bottom: 10px;
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 2;
}

.search-input {
    padding-right: 45px;
    padding-left: 45px;
    border-radius: 25px;
    border: 2px solid #e9ecef;
    font-size: 16px;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-2px);
}

.btn-clear {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: none;
    color: #6c757d;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.btn-clear:hover {
    background: #e9ecef;
    color: #495057;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}

.suggestion-item {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* الفلاتر المتقدمة */
.filters-section {
    margin-top: 15px;
}

.filters-content {
    background: white;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    margin-top: 10px;
}

/* شريط أدوات الجدول */
.table-toolbar {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.table-info {
    font-size: 14px;
}

/* تحسينات الجدول */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    font-size: 14px;
    padding: 15px 10px;
    vertical-align: middle;
}

.table td {
    padding: 12px 10px;
    vertical-align: middle;
}

.table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.table th.sortable:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.table th.sortable i {
    opacity: 0.5;
    transition: opacity 0.2s ease;
}

.table th.sortable:hover i {
    opacity: 1;
}

/* صفوف الموظفين */
.employee-row {
    transition: all 0.2s ease;
}

.employee-row:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: translateX(5px);
}

.employee-row.selected {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 4px solid #007bff;
}

.employee-avatar {
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.employee-avatar:hover {
    border-color: #007bff;
    transform: scale(1.1);
}

.employee-name strong {
    color: #495057;
    font-size: 14px;
}

.employee-name small {
    font-size: 12px;
}

/* الشارات */
.badge {
    font-size: 11px;
    padding: 6px 10px;
    border-radius: 15px;
}

.employee-number {
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
}

.statistical-number {
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
}

.rank-badge {
    font-weight: 500;
}

.province-badge {
    border: 1px solid #6c757d;
    color: #6c757d;
    background: transparent;
}

/* معلومات التاريخ */
.date-info {
    text-align: center;
}

.date-info small {
    font-size: 11px;
}

/* أزرار الإجراءات */
.btn-group .btn {
    border-radius: 6px;
    margin: 0 1px;
}

.btn-sm {
    padding: 6px 10px;
    font-size: 12px;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 15px;
    }
    
    .stat-content h3 {
        font-size: 1.8rem;
    }
    
    .search-filters-container {
        padding: 15px;
    }
    
    .table-responsive {
        border-radius: 10px;
    }
    
    .table th,
    .table td {
        padding: 8px 5px;
        font-size: 12px;
    }
    
    .employee-avatar {
        width: 35px !important;
        height: 35px !important;
    }
    
    .btn-sm {
        padding: 4px 6px;
        font-size: 11px;
    }
}

/* تأثيرات التحميل */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات إضافية */
.table-responsive {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-bottom: none;
    border-radius: 15px 15px 0 0 !important;
}
