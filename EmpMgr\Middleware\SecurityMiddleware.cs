using EmpMgr.Services;
using System.Net;
using System.Text.Json;

namespace EmpMgr.Middleware
{
    public class SecurityMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<SecurityMiddleware> _logger;
        private readonly ISecurityMonitoringService _securityMonitoring;

        public SecurityMiddleware(RequestDelegate next, ILogger<SecurityMiddleware> logger, ISecurityMonitoringService securityMonitoring)
        {
            _next = next;
            _logger = logger;
            _securityMonitoring = securityMonitoring;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                // التحقق من IP المحظور
                var clientIP = GetClientIPAddress(context);
                if (await _securityMonitoring.IsIPBlockedAsync(clientIP))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.Forbidden;
                    await context.Response.WriteAsync("Access denied. IP address is blocked.");
                    return;
                }

                // إضافة headers الأمان
                AddSecurityHeaders(context);

                // التحقق من معدل الطلبات
                if (await IsRateLimitExceeded(context, clientIP))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
                    await context.Response.WriteAsync("Rate limit exceeded. Please try again later.");
                    return;
                }

                // التحقق من حجم الطلب
                if (IsRequestTooLarge(context))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.RequestEntityTooLarge;
                    await context.Response.WriteAsync("Request too large.");
                    return;
                }

                // التحقق من SQL Injection
                if (ContainsSQLInjection(context))
                {
                    await _securityMonitoring.LogSecurityEventAsync("SQL_INJECTION_ATTEMPT", 
                        $"Potential SQL injection detected from {clientIP}");
                    
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    await context.Response.WriteAsync("Invalid request.");
                    return;
                }

                // التحقق من XSS
                if (ContainsXSS(context))
                {
                    await _securityMonitoring.LogSecurityEventAsync("XSS_ATTEMPT", 
                        $"Potential XSS attack detected from {clientIP}");
                    
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    await context.Response.WriteAsync("Invalid request.");
                    return;
                }

                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SecurityMiddleware");
                await HandleExceptionAsync(context, ex);
            }
        }

        private void AddSecurityHeaders(HttpContext context)
        {
            var response = context.Response;

            // منع clickjacking
            if (!response.Headers.ContainsKey("X-Frame-Options"))
                response.Headers["X-Frame-Options"] = "DENY";

            // منع MIME type sniffing
            if (!response.Headers.ContainsKey("X-Content-Type-Options"))
                response.Headers["X-Content-Type-Options"] = "nosniff";

            // تفعيل XSS protection
            if (!response.Headers.ContainsKey("X-XSS-Protection"))
                response.Headers["X-XSS-Protection"] = "1; mode=block";

            // Content Security Policy
            if (!response.Headers.ContainsKey("Content-Security-Policy"))
                response.Headers["Content-Security-Policy"] =
                    "default-src 'self'; " +
                    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " +
                    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; " +
                    "font-src 'self' https://fonts.gstatic.com; " +
                    "img-src 'self' data: blob:; " +
                    "connect-src 'self';";

            // Strict Transport Security (HTTPS only)
            if (context.Request.IsHttps && !response.Headers.ContainsKey("Strict-Transport-Security"))
            {
                response.Headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains";
            }

            // منع referrer leakage
            if (!response.Headers.ContainsKey("Referrer-Policy"))
                response.Headers["Referrer-Policy"] = "strict-origin-when-cross-origin";

            // إزالة server header
            response.Headers.Remove("Server");
        }

        private string GetClientIPAddress(HttpContext context)
        {
            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            }
            
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = context.Connection.RemoteIpAddress?.ToString();
            }

            return ipAddress ?? "Unknown";
        }

        private static readonly Dictionary<string, List<DateTime>> _requestCounts = new();
        private static readonly object _rateLimitLock = new();

        private Task<bool> IsRateLimitExceeded(HttpContext context, string clientIP)
        {
            const int maxRequests = 100; // 100 طلب
            const int timeWindowMinutes = 1; // في الدقيقة

            lock (_rateLimitLock)
            {
                if (!_requestCounts.ContainsKey(clientIP))
                {
                    _requestCounts[clientIP] = new List<DateTime>();
                }

                var now = DateTime.UtcNow;
                var cutoff = now.AddMinutes(-timeWindowMinutes);

                // إزالة الطلبات القديمة
                _requestCounts[clientIP] = _requestCounts[clientIP].Where(d => d > cutoff).ToList();

                // إضافة الطلب الحالي
                _requestCounts[clientIP].Add(now);

                // التحقق من تجاوز الحد
                if (_requestCounts[clientIP].Count > maxRequests)
                {
                    _ = Task.Run(async () => 
                    {
                        await _securityMonitoring.LogSecurityEventAsync("RATE_LIMIT_EXCEEDED", 
                            $"Rate limit exceeded for IP {clientIP}");
                    });
                    
                    return Task.FromResult(true);
                }
            }

            return Task.FromResult(false);
        }

        private bool IsRequestTooLarge(HttpContext context)
        {
            const long maxSize = 50 * 1024 * 1024; // 50 MB
            
            if (context.Request.ContentLength.HasValue)
            {
                return context.Request.ContentLength.Value > maxSize;
            }

            return false;
        }

        private bool ContainsSQLInjection(HttpContext context)
        {
            var sqlPatterns = new[]
            {
                @"\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b",
                @"\b(UNION|OR|AND)\b.*\b(SELECT|INSERT|UPDATE|DELETE)\b",
                @"('|''|--|;)",
                @"\b(SCRIPT|JAVASCRIPT|VBSCRIPT|ONLOAD|ONERROR)\b"
            };

            var queryString = context.Request.QueryString.ToString();
            var userAgent = context.Request.Headers["User-Agent"].ToString();

            foreach (var pattern in sqlPatterns)
            {
                if (System.Text.RegularExpressions.Regex.IsMatch(queryString, pattern, 
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase))
                {
                    return true;
                }

                if (System.Text.RegularExpressions.Regex.IsMatch(userAgent, pattern, 
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        private bool ContainsXSS(HttpContext context)
        {
            var xssPatterns = new[]
            {
                @"<script[^>]*>.*?</script>",
                @"javascript:",
                @"vbscript:",
                @"onload\s*=",
                @"onerror\s*=",
                @"onclick\s*=",
                @"onmouseover\s*=",
                @"<iframe[^>]*>",
                @"<object[^>]*>",
                @"<embed[^>]*>"
            };

            var queryString = context.Request.QueryString.ToString();
            var userAgent = context.Request.Headers["User-Agent"].ToString();
            var referer = context.Request.Headers["Referer"].ToString();

            var testStrings = new[] { queryString, userAgent, referer };

            foreach (var testString in testStrings)
            {
                foreach (var pattern in xssPatterns)
                {
                    if (System.Text.RegularExpressions.Regex.IsMatch(testString, pattern, 
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            context.Response.ContentType = "application/json";

            object response;

            // عدم إظهار تفاصيل الخطأ في الإنتاج
            var isDevelopment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development";
            if (isDevelopment)
            {
                response = new
                {
                    error = exception.Message,
                    stackTrace = exception.StackTrace,
                    requestId = context.TraceIdentifier
                };
            }
            else
            {
                response = new
                {
                    error = "An error occurred while processing your request.",
                    requestId = context.TraceIdentifier
                };
            }

            await context.Response.WriteAsync(JsonSerializer.Serialize(response));
        }
    }

    // Middleware للتحقق من CSRF
    public class CSRFMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ISecurityService _securityService;

        public CSRFMiddleware(RequestDelegate next, ISecurityService securityService)
        {
            _next = next;
            _securityService = securityService;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // التحقق من CSRF للطلبات POST/PUT/DELETE
            if (IsStateChangingRequest(context.Request.Method))
            {
                var token = context.Request.Headers["X-CSRF-Token"].FirstOrDefault() ??
                           context.Request.Form["__RequestVerificationToken"].FirstOrDefault();

                if (string.IsNullOrEmpty(token) || !_securityService.ValidateCSRFToken(token))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.Forbidden;
                    await context.Response.WriteAsync("Invalid CSRF token.");
                    return;
                }
            }

            await _next(context);
        }

        private bool IsStateChangingRequest(string method)
        {
            return method.Equals("POST", StringComparison.OrdinalIgnoreCase) ||
                   method.Equals("PUT", StringComparison.OrdinalIgnoreCase) ||
                   method.Equals("DELETE", StringComparison.OrdinalIgnoreCase) ||
                   method.Equals("PATCH", StringComparison.OrdinalIgnoreCase);
        }
    }

    // Extension methods للتسجيل
    public static class SecurityMiddlewareExtensions
    {
        public static IApplicationBuilder UseSecurityMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<SecurityMiddleware>();
        }

        public static IApplicationBuilder UseCSRFMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<CSRFMiddleware>();
        }
    }
}
