@{
    ViewData["Title"] = "استيراد البيانات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="import-page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="import-title">
                            <i class="fas fa-upload me-3"></i>
                            استيراد بيانات الموظفين
                        </h2>
                        <p class="import-subtitle">استيراد بيانات الموظفين من ملفات Excel، CSV، أو JSON</p>
                    </div>
                    <div class="import-actions">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            @TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            @TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["ImportResult"] != null)
    {
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            @TempData["ImportResult"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="row">
        <!-- تحميل القوالب -->
        <div class="col-lg-4 mb-4">
            <div class="card template-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-download me-2"></i>
                        تحميل القوالب
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">قم بتحميل قالب فارغ لملء البيانات</p>
                    
                    <div class="template-buttons">
                        <a asp-action="DownloadTemplate" asp-route-format="excel" class="btn btn-success btn-template">
                            <i class="fas fa-file-excel me-2"></i>
                            قالب Excel
                        </a>
                        <a asp-action="DownloadTemplate" asp-route-format="csv" class="btn btn-info btn-template">
                            <i class="fas fa-file-csv me-2"></i>
                            قالب CSV
                        </a>
                        <a asp-action="DownloadTemplate" asp-route-format="json" class="btn btn-warning btn-template">
                            <i class="fas fa-file-code me-2"></i>
                            قالب JSON
                        </a>
                    </div>

                    <div class="template-info mt-3">
                        <h6>معلومات مهمة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-info-circle text-primary me-2"></i>استخدم القالب المناسب لنوع الملف</li>
                            <li><i class="fas fa-info-circle text-primary me-2"></i>لا تغير أسماء الأعمدة</li>
                            <li><i class="fas fa-info-circle text-primary me-2"></i>تأكد من صحة البيانات قبل الاستيراد</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- استيراد الملف -->
        <div class="col-lg-8 mb-4">
            <div class="card import-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-upload me-2"></i>
                        استيراد الملف
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Import" method="post" enctype="multipart/form-data" id="importForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="format" class="form-label">نوع الملف</label>
                                <select class="form-select" id="format" name="format" required>
                                    <option value="">اختر نوع الملف</option>
                                    <option value="excel">Excel (.xlsx)</option>
                                    <option value="csv">CSV (.csv)</option>
                                    <option value="json">JSON (.json)</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="file" class="form-label">الملف</label>
                                <input type="file" class="form-control" id="file" name="file" required 
                                       accept=".xlsx,.csv,.json">
                            </div>
                        </div>

                        <div class="upload-area" id="uploadArea">
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                <h5>اسحب الملف هنا أو انقر للاختيار</h5>
                                <p class="text-muted">الحد الأقصى لحجم الملف: 50 ميجابايت</p>
                                <div class="supported-formats">
                                    <span class="badge bg-success me-2">Excel</span>
                                    <span class="badge bg-info me-2">CSV</span>
                                    <span class="badge bg-warning">JSON</span>
                                </div>
                            </div>
                        </div>

                        <div class="file-info" id="fileInfo" style="display: none;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file fa-2x text-primary me-3"></i>
                                <div class="flex-grow-1">
                                    <h6 class="file-name mb-1"></h6>
                                    <small class="file-size text-muted"></small>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFile()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <div class="import-options mt-4">
                            <h6>خيارات الاستيراد:</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="skipDuplicates" name="skipDuplicates" checked>
                                <label class="form-check-label" for="skipDuplicates">
                                    تخطي السجلات المكررة
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="validateData" name="validateData" checked>
                                <label class="form-check-label" for="validateData">
                                    التحقق من صحة البيانات
                                </label>
                            </div>
                        </div>

                        <div class="import-actions mt-4">
                            <button type="submit" class="btn btn-primary btn-lg" id="importBtn" disabled>
                                <i class="fas fa-upload me-2"></i>
                                بدء الاستيراد
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-lg ms-2" onclick="resetForm()">
                                <i class="fas fa-redo me-2"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="row">
        <div class="col-12">
            <div class="card info-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-info-circle me-2"></i>
                        إرشادات الاستيراد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="text-primary">الحقول المطلوبة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>رقم الموظف</li>
                                <li><i class="fas fa-check text-success me-2"></i>الاسم الأول</li>
                                <li><i class="fas fa-check text-success me-2"></i>اسم الأب</li>
                                <li><i class="fas fa-check text-success me-2"></i>الرتبة</li>
                                <li><i class="fas fa-check text-success me-2"></i>المحافظة</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-warning">تنسيق البيانات:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>التواريخ: yyyy-mm-dd</li>
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>الجنس: ذكر أو أنثى</li>
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>أسماء الرتب والمحافظات يجب أن تطابق النظام</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-danger">تحذيرات:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-exclamation-circle text-danger me-2"></i>تأكد من نسخ احتياطي قبل الاستيراد</li>
                                <li><i class="fas fa-exclamation-circle text-danger me-2"></i>راجع البيانات قبل الاستيراد</li>
                                <li><i class="fas fa-exclamation-circle text-danger me-2"></i>لا يمكن التراجع عن الاستيراد</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('file');
            const formatSelect = document.getElementById('format');
            const uploadArea = document.getElementById('uploadArea');
            const fileInfo = document.getElementById('fileInfo');
            const importBtn = document.getElementById('importBtn');
            const importForm = document.getElementById('importForm');

            // Drag and drop functionality
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('drag-over');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    handleFileSelect();
                }
            });

            uploadArea.addEventListener('click', function() {
                fileInput.click();
            });

            fileInput.addEventListener('change', handleFileSelect);
            formatSelect.addEventListener('change', validateForm);

            function handleFileSelect() {
                const file = fileInput.files[0];
                if (file) {
                    // Auto-detect format
                    const extension = file.name.split('.').pop().toLowerCase();
                    if (extension === 'xlsx') {
                        formatSelect.value = 'excel';
                    } else if (extension === 'csv') {
                        formatSelect.value = 'csv';
                    } else if (extension === 'json') {
                        formatSelect.value = 'json';
                    }

                    // Show file info
                    document.querySelector('.file-name').textContent = file.name;
                    document.querySelector('.file-size').textContent = formatFileSize(file.size);
                    
                    uploadArea.style.display = 'none';
                    fileInfo.style.display = 'block';
                    
                    validateForm();
                }
            }

            function validateForm() {
                const hasFile = fileInput.files.length > 0;
                const hasFormat = formatSelect.value !== '';
                
                importBtn.disabled = !(hasFile && hasFormat);
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Form submission with loading
            importForm.addEventListener('submit', function(e) {
                importBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاستيراد...';
                importBtn.disabled = true;
            });

            // Global functions
            window.clearFile = function() {
                fileInput.value = '';
                uploadArea.style.display = 'block';
                fileInfo.style.display = 'none';
                validateForm();
            };

            window.resetForm = function() {
                importForm.reset();
                clearFile();
                formatSelect.value = '';
            };
        });
    </script>
}

@section Styles {
    <style>
        .import-page-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .import-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
        }

        .import-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .template-card, .import-card, .info-card {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px 20px 0 0;
            border: none;
            padding: 20px;
        }

        .card-title {
            color: #2c3e50;
            font-weight: 700;
            margin: 0;
        }

        .btn-template {
            width: 100%;
            margin-bottom: 10px;
            border-radius: 15px;
            font-weight: 600;
        }

        .upload-area {
            border: 3px dashed #dee2e6;
            border-radius: 20px;
            padding: 60px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
        }

        .upload-area:hover, .upload-area.drag-over {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .upload-content h5 {
            color: #2c3e50;
            font-weight: 600;
        }

        .file-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .file-name {
            color: #2c3e50;
            font-weight: 600;
        }

        .import-options {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
        }

        .import-options h6 {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .import-actions .btn {
            border-radius: 25px;
            font-weight: 600;
            padding: 12px 30px;
        }

        .supported-formats .badge {
            font-size: 0.8rem;
        }

        .template-info ul li {
            padding: 5px 0;
            color: #6c757d;
        }

        @@media (max-width: 768px) {
            .import-title {
                font-size: 2rem;
            }

            .import-actions {
                margin-top: 20px;
            }

            .upload-area {
                padding: 40px 15px;
            }

            .btn-template {
                margin-bottom: 15px;
            }
        }
    </style>
}
