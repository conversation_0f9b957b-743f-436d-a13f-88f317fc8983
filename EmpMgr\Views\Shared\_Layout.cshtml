﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام إدارة الضباط والمنتسبين</title>

    <!-- Cairo Font -->
    <link rel="preconnect" href="http://fonts.googleapis.com">
    <link rel="preconnect" href="http://fonts.gstatic.com" crossorigin>
    <link href="http://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="http://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Bootstrap RTL -->
    <link href="http://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/form-enhancements.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/logo-white.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/EmpMgr.styles.css" asp-append-version="true" />

    @await RenderSectionAsync("Styles", required: false)

    <style>
        /* تطبيق خط Cairo على جميع العناصر */
        *, *::before, *::after {
            font-family: 'Cairo', sans-serif !important;
        }

        body, html {
            font-family: 'Cairo', sans-serif !important;
            direction: rtl;
            text-align: right;
            background-color: #f8f9fa;
        }

        /* تطبيق خط Cairo على جميع عناصر الإدخال والنصوص */
        input, textarea, select, button, label, span, div, p, h1, h2, h3, h4, h5, h6, a {
            font-family: 'Cairo', sans-serif !important;
        }

        /* تطبيق خط Cairo على عناصر Bootstrap */
        .form-control, .form-select, .btn, .nav-link, .navbar-brand, .card-header, .card-body {
            font-family: 'Cairo', sans-serif !important;
        }

        /* تطبيق خط Cairo على القوائم المنسدلة */
        option, .dropdown-item {
            font-family: 'Cairo', sans-serif !important;
        }

        /* تطبيق خط Cairo على النصوص المدخلة */
        input[type="text"], input[type="email"], input[type="password"], input[type="number"], textarea {
            font-family: 'Cairo', sans-serif !important;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
            <div class="container-fluid">
                <a class="navbar-brand d-flex align-items-center" asp-area="" asp-controller="Home" asp-action="Index">
                    <img src="~/images/moi-logo.png" alt="شعار وزارة الداخلية" width="40" height="40" class="me-2 white-logo">
                    <span class="fw-bold">نظام إدارة الضباط والمنتسبين</span>
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="تبديل التنقل">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Dashboard" asp-action="Index">
                                <i class="fas fa-tachometer-alt me-1"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home me-2 text-primary"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="employeeDropdown" role="button"
                               data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-users me-2 text-success"></i>
                                إدارة الموظفين
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" asp-controller="Employee" asp-action="Index">
                                    <i class="fas fa-list me-2 text-info"></i>قائمة الموظفين
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Employee" asp-action="Create">
                                    <i class="fas fa-user-plus me-2 text-success"></i>إضافة موظف جديد
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Employee" asp-action="Search">
                                    <i class="fas fa-search me-2 text-warning"></i>البحث المتقدم
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-chart-bar me-2 text-primary"></i>التقارير والإحصائيات
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="constantsDropdown" role="button"
                               data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-cogs me-2 text-info"></i>
                                الثوابت والإعدادات
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" asp-controller="Constants" asp-action="Index">
                                    <i class="fas fa-list-ul me-2 text-secondary"></i>جميع الثوابت
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header"><i class="fas fa-building me-2"></i>البيانات الحكومية</h6></li>
                                <li><a class="dropdown-item" asp-controller="Constants" asp-action="Ministries">
                                    <i class="fas fa-university me-2 text-primary"></i>الوزارات
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Constants" asp-action="Agencies">
                                    <i class="fas fa-sitemap me-2 text-info"></i>الوكالات
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Constants" asp-action="Directorates">
                                    <i class="fas fa-building me-2 text-success"></i>المديريات
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header"><i class="fas fa-graduation-cap me-2"></i>البيانات التعليمية</h6></li>
                                <li><a class="dropdown-item" asp-controller="Constants" asp-action="Universities">
                                    <i class="fas fa-university me-2 text-warning"></i>الجامعات
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Constants" asp-action="Colleges">
                                    <i class="fas fa-school me-2 text-danger"></i>الكليات
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Constants" asp-action="Institutes">
                                    <i class="fas fa-chalkboard-teacher me-2 text-primary"></i>المعاهد
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Constants" asp-action="Departments">
                                    <i class="fas fa-layer-group me-2 text-info"></i>الأقسام
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header"><i class="fas fa-map-marker-alt me-2"></i>البيانات الجغرافية</h6></li>
                                <li><a class="dropdown-item" asp-controller="Constants" asp-action="Provinces">
                                    <i class="fas fa-map me-2 text-success"></i>المحافظات
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Constants" asp-action="Ranks">
                                    <i class="fas fa-star me-2 text-warning"></i>الرتب والدرجات
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Promotion" asp-action="Index">
                                <i class="fas fa-chart-line me-2 text-success"></i>
                                إدارة الترقيات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="SystemSettings" asp-action="Index">
                                <i class="fas fa-cog me-2 text-secondary"></i>
                                إعدادات النظام
                            </a>
                        </li>
                    </ul>

                    <ul class="navbar-nav">
                        <!-- إشعارات -->
                        <li class="nav-item dropdown me-3">
                            <a class="nav-link position-relative" href="#" id="notificationsDropdown" role="button"
                               data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell fs-5 text-warning"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    3
                                    <span class="visually-hidden">إشعارات جديدة</span>
                                </span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                                <li><h6 class="dropdown-header">
                                    <i class="fas fa-bell me-2"></i>الإشعارات الحديثة
                                </h6></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">
                                    <div class="d-flex align-items-start">
                                        <i class="fas fa-user-plus text-success me-2 mt-1"></i>
                                        <div>
                                            <div class="fw-bold">موظف جديد</div>
                                            <small class="text-muted">تم إضافة موظف جديد للنظام</small>
                                        </div>
                                    </div>
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <div class="d-flex align-items-start">
                                        <i class="fas fa-edit text-info me-2 mt-1"></i>
                                        <div>
                                            <div class="fw-bold">تحديث بيانات</div>
                                            <small class="text-muted">تم تحديث بيانات موظف</small>
                                        </div>
                                    </div>
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <div class="d-flex align-items-start">
                                        <i class="fas fa-chart-line text-primary me-2 mt-1"></i>
                                        <div>
                                            <div class="fw-bold">تقرير جديد</div>
                                            <small class="text-muted">تقرير شهري متاح للمراجعة</small>
                                        </div>
                                    </div>
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="#">
                                    <i class="fas fa-eye me-2"></i>عرض جميع الإشعارات
                                </a></li>
                            </ul>
                        </li>

                        <!-- قائمة المستخدم -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button"
                               data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="user-avatar me-2">
                                    <i class="fas fa-user-circle fs-4 text-primary"></i>
                                </div>
                                <div class="user-info d-none d-md-block">
                                    <div class="fw-bold">@User.Identity?.Name</div>
                                    <small class="text-muted">مدير النظام</small>
                                </div>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><h6 class="dropdown-header">
                                    <i class="fas fa-user me-2"></i>حسابي
                                </h6></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-user-edit me-2 text-info"></i>الملف الشخصي
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-key me-2 text-warning"></i>تغيير كلمة المرور
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-cog me-2 text-secondary"></i>إعدادات الحساب
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header">
                                    <i class="fas fa-tools me-2"></i>أدوات
                                </h6></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-download me-2 text-success"></i>تصدير البيانات
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-upload me-2 text-primary"></i>استيراد البيانات
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-history me-2 text-info"></i>سجل النشاطات
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form asp-area="" asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h6 class="fw-bold">
                        <i class="fas fa-building me-2"></i>
                        نظام إدارة الضباط والمنتسبين
                    </h6>
                    <p class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        وزارة الداخلية - جمهورية العراق
                    </p>
                </div>
                <div class="col-md-4 text-center">
                    <h6 class="fw-bold">
                        <i class="fas fa-user-tie me-2"></i>
                        المطور
                    </h6>
                    <p class="mb-1">العقيد المهندس حيدر چياد ثويني</p>
                    <small class="text-muted">
                        <i class="fas fa-laptop-code me-1"></i>
                        مطور ومصمم النظام
                    </small>
                </div>
                <div class="col-md-4 text-end">
                    <p class="mb-1">
                        <i class="fas fa-copyright me-2"></i>
                        &copy; @DateTime.Now.Year - جميع الحقوق محفوظة
                    </p>
                    <div class="d-flex justify-content-end align-items-center">
                        <span class="badge bg-primary me-2">
                            <i class="fas fa-tag me-1"></i>
                            الإصدار 1.0
                        </span>
                        <span class="badge bg-success">
                            <i class="fas fa-shield-alt me-1"></i>
                            آمن
                        </span>
                    </div>
                </div>
            </div>

            <hr class="my-3 border-secondary">

            <div class="row">
                <div class="col-12 text-center">
                    <small class="text-muted">
                        <i class="fas fa-heart text-danger me-1"></i>
                        تم تطوير هذا النظام بعناية فائقة لخدمة وزارة الداخلية العراقية
                        <i class="fas fa-heart text-danger ms-1"></i>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="http://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="http://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/theme-manager.js" asp-append-version="true"></script>
    <script src="~/js/performance-optimizer.js" asp-append-version="true"></script>
    <script src="~/js/notification-system.js" asp-append-version="true"></script>
    <script src="~/js/form-fixes.js" asp-append-version="true"></script>
    <script src="~/js/photo-manager.js" asp-append-version="true"></script>
    <script src="~/js/camera.js" asp-append-version="true"></script>
    <script src="~/js/loading.js" asp-append-version="true"></script>

    <!-- عرض رسائل TempData -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div data-success-message="@TempData["SuccessMessage"]" class="d-none"></div>
    }
    @if (TempData["ErrorMessage"] != null)
    {
        <div data-error-message="@TempData["ErrorMessage"]" class="d-none"></div>
    }

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
