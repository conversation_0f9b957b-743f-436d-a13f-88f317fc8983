@{
    ViewData["Title"] = "لوحة التحكم";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="dashboard-title">
                            <i class="fas fa-tachometer-alt me-3"></i>
                            لوحة التحكم
                        </h2>
                        <p class="dashboard-subtitle">نظرة شاملة على إحصائيات الموظفين</p>
                    </div>
                    <div class="dashboard-actions">
                        <button class="btn btn-outline-primary me-2" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt me-2"></i>
                            تحديث
                        </button>
                        <button class="btn btn-primary" onclick="exportReport()">
                            <i class="fas fa-download me-2"></i>
                            تصدير التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Charts Row 1 -->
    <div class="row mb-4">
        <!-- توزيع الموظفين حسب الرتب -->
        <div class="col-lg-6 mb-4">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-medal me-2"></i>
                        توزيع الموظفين حسب الرتب
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="rankChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- توزيع الموظفين حسب المحافظات -->
        <div class="col-lg-6 mb-4">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        أعلى 10 محافظات
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="provinceChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="row mb-4">
        <!-- الموظفين الجدد (آخر 12 شهر) -->
        <div class="col-lg-8 mb-4">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-chart-line me-2"></i>
                        الموظفين الجدد (آخر 12 شهر)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="newEmployeesChart" height="250"></canvas>
                </div>
            </div>
        </div>

        <!-- توزيع حسب التحصيل الدراسي -->
        <div class="col-lg-4 mb-4">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-graduation-cap me-2"></i>
                        التحصيل الدراسي
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="educationChart" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 3 -->
    <div class="row mb-4">
        <!-- توزيع حسب الجنس -->
        <div class="col-lg-4 mb-4">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-users me-2"></i>
                        توزيع حسب الجنس
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="genderChart" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- توزيع حسب الحالة الاجتماعية -->
        <div class="col-lg-4 mb-4">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-heart me-2"></i>
                        الحالة الاجتماعية
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="maritalChart" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- أحدث الموظفين -->
        <div class="col-lg-4 mb-4">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-clock me-2"></i>
                        أحدث الموظفين
                    </h5>
                </div>
                <div class="card-body" id="recentEmployees">
                    <!-- سيتم تحميل القائمة ديناميكياً -->
                </div>
            </div>
        </div>
    </div>

    <!-- إعدادات المظهر -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-palette me-2"></i>
                        إعدادات المظهر
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">نمط الألوان</label>
                                <select class="form-select" id="colorTheme">
                                    <option value="default">الافتراضي</option>
                                    <option value="dark">داكن</option>
                                    <option value="blue">أزرق</option>
                                    <option value="green">أخضر</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">حجم الخط</label>
                                <select class="form-select" id="fontSize">
                                    <option value="small">صغير</option>
                                    <option value="medium" selected>متوسط</option>
                                    <option value="large">كبير</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">التحديث التلقائي</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="autoRefreshToggle" checked>
                                    <label class="form-check-label" for="autoRefreshToggle">
                                        تفعيل التحديث التلقائي
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="dashboardLoading" class="dashboard-loading">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-3">جاري تحميل البيانات...</p>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .dashboard-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
        }

        .dashboard-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .stat-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
        }

        .stat-card-primary {
            --card-color: #667eea;
            --card-color-light: #764ba2;
        }

        .stat-card-success {
            --card-color: #28a745;
            --card-color-light: #20c997;
        }

        .stat-card-info {
            --card-color: #17a2b8;
            --card-color-light: #6f42c1;
        }

        .stat-card-warning {
            --card-color: #ffc107;
            --card-color-light: #fd7e14;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            background: linear-gradient(135deg, var(--card-color), var(--card-color-light));
            margin-bottom: 20px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
            line-height: 1;
        }

        .stat-label {
            color: #6c757d;
            font-weight: 600;
            margin: 5px 0 0 0;
            font-size: 0.9rem;
        }

        .stat-growth {
            font-size: 0.8rem;
            font-weight: 600;
        }

        .stat-growth.positive {
            color: #28a745;
        }

        .stat-growth.negative {
            color: #dc3545;
        }

        .chart-card {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
            transition: transform 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-3px);
        }

        .chart-card .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px 20px 0 0;
            border: none;
            padding: 20px;
        }

        .chart-card .card-title {
            color: #2c3e50;
            font-weight: 700;
            margin: 0;
        }

        .recent-employee-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .recent-employee-item:last-child {
            border-bottom: none;
        }

        .employee-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            margin-left: 15px;
            border: 3px solid #e9ecef;
        }

        .employee-avatar-placeholder {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: 10px;
            flex-shrink: 0;
        }

        .employee-info {
            flex-grow: 1;
        }

        .employee-name {
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
            font-size: 0.9rem;
        }

        .employee-details {
            color: #6c757d;
            display: block;
            margin-top: 2px;
        }

        .employee-date {
            color: #adb5bd;
            font-size: 0.8rem;
            display: block;
            margin-top: 2px;
        }

        .dashboard-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            text-align: center;
            color: #667eea;
        }

        .dashboard-actions .btn {
            border-radius: 25px;
            font-weight: 600;
            padding: 10px 20px;
        }

        @@media (max-width: 768px) {
            .dashboard-title {
                font-size: 2rem;
            }

            .dashboard-actions {
                margin-top: 20px;
            }

            .stat-card {
                margin-bottom: 20px;
            }

            .chart-card {
                margin-bottom: 20px;
            }
        }

        /* ثيمات الألوان */
        .theme-dark {
            background-color: #2c3e50 !important;
            color: #ecf0f1 !important;
        }

        .theme-dark .card {
            background-color: #34495e !important;
            color: #ecf0f1 !important;
        }

        .theme-dark .dashboard-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
        }

        .theme-blue .dashboard-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
        }

        .theme-green .dashboard-header {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%) !important;
        }

        /* أحجام الخط */
        .font-size-small {
            font-size: 0.9rem;
        }

        .font-size-medium {
            font-size: 1rem;
        }

        .font-size-large {
            font-size: 1.1rem;
        }

        .font-size-small .dashboard-title {
            font-size: 2rem !important;
        }

        .font-size-large .dashboard-title {
            font-size: 3rem !important;
        }
    </style>
}

@section Scripts {
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2"></script>
    
    <!-- تضمين ملفات التحسينات -->
    <script src="~/js/performance-optimizer.js"></script>
    <script src="~/js/notification-system.js"></script>
    
    <script>
        class DashboardManager {
            constructor() {
                this.charts = {};
                this.refreshInterval = null;
                this.autoRefresh = true;
                this.refreshRate = 300000; // 5 دقائق
            }

            async init() {
                this.showLoading();

                try {
                    await this.loadAllCharts();
                    await this.loadRecentEmployees();
                    
                    if (this.autoRefresh) {
                        this.startAutoRefresh();
                    }
                    
                    showSuccess('تم تحميل لوحة التحكم', 'تم تحميل جميع البيانات بنجاح');
                } catch (error) {
                    console.error('خطأ في تحميل لوحة التحكم:', error);
                    showError('خطأ في التحميل', 'حدث خطأ أثناء تحميل البيانات');
                } finally {
                    this.hideLoading();
                }
            }

            showLoading() {
                document.getElementById('dashboardLoading').style.display = 'flex';
            }

            hideLoading() {
                document.getElementById('dashboardLoading').style.display = 'none';
            }



            async loadAllCharts() {
                await Promise.all([
                    this.loadRankChart(),
                    this.loadProvinceChart(),
                    this.loadNewEmployeesChart(),
                    this.loadEducationChart(),
                    this.loadGenderChart(),
                    this.loadMaritalChart()
                ]);
            }

            async loadRankChart() {
                const response = await fetch('/Dashboard/GetEmployeesByRank');
                const data = await response.json();
                
                const ctx = document.getElementById('rankChart').getContext('2d');
                this.charts.rank = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.map(item => item.rank),
                        datasets: [{
                            data: data.map(item => item.count),
                            backgroundColor: [
                                '#667eea', '#764ba2', '#f093fb', '#f5576c',
                                '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                                '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const item = data[context.dataIndex];
                                        return `${item.rank}: ${item.count} (${item.percentage}%)`;
                                    }
                                }
                            }
                        }
                    }
                });
            }

            async loadProvinceChart() {
                const response = await fetch('/Dashboard/GetEmployeesByProvince');
                const data = await response.json();
                
                const ctx = document.getElementById('provinceChart').getContext('2d');
                this.charts.province = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: data.map(item => item.province),
                        datasets: [{
                            label: 'عدد الموظفين',
                            data: data.map(item => item.count),
                            backgroundColor: 'rgba(102, 126, 234, 0.8)',
                            borderColor: 'rgba(102, 126, 234, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            async loadNewEmployeesChart() {
                const response = await fetch('/Dashboard/GetNewEmployeesChart');
                const data = await response.json();
                
                const ctx = document.getElementById('newEmployeesChart').getContext('2d');
                this.charts.newEmployees = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.map(item => item.monthName),
                        datasets: [{
                            label: 'الموظفين الجدد',
                            data: data.map(item => item.count),
                            borderColor: 'rgba(102, 126, 234, 1)',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            async loadEducationChart() {
                const response = await fetch('/Dashboard/GetEmployeesByEducation');
                const data = await response.json();
                
                const ctx = document.getElementById('educationChart').getContext('2d');
                this.charts.education = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: data.map(item => item.education),
                        datasets: [{
                            data: data.map(item => item.count),
                            backgroundColor: [
                                '#667eea', '#764ba2', '#f093fb', '#f5576c',
                                '#4facfe', '#00f2fe', '#43e97b'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 15,
                                    usePointStyle: true
                                }
                            }
                        }
                    }
                });
            }

            async loadGenderChart() {
                const response = await fetch('/Dashboard/GetEmployeesByGender');
                const data = await response.json();
                
                const ctx = document.getElementById('genderChart').getContext('2d');
                this.charts.gender = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.map(item => item.gender),
                        datasets: [{
                            data: data.map(item => item.count),
                            backgroundColor: ['#667eea', '#f5576c']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            async loadMaritalChart() {
                const response = await fetch('/Dashboard/GetEmployeesByMaritalStatus');
                const data = await response.json();
                
                const ctx = document.getElementById('maritalChart').getContext('2d');
                this.charts.marital = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.map(item => item.status),
                        datasets: [{
                            data: data.map(item => item.count),
                            backgroundColor: ['#667eea', '#764ba2', '#f093fb', '#f5576c']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            async loadRecentEmployees() {
                try {
                    const response = await fetch('/Dashboard/GetRecentEmployees');
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const data = await response.json();

                    if (data && data.length > 0) {
                        const html = data.map(emp => `
                            <div class="recent-employee-item">
                                <div class="employee-avatar-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="employee-info">
                                    <h6 class="employee-name">${emp.fullName}</h6>
                                    <small class="employee-details">${emp.rank} - ${emp.province}</small>
                                    <small class="employee-date">${emp.createdDate}</small>
                                </div>
                            </div>
                        `).join('');

                        document.getElementById('recentEmployees').innerHTML = html;
                    } else {
                        document.getElementById('recentEmployees').innerHTML = `
                            <div class="text-center text-muted">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <p>لا توجد موظفين مضافين حديثاً</p>
                            </div>
                        `;
                    }
                } catch (error) {
                    console.error('خطأ في تحميل أحدث الموظفين:', error);
                    document.getElementById('recentEmployees').innerHTML = `
                        <div class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                            <p>خطأ في تحميل البيانات</p>
                        </div>
                    `;
                }
            }

            startAutoRefresh() {
                this.refreshInterval = setInterval(() => {
                    this.refresh();
                }, this.refreshRate);
            }

            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                }
            }

            async refresh() {
                try {
                    await this.loadRecentEmployees();
                    
                    // تحديث البيانات في الرسوم البيانية
                    Object.values(this.charts).forEach(chart => {
                        if (chart && typeof chart.update === 'function') {
                            chart.update();
                        }
                    });
                    
                    showInfo('تم التحديث', 'تم تحديث البيانات تلقائياً');
                } catch (error) {
                    console.error('خطأ في التحديث التلقائي:', error);
                }
            }

            destroy() {
                this.stopAutoRefresh();
                Object.values(this.charts).forEach(chart => {
                    if (chart && typeof chart.destroy === 'function') {
                        chart.destroy();
                    }
                });
                this.charts = {};
            }
        }

        // إنشاء مثيل لوحة التحكم
        const dashboardManager = new DashboardManager();

        // تحميل لوحة التحكم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            dashboardManager.init();
            initializeAppearanceSettings();
        });

        // دوال عامة
        function refreshDashboard() {
            dashboardManager.refresh();
        }

        function exportReport() {
            showInfo('قريباً', 'ميزة تصدير التقارير ستكون متاحة قريباً');
        }

        // إعدادات المظهر
        function initializeAppearanceSettings() {
            // تحميل الإعدادات المحفوظة
            const savedTheme = localStorage.getItem('dashboardTheme') || 'default';
            const savedFontSize = localStorage.getItem('dashboardFontSize') || 'medium';
            const savedAutoRefresh = localStorage.getItem('dashboardAutoRefresh') !== 'false';

            // تطبيق الإعدادات
            document.getElementById('colorTheme').value = savedTheme;
            document.getElementById('fontSize').value = savedFontSize;
            document.getElementById('autoRefreshToggle').checked = savedAutoRefresh;

            applyTheme(savedTheme);
            applyFontSize(savedFontSize);
            dashboardManager.autoRefresh = savedAutoRefresh;

            // مستمعات الأحداث
            document.getElementById('colorTheme').addEventListener('change', function() {
                const theme = this.value;
                applyTheme(theme);
                localStorage.setItem('dashboardTheme', theme);
                showSuccess('تم التغيير', 'تم تطبيق نمط الألوان الجديد');
            });

            document.getElementById('fontSize').addEventListener('change', function() {
                const fontSize = this.value;
                applyFontSize(fontSize);
                localStorage.setItem('dashboardFontSize', fontSize);
                showSuccess('تم التغيير', 'تم تطبيق حجم الخط الجديد');
            });

            document.getElementById('autoRefreshToggle').addEventListener('change', function() {
                const autoRefresh = this.checked;
                dashboardManager.autoRefresh = autoRefresh;
                localStorage.setItem('dashboardAutoRefresh', autoRefresh);

                if (autoRefresh) {
                    dashboardManager.startAutoRefresh();
                    showSuccess('تم التفعيل', 'تم تفعيل التحديث التلقائي');
                } else {
                    dashboardManager.stopAutoRefresh();
                    showInfo('تم الإيقاف', 'تم إيقاف التحديث التلقائي');
                }
            });
        }

        function applyTheme(theme) {
            document.body.className = document.body.className.replace(/theme-\w+/g, '');
            if (theme !== 'default') {
                document.body.classList.add(`theme-${theme}`);
            }
        }

        function applyFontSize(fontSize) {
            document.body.className = document.body.className.replace(/font-size-\w+/g, '');
            document.body.classList.add(`font-size-${fontSize}`);
        }

        // تنظيف عند مغادرة الصفحة
        window.addEventListener('beforeunload', () => {
            dashboardManager.destroy();
        });
    </script>
}
