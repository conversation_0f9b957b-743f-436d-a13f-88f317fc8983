using System.ComponentModel.DataAnnotations;
using EmpMgr.Models;

namespace EmpMgr.ViewModels
{
    public class LoginViewModel
    {
        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [DataType(DataType.Password)]
        [Display(Name = "كلمة المرور")]
        public string Password { get; set; } = string.Empty;

        [Display(Name = "تذكرني")]
        public bool RememberMe { get; set; }
    }

    public class RegisterViewModel
    {
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [Display(Name = "الاسم الكامل")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [StringLength(100, ErrorMessage = "كلمة المرور يجب أن تكون على الأقل {2} أحرف وأقل من {1} حرف.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "كلمة المرور")]
        public string Password { get; set; } = string.Empty;

        [DataType(DataType.Password)]
        [Display(Name = "تأكيد كلمة المرور")]
        [Compare("Password", ErrorMessage = "كلمة المرور وتأكيد كلمة المرور غير متطابقتين.")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    public class ForgotPasswordViewModel
    {
        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = string.Empty;
    }

    public class ResetPasswordViewModel
    {
        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [StringLength(100, ErrorMessage = "كلمة المرور يجب أن تكون على الأقل {2} أحرف وأقل من {1} حرف.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "كلمة المرور")]
        public string Password { get; set; } = string.Empty;

        [DataType(DataType.Password)]
        [Display(Name = "تأكيد كلمة المرور")]
        [Compare("Password", ErrorMessage = "كلمة المرور وتأكيد كلمة المرور غير متطابقتين.")]
        public string ConfirmPassword { get; set; } = string.Empty;

        public string Code { get; set; } = string.Empty;
    }

    public class ChangePasswordViewModel
    {
        [Required(ErrorMessage = "كلمة المرور الحالية مطلوبة")]
        [DataType(DataType.Password)]
        [Display(Name = "كلمة المرور الحالية")]
        public string OldPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور الجديدة مطلوبة")]
        [StringLength(100, ErrorMessage = "كلمة المرور يجب أن تكون على الأقل {2} أحرف وأقل من {1} حرف.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "كلمة المرور الجديدة")]
        public string NewPassword { get; set; } = string.Empty;

        [DataType(DataType.Password)]
        [Display(Name = "تأكيد كلمة المرور الجديدة")]
        [Compare("NewPassword", ErrorMessage = "كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين.")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    // ViewModels للموظفين
    public class EmployeeCreateViewModel
    {
        [Display(Name = "رقم الموظف")]
        public string EmployeeNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الرقم الإحصائي مطلوب")]
        [Display(Name = "الرقم الإحصائي")]
        [RegularExpression(@"^\d+$", ErrorMessage = "الرقم الإحصائي يجب أن يحتوي على أرقام فقط")]
        public string StatisticalNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم مطلوب")]
        [Display(Name = "الاسم")]
        [RegularExpression(@"^[\u0600-\u06FF\s]+$", ErrorMessage = "الاسم يجب أن يحتوي على حروف عربية فقط")]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم الأب مطلوب")]
        [Display(Name = "اسم الأب")]
        [RegularExpression(@"^[\u0600-\u06FF\s]+$", ErrorMessage = "اسم الأب يجب أن يحتوي على حروف عربية فقط")]
        public string FatherName { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم الجد مطلوب")]
        [Display(Name = "اسم الجد")]
        [RegularExpression(@"^[\u0600-\u06FF\s]+$", ErrorMessage = "اسم الجد يجب أن يحتوي على حروف عربية فقط")]
        public string GrandFatherName { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم والد الجد مطلوب")]
        [Display(Name = "اسم والد الجد")]
        [RegularExpression(@"^[\u0600-\u06FF\s]+$", ErrorMessage = "اسم والد الجد يجب أن يحتوي على حروف عربية فقط")]
        public string GreatGrandFatherName { get; set; } = string.Empty;

        [Required(ErrorMessage = "اللقب مطلوب")]
        [Display(Name = "اللقب")]
        [RegularExpression(@"^[\u0600-\u06FF\s]+$", ErrorMessage = "اللقب يجب أن يحتوي على حروف عربية فقط")]
        public string LastName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الرتبة مطلوبة")]
        [Display(Name = "الرتبة")]
        public int RankId { get; set; }

        [Required(ErrorMessage = "الحالة الاجتماعية مطلوبة")]
        [Display(Name = "الحالة الاجتماعية")]
        public MaritalStatus MaritalStatus { get; set; }

        [Required(ErrorMessage = "الجنس مطلوب")]
        [Display(Name = "الجنس")]
        public Gender Gender { get; set; }

        [Required(ErrorMessage = "فصيلة الدم مطلوبة")]
        [Display(Name = "فصيلة الدم")]
        public BloodType BloodType { get; set; }

        [Required(ErrorMessage = "الحالة الصحية مطلوبة")]
        [Display(Name = "الحالة الصحية")]
        public HealthStatus HealthStatus { get; set; }

        [Required(ErrorMessage = "تاريخ الولادة مطلوب")]
        [Display(Name = "تاريخ الولادة")]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [Required(ErrorMessage = "محل الولادة مطلوب")]
        [Display(Name = "محل الولادة")]
        [StringLength(200, ErrorMessage = "محل الولادة يجب أن يكون أقل من 200 حرف")]
        public string PlaceOfBirth { get; set; } = string.Empty;

        [Required(ErrorMessage = "المحافظة مطلوبة")]
        [Display(Name = "المحافظة")]
        public int ProvinceId { get; set; }

        [Display(Name = "القضاء")]
        public string? District { get; set; }

        [Display(Name = "الناحية")]
        public string? Subdistrict { get; set; }

        [Display(Name = "القرية")]
        public string? Village { get; set; }

        [Display(Name = "الحي")]
        public string? Neighborhood { get; set; }

        [Display(Name = "المحلة")]
        public string? Quarter { get; set; }

        [Display(Name = "الزقاق")]
        public string? Alley { get; set; }

        [Display(Name = "الدار")]
        public string? House { get; set; }

        [Display(Name = "أقرب نقطة دالة")]
        public string? NearestLandmark { get; set; }

        [Display(Name = "العنوان الكامل")]
        public string? FullAddress { get; set; }

        [Required(ErrorMessage = "التحصيل الدراسي مطلوب")]
        [Display(Name = "التحصيل الدراسي")]
        public EducationLevel EducationLevel { get; set; }

        [Display(Name = "الجامعة")]
        public int? UniversityId { get; set; }

        [Display(Name = "الكلية")]
        public int? CollegeId { get; set; }

        [Display(Name = "القسم الأكاديمي")]
        public int? AcademicDepartmentId { get; set; }

        [Display(Name = "المعهد")]
        public int? InstituteId { get; set; }

        [Display(Name = "القسم")]
        public int? DepartmentId { get; set; }

        [Display(Name = "التخصص")]
        [StringLength(100, ErrorMessage = "التخصص يجب أن يكون أقل من 100 حرف")]
        public string? Specialization { get; set; }




        // البنية الهرمية الحكومية
        [Display(Name = "الوزارة")]
        public int? MinistryId { get; set; }

        [Display(Name = "الوكالة")]
        public int? AgencyId { get; set; }

        [Display(Name = "المديرية")]
        public int? DirectorateId { get; set; }

        [Display(Name = "القسم الحكومي")]
        public int? GovernmentDepartmentId { get; set; }

        [Display(Name = "الشعبة")]
        public int? DivisionId { get; set; }

        // القوائم للعرض
        public List<Rank> Ranks { get; set; } = new List<Rank>();
        public List<Province> Provinces { get; set; } = new List<Province>();
        public List<University> Universities { get; set; } = new List<University>();
        public List<College> Colleges { get; set; } = new List<College>();
        public List<AcademicDepartment> AcademicDepartments { get; set; } = new List<AcademicDepartment>();
        public List<Institute> Institutes { get; set; } = new List<Institute>();
        public List<Department> Departments { get; set; } = new List<Department>();


        // الصورة الشخصية
        [Display(Name = "الصورة الشخصية")]
        public IFormFile? PhotoFile { get; set; }

        [Display(Name = "بيانات الصورة")]
        public string? PhotoBase64 { get; set; }

        [Display(Name = "نوع الصورة")]
        public string? PhotoContentType { get; set; }

        [Display(Name = "اسم ملف الصورة")]
        public string? PhotoFileName { get; set; }

        // خاصية مساعدة للتحقق من وجود صورة
        public bool HasPhoto => !string.IsNullOrEmpty(PhotoBase64) || PhotoFile != null;

        [Display(Name = "تاريخ التعيين")]
        [DataType(DataType.Date)]
        [Required(ErrorMessage = "تاريخ التعيين مطلوب")]
        public DateTime AppointmentDate { get; set; } = DateTime.Today;

        [Display(Name = "تاريخ آخر ترقية")]
        [DataType(DataType.Date)]
        public DateTime? LastPromotionDate { get; set; }

        [Display(Name = "تاريخ استحقاق الترقية (محسوب)")]
        [DataType(DataType.Date)]
        public DateTime? CalculatedPromotionDate { get; set; }

        [Display(Name = "تاريخ استحقاق الترقية القادمة")]
        [DataType(DataType.Date)]
        public DateTime? NextPromotionEligibilityDate { get; set; }

        [Display(Name = "حالة الترقية")]
        [StringLength(100, ErrorMessage = "حالة الترقية يجب أن تكون أقل من 100 حرف")]
        public string? PromotionStatus { get; set; }

        [Display(Name = "الأيام المتبقية للترقية")]
        public int? DaysUntilPromotion { get; set; }

        [Display(Name = "مؤهل للترقية")]
        public bool IsEligibleForPromotion { get; set; }

        [Display(Name = "ملاحظات الترقية")]
        [StringLength(1000, ErrorMessage = "ملاحظات الترقية يجب أن تكون أقل من 1000 حرف")]
        public string? PromotionNotes { get; set; }

        // البنية الهرمية الحكومية
        public List<Ministry> Ministries { get; set; } = new List<Ministry>();
        public List<Agency> Agencies { get; set; } = new List<Agency>();
        public List<Directorate> Directorates { get; set; } = new List<Directorate>();
        public List<GovernmentDepartment> GovernmentDepartments { get; set; } = new List<GovernmentDepartment>();
        public List<Division> Divisions { get; set; } = new List<Division>();
    }

    public class EmployeeEditViewModel : EmployeeCreateViewModel
    {
        public int Id { get; set; }

        // الصورة الحالية
        [Display(Name = "الصورة الحالية")]
        public byte[]? CurrentPhoto { get; set; }

        [Display(Name = "نوع الصورة الحالية")]
        public string? CurrentPhotoContentType { get; set; }

        [Display(Name = "اسم ملف الصورة الحالية")]
        public string? CurrentPhotoFileName { get; set; }

        // خاصية مساعدة للتحقق من وجود صورة حالية
        public bool HasCurrentPhoto => CurrentPhoto != null && CurrentPhoto.Length > 0;
    }
}
