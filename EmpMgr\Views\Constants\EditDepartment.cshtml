@model EmpMgr.Models.Department

@{
    ViewData["Title"] = "تعديل القسم";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-edit me-2"></i>
                    تعديل القسم: @Model.Name
                </h2>
                <a asp-action="Departments" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة لقائمة الأقسام
                </a>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-sitemap me-2"></i>
                                تعديل بيانات القسم
                            </h5>
                        </div>
                        <div class="card-body">
                            <form asp-action="EditDepartment" method="post">
                                <input asp-for="Id" type="hidden" />
                                <input asp-for="CreatedDate" type="hidden" />
                                
                                <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                                <!-- المعهد -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label asp-for="InstituteId" class="form-label required">المعهد</label>
                                        <select asp-for="InstituteId" class="form-select" required>
                                            <option value="">اختر المعهد</option>
                                            @foreach (var institute in (ViewBag.Institutes as List<EmpMgr.Models.Institute>) ?? new List<EmpMgr.Models.Institute>())
                                            {
                                                <option value="@institute.Id" selected="@(institute.Id == Model.InstituteId)">
                                                    @institute.Name
                                                </option>
                                            }
                                        </select>
                                        <span asp-validation-for="InstituteId" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <label asp-for="Type" class="form-label required">نوع القسم</label>
                                        <select asp-for="Type" class="form-select" asp-items="Html.GetEnumSelectList<EmpMgr.Models.DepartmentType>()" required>
                                            <option value="">اختر نوع القسم</option>
                                        </select>
                                        <span asp-validation-for="Type" class="text-danger"></span>
                                    </div>
                                </div>

                                <!-- اسم القسم والكود -->
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label asp-for="Name" class="form-label required">اسم القسم</label>
                                        <input asp-for="Name" class="form-control" placeholder="أدخل اسم القسم" required />
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-4">
                                        <label asp-for="Code" class="form-label">رمز القسم</label>
                                        <input asp-for="Code" class="form-control" placeholder="مثال: CS" />
                                        <span asp-validation-for="Code" class="text-danger"></span>
                                    </div>
                                </div>

                                <!-- الوصف -->
                                <div class="mb-3">
                                    <label asp-for="Description" class="form-label">الوصف</label>
                                    <textarea asp-for="Description" class="form-control" rows="3" 
                                              placeholder="وصف مختصر عن القسم وأنشطته"></textarea>
                                    <span asp-validation-for="Description" class="text-danger"></span>
                                </div>

                                <!-- سنة التأسيس والحالة -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label asp-for="EstablishedYear" class="form-label">سنة التأسيس</label>
                                        <input asp-for="EstablishedYear" type="number" class="form-control" 
                                               min="1900" max="@DateTime.Now.Year" placeholder="@DateTime.Now.Year" />
                                        <span asp-validation-for="EstablishedYear" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">الحالة</label>
                                        <div class="form-check form-switch">
                                            <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                            <label asp-for="IsActive" class="form-check-label">
                                                القسم نشط
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات إضافية -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">تاريخ الإنشاء</label>
                                        <input type="text" class="form-control" value="@Model.CreatedDate.ToString("dd/MM/yyyy")" readonly />
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">آخر تحديث</label>
                                        <input type="text" class="form-control" value="@DateTime.Now.ToString("dd/MM/yyyy")" readonly />
                                    </div>
                                </div>

                                <!-- أزرار الإجراءات -->
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ التغييرات
                                        </button>
                                        <button type="reset" class="btn btn-outline-secondary">
                                            <i class="fas fa-undo me-2"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                    <div>
                                        <a asp-action="DeleteDepartment" asp-route-id="@Model.Id" class="btn btn-outline-danger me-2">
                                            <i class="fas fa-trash me-2"></i>
                                            حذف القسم
                                        </a>
                                        <a asp-action="Departments" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>
                                            إلغاء
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات القسم
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">
                                        <i class="fas fa-users me-2"></i>
                                        الموظفون المرتبطون
                                    </h6>
                                    <p class="text-muted">
                                        عدد الموظفين المرتبطين بهذا القسم: 
                                        <span class="badge bg-primary">0</span>
                                    </p>
                                    <small class="text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        لا يمكن حذف القسم إذا كان مرتبط بموظفين
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-info">
                                        <i class="fas fa-history me-2"></i>
                                        تاريخ القسم
                                    </h6>
                                    <ul class="list-unstyled">
                                        <li><strong>تاريخ الإنشاء:</strong> @Model.CreatedDate.ToString("dd/MM/yyyy")</li>
                                        @if (Model.EstablishedYear.HasValue)
                                        {
                                            <li><strong>سنة التأسيس:</strong> @Model.EstablishedYear.Value</li>
                                        }
                                        <li><strong>الحالة:</strong> 
                                            @if (Model.IsActive)
                                            {
                                                <span class="badge bg-success">نشط</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">غير نشط</span>
                                            }
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأكيد الحذف
            const deleteButton = document.querySelector('a[href*="DeleteDepartment"]');
            if (deleteButton) {
                deleteButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (confirm('هل أنت متأكد من حذف هذا القسم؟\n\nتحذير: سيتم حذف جميع البيانات المرتبطة بهذا القسم.')) {
                        window.location.href = this.href;
                    }
                });
            }

            // تحسين تجربة المستخدم
            const nameInput = document.getElementById('Name');
            const codeInput = document.getElementById('Code');
            
            // إنشاء كود تلقائي من الاسم إذا كان فارغاً
            nameInput.addEventListener('input', function() {
                if (!codeInput.value) {
                    const name = this.value.trim();
                    if (name) {
                        const words = name.split(' ');
                        let code = '';
                        words.forEach(word => {
                            if (word.length > 0) {
                                code += word.charAt(0).toUpperCase();
                            }
                        });
                        codeInput.value = code.substring(0, 10);
                    }
                }
            });
        });
    </script>
}
