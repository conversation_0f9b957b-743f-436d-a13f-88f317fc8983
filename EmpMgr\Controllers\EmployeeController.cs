using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using EmpMgr.Data;
using EmpMgr.Models;
using EmpMgr.Services;
using EmpMgr.ViewModels;

namespace EmpMgr.Controllers
{
    [Authorize]
    public class EmployeeController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IEmployeeNumberService _employeeNumberService;

        private readonly IExportImportService _exportImportService;

        public EmployeeController(ApplicationDbContext context, IEmployeeNumberService employeeNumberService, IExportImportService exportImportService)
        {
            _context = context;
            _employeeNumberService = employeeNumberService;
            _exportImportService = exportImportService;
        }

        // GET: Employee
        public async Task<IActionResult> Index()
        {
            var employees = await _context.Employees
                .Include(e => e.Rank)
                .Include(e => e.Province)
                .Include(e => e.University)
                .Include(e => e.Institute)
                .OrderByDescending(e => e.CreatedDate)
                .ToListAsync();

            return View(employees);
        }

        // GET: Employee/Search
        public IActionResult Search()
        {
            return View();
        }

        // GET: Employee/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var employee = await _context.Employees
                .Include(e => e.Rank)
                .Include(e => e.Province)
                .Include(e => e.University)
                .Include(e => e.Institute)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (employee == null)
            {
                return NotFound();
            }

            return View(employee);
        }

        // GET: Employee/Create
        public async Task<IActionResult> Create()
        {
            var viewModel = new EmployeeCreateViewModel
            {
                EmployeeNumber = await _employeeNumberService.GenerateEmployeeNumberAsync(),
                Ranks = await _context.Ranks.Where(r => r.IsActive).OrderBy(r => r.Order).ToListAsync(),
                Provinces = await _context.Provinces.Where(p => p.IsActive).OrderBy(p => p.Name).ToListAsync(),
                Universities = await _context.Universities.Where(u => u.IsActive).OrderBy(u => u.Name).ToListAsync(),
                Institutes = await _context.Institutes.Where(i => i.IsActive).OrderBy(i => i.Name).ToListAsync(),

                // البنية الهرمية الحكومية
                Ministries = await _context.Ministries.Where(m => m.IsActive).OrderBy(m => m.Name).ToListAsync(),
                Agencies = await _context.Agencies.Where(a => a.IsActive).OrderBy(a => a.Name).ToListAsync(),
                Directorates = await _context.Directorates.Where(d => d.IsActive).OrderBy(d => d.Name).ToListAsync(),
                GovernmentDepartments = await _context.GovernmentDepartments.Where(gd => gd.IsActive).OrderBy(gd => gd.Name).ToListAsync(),
                Divisions = await _context.Divisions.Where(div => div.IsActive).OrderBy(div => div.Name).ToListAsync()
            };

            return View(viewModel);
        }

        // POST: Employee/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(EmployeeCreateViewModel viewModel)
        {
            // التحقق من تفرد الرقم الإحصائي
            if (!await _employeeNumberService.IsStatisticalNumberUniqueAsync(viewModel.StatisticalNumber))
            {
                ModelState.AddModelError("StatisticalNumber", "الرقم الإحصائي موجود مسبقاً");
            }

            // التحقق من تفرد اسم الموظف
            if (!await _employeeNumberService.IsEmployeeNameUniqueAsync(
                viewModel.FirstName, viewModel.FatherName, viewModel.GrandFatherName, 
                viewModel.GreatGrandFatherName, viewModel.LastName))
            {
                ModelState.AddModelError("", "اسم الموظف موجود مسبقاً في النظام");
            }

            if (ModelState.IsValid)
            {


                if (ModelState.IsValid)
                {
                    var employee = new Employee
                    {
                        EmployeeNumber = viewModel.EmployeeNumber,
                        StatisticalNumber = viewModel.StatisticalNumber,

                        FirstName = viewModel.FirstName,
                        FatherName = viewModel.FatherName,
                        GrandFatherName = viewModel.GrandFatherName,
                        GreatGrandFatherName = viewModel.GreatGrandFatherName,
                        LastName = viewModel.LastName,
                        RankId = viewModel.RankId,
                        MaritalStatus = viewModel.MaritalStatus,
                        Gender = viewModel.Gender,
                        BloodType = viewModel.BloodType,
                        HealthStatus = viewModel.HealthStatus,
                        DateOfBirth = viewModel.DateOfBirth,
                        PlaceOfBirth = viewModel.PlaceOfBirth,
                        ProvinceId = viewModel.ProvinceId,
                        District = viewModel.District,
                        Subdistrict = viewModel.Subdistrict,
                        Village = viewModel.Village,
                        Neighborhood = viewModel.Neighborhood,
                        Quarter = viewModel.Quarter,
                        Alley = viewModel.Alley,
                        House = viewModel.House,
                        NearestLandmark = viewModel.NearestLandmark,
                        EducationLevel = viewModel.EducationLevel,
                        UniversityId = (viewModel.EducationLevel == EducationLevel.Bachelor ||
                                       viewModel.EducationLevel == EducationLevel.Master ||
                                       viewModel.EducationLevel == EducationLevel.PhD) ? viewModel.UniversityId : null,
                        CollegeId = (viewModel.EducationLevel == EducationLevel.Bachelor ||
                                    viewModel.EducationLevel == EducationLevel.Master ||
                                    viewModel.EducationLevel == EducationLevel.PhD) ? viewModel.CollegeId : null,
                        InstituteId = (viewModel.EducationLevel == EducationLevel.Diploma) ? viewModel.InstituteId : null,
                        DepartmentId = (viewModel.EducationLevel == EducationLevel.Diploma) ? viewModel.DepartmentId : null,
                        Specialization = viewModel.Specialization,

                        // البنية الهرمية الحكومية
                        MinistryId = viewModel.MinistryId,
                        AgencyId = viewModel.AgencyId,
                        DirectorateId = viewModel.DirectorateId,
                        GovernmentDepartmentId = viewModel.GovernmentDepartmentId,
                        DivisionId = viewModel.DivisionId,

                        // البيانات الوظيفية
                        AppointmentDate = viewModel.AppointmentDate,
                        LastPromotionDate = viewModel.LastPromotionDate,
                        CalculatedPromotionDate = viewModel.CalculatedPromotionDate,
                        CreatedDate = DateTime.Now
                    };

                    // حساب تاريخ استحقاق الترقية إذا لم يكن محسوباً مسبقاً
                    if (!employee.CalculatedPromotionDate.HasValue && employee.RankId != null)
                    {
                        var rank = await _context.Ranks.FindAsync(employee.RankId);
                        if (rank != null && rank.RankDurationYears > 0)
                        {
                            // تحديد تاريخ البداية (آخر ترقية أو تاريخ التعيين)
                            var startDate = employee.LastPromotionDate ?? employee.AppointmentDate;

                            // حساب تاريخ الاستحقاق بناءً على مدة الرتبة
                            var eligibilityDate = startDate.AddYears(rank.RankDurationYears);

                            employee.CalculatedPromotionDate = eligibilityDate;
                        }
                    }

                    // معالجة الصورة
                    await ProcessEmployeePhoto(employee, viewModel);

                    _context.Add(employee);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = "تم إضافة الموظف بنجاح";
                    return RedirectToAction(nameof(Index));
                }
            }

            // إعادة تحميل القوائم في حالة وجود أخطاء
            viewModel.Ranks = await _context.Ranks.Where(r => r.IsActive).OrderBy(r => r.Order).ToListAsync();
            viewModel.Provinces = await _context.Provinces.Where(p => p.IsActive).OrderBy(p => p.Name).ToListAsync();
            viewModel.Universities = await _context.Universities.Where(u => u.IsActive).OrderBy(u => u.Name).ToListAsync();
            viewModel.Institutes = await _context.Institutes.Where(i => i.IsActive).OrderBy(i => i.Name).ToListAsync();


            // البنية الهرمية الحكومية
            viewModel.Ministries = await _context.Ministries.Where(m => m.IsActive).OrderBy(m => m.Name).ToListAsync();
            viewModel.Agencies = await _context.Agencies.Where(a => a.IsActive).OrderBy(a => a.Name).ToListAsync();
            viewModel.Directorates = await _context.Directorates.Where(d => d.IsActive).OrderBy(d => d.Name).ToListAsync();
            viewModel.GovernmentDepartments = await _context.GovernmentDepartments.Where(gd => gd.IsActive).OrderBy(gd => gd.Name).ToListAsync();
            viewModel.Divisions = await _context.Divisions.Where(div => div.IsActive).OrderBy(div => div.Name).ToListAsync();

            // تسجيل للتشخيص
            var logger = HttpContext.RequestServices.GetService<ILogger<EmployeeController>>();
            logger?.LogInformation($"Employee Create - Ministries loaded: {viewModel.Ministries.Count}");
            logger?.LogInformation($"Employee Create - Agencies loaded: {viewModel.Agencies.Count}");

            if (viewModel.Ministries.Count == 0)
            {
                logger?.LogWarning("No ministries found in database!");
            }
            else
            {
                foreach (var ministry in viewModel.Ministries)
                {
                    logger?.LogInformation($"Ministry: {ministry.Name} (ID: {ministry.Id})");
                }
            }

            return View(viewModel);
        }

        // GET: Employee/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var employee = await _context.Employees.FindAsync(id);
            if (employee == null)
            {
                return NotFound();
            }

            var viewModel = new EmployeeEditViewModel
            {
                Id = employee.Id,
                EmployeeNumber = employee.EmployeeNumber,
                StatisticalNumber = employee.StatisticalNumber,
                FirstName = employee.FirstName,
                FatherName = employee.FatherName,
                GrandFatherName = employee.GrandFatherName,
                GreatGrandFatherName = employee.GreatGrandFatherName,
                LastName = employee.LastName,
                RankId = employee.RankId,
                MaritalStatus = employee.MaritalStatus,
                Gender = employee.Gender,
                BloodType = employee.BloodType,
                HealthStatus = employee.HealthStatus,

                // معلومات الولادة
                DateOfBirth = employee.DateOfBirth,
                PlaceOfBirth = employee.PlaceOfBirth,

                // العنوان
                ProvinceId = employee.ProvinceId,
                District = employee.District,
                Subdistrict = employee.Subdistrict,
                Village = employee.Village,
                Neighborhood = employee.Neighborhood,
                Quarter = employee.Quarter,
                Alley = employee.Alley,
                House = employee.House,
                NearestLandmark = employee.NearestLandmark,

                // التعليم
                EducationLevel = employee.EducationLevel,
                UniversityId = employee.UniversityId,
                CollegeId = employee.CollegeId,
                InstituteId = employee.InstituteId,
                DepartmentId = employee.DepartmentId,
                Specialization = employee.Specialization,

                // بيانات التعيين والترقية
                AppointmentDate = employee.AppointmentDate,
                LastPromotionDate = employee.LastPromotionDate,
                CalculatedPromotionDate = employee.CalculatedPromotionDate,
                NextPromotionEligibilityDate = employee.NextPromotionEligibilityDate,
                PromotionStatus = employee.PromotionStatus,
                DaysUntilPromotion = employee.DaysUntilPromotion,
                IsEligibleForPromotion = employee.IsEligibleForPromotion,
                PromotionNotes = employee.PromotionNotes,

                // البنية الهرمية الحكومية
                MinistryId = employee.MinistryId,
                AgencyId = employee.AgencyId,
                DirectorateId = employee.DirectorateId,
                GovernmentDepartmentId = employee.GovernmentDepartmentId,
                DivisionId = employee.DivisionId,

                // الصورة الحالية
                CurrentPhoto = employee.Photo,
                CurrentPhotoContentType = employee.PhotoContentType,
                CurrentPhotoFileName = employee.PhotoFileName,

                Ranks = await _context.Ranks.Where(r => r.IsActive).OrderBy(r => r.Order).ToListAsync(),
                Provinces = await _context.Provinces.Where(p => p.IsActive).OrderBy(p => p.Name).ToListAsync(),
                Universities = await _context.Universities.Where(u => u.IsActive).OrderBy(u => u.Name).ToListAsync(),
                Colleges = await _context.Colleges.Where(c => c.IsActive && c.UniversityId == employee.UniversityId).OrderBy(c => c.Name).ToListAsync(),
                Institutes = await _context.Institutes.Where(i => i.IsActive).OrderBy(i => i.Name).ToListAsync(),
                Departments = await _context.Departments.Where(d => d.IsActive && d.InstituteId == employee.InstituteId).OrderBy(d => d.Name).ToListAsync(),

                // البنية الهرمية الحكومية
                Ministries = await _context.Ministries.Where(m => m.IsActive).OrderBy(m => m.Name).ToListAsync(),
                Agencies = await _context.Agencies.Where(a => a.IsActive).OrderBy(a => a.Name).ToListAsync(),
                Directorates = await _context.Directorates.Where(d => d.IsActive).OrderBy(d => d.Name).ToListAsync(),
                GovernmentDepartments = await _context.GovernmentDepartments.Where(gd => gd.IsActive).OrderBy(gd => gd.Name).ToListAsync(),
                Divisions = await _context.Divisions.Where(div => div.IsActive).OrderBy(div => div.Name).ToListAsync()
            };

            return View(viewModel);
        }

        // POST: Employee/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, EmployeeEditViewModel viewModel)
        {
            if (id != viewModel.Id)
            {
                return NotFound();
            }

            // التحقق من تفرد الرقم الإحصائي
            if (!await _employeeNumberService.IsStatisticalNumberUniqueAsync(viewModel.StatisticalNumber, viewModel.Id))
            {
                ModelState.AddModelError("StatisticalNumber", "الرقم الإحصائي موجود مسبقاً");
            }

            // التحقق من تفرد اسم الموظف
            if (!await _employeeNumberService.IsEmployeeNameUniqueAsync(
                viewModel.FirstName, viewModel.FatherName, viewModel.GrandFatherName, 
                viewModel.GreatGrandFatherName, viewModel.LastName, viewModel.Id))
            {
                ModelState.AddModelError("", "اسم الموظف موجود مسبقاً في النظام");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var employee = await _context.Employees.FindAsync(id);
                    if (employee == null)
                    {
                        return NotFound();
                    }



                    employee.StatisticalNumber = viewModel.StatisticalNumber;
                    employee.FirstName = viewModel.FirstName;
                    employee.FatherName = viewModel.FatherName;
                    employee.GrandFatherName = viewModel.GrandFatherName;
                    employee.GreatGrandFatherName = viewModel.GreatGrandFatherName;
                    employee.LastName = viewModel.LastName;
                    employee.RankId = viewModel.RankId;
                    employee.MaritalStatus = viewModel.MaritalStatus;
                    employee.Gender = viewModel.Gender;
                    employee.BloodType = viewModel.BloodType;
                    employee.HealthStatus = viewModel.HealthStatus;
                    employee.ProvinceId = viewModel.ProvinceId;
                    employee.District = viewModel.District;
                    employee.Subdistrict = viewModel.Subdistrict;
                    employee.Village = viewModel.Village;
                    employee.Neighborhood = viewModel.Neighborhood;
                    employee.Quarter = viewModel.Quarter;
                    employee.Alley = viewModel.Alley;
                    employee.House = viewModel.House;
                    employee.NearestLandmark = viewModel.NearestLandmark;
                    employee.EducationLevel = viewModel.EducationLevel;
                    employee.UniversityId = (viewModel.EducationLevel == EducationLevel.Bachelor ||
                                           viewModel.EducationLevel == EducationLevel.Master ||
                                           viewModel.EducationLevel == EducationLevel.PhD) ? viewModel.UniversityId : null;
                    employee.CollegeId = (viewModel.EducationLevel == EducationLevel.Bachelor ||
                                         viewModel.EducationLevel == EducationLevel.Master ||
                                         viewModel.EducationLevel == EducationLevel.PhD) ? viewModel.CollegeId : null;
                    employee.InstituteId = (viewModel.EducationLevel == EducationLevel.Diploma) ? viewModel.InstituteId : null;
                    employee.DepartmentId = (viewModel.EducationLevel == EducationLevel.Diploma) ? viewModel.DepartmentId : null;
                    employee.Specialization = viewModel.Specialization;

                    // معلومات الولادة
                    employee.DateOfBirth = viewModel.DateOfBirth;
                    employee.PlaceOfBirth = viewModel.PlaceOfBirth;

                    // بيانات التعيين والترقية
                    employee.AppointmentDate = viewModel.AppointmentDate;
                    employee.LastPromotionDate = viewModel.LastPromotionDate;
                    employee.CalculatedPromotionDate = viewModel.CalculatedPromotionDate;
                    employee.NextPromotionEligibilityDate = viewModel.NextPromotionEligibilityDate;
                    employee.PromotionStatus = viewModel.PromotionStatus;
                    employee.DaysUntilPromotion = viewModel.DaysUntilPromotion;
                    employee.IsEligibleForPromotion = viewModel.IsEligibleForPromotion;
                    employee.PromotionNotes = viewModel.PromotionNotes;

                    // البنية الهرمية الحكومية
                    employee.MinistryId = viewModel.MinistryId;
                    employee.AgencyId = viewModel.AgencyId;
                    employee.DirectorateId = viewModel.DirectorateId;
                    employee.GovernmentDepartmentId = viewModel.GovernmentDepartmentId;
                    employee.DivisionId = viewModel.DivisionId;

                    employee.UpdatedDate = DateTime.Now;

                    // معالجة الصورة
                    await ProcessEmployeePhoto(employee, viewModel);

                    _context.Update(employee);
                    await _context.SaveChangesAsync();
                    
                    TempData["SuccessMessage"] = "تم تحديث بيانات الموظف بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!EmployeeExists(viewModel.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            // إعادة تحميل القوائم في حالة وجود أخطاء
            viewModel.Ranks = await _context.Ranks.Where(r => r.IsActive).OrderBy(r => r.Order).ToListAsync();
            viewModel.Provinces = await _context.Provinces.Where(p => p.IsActive).OrderBy(p => p.Name).ToListAsync();
            viewModel.Universities = await _context.Universities.Where(u => u.IsActive).OrderBy(u => u.Name).ToListAsync();
            viewModel.Colleges = await _context.Colleges.Where(c => c.IsActive && c.UniversityId == viewModel.UniversityId).OrderBy(c => c.Name).ToListAsync();
            viewModel.Institutes = await _context.Institutes.Where(i => i.IsActive).OrderBy(i => i.Name).ToListAsync();
            viewModel.Departments = await _context.Departments.Where(d => d.IsActive && d.InstituteId == viewModel.InstituteId).OrderBy(d => d.Name).ToListAsync();

            // البنية الهرمية الحكومية
            viewModel.Ministries = await _context.Ministries.Where(m => m.IsActive).OrderBy(m => m.Name).ToListAsync();
            viewModel.Agencies = await _context.Agencies.Where(a => a.IsActive).OrderBy(a => a.Name).ToListAsync();
            viewModel.Directorates = await _context.Directorates.Where(d => d.IsActive).OrderBy(d => d.Name).ToListAsync();
            viewModel.GovernmentDepartments = await _context.GovernmentDepartments.Where(gd => gd.IsActive).OrderBy(gd => gd.Name).ToListAsync();
            viewModel.Divisions = await _context.Divisions.Where(div => div.IsActive).OrderBy(div => div.Name).ToListAsync();


            return View(viewModel);
        }

        // GET: Employee/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var employee = await _context.Employees
                .Include(e => e.Rank)
                .Include(e => e.Province)
                .Include(e => e.University)
                .Include(e => e.Institute)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (employee == null)
            {
                return NotFound();
            }

            return View(employee);
        }

        // POST: Employee/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var employee = await _context.Employees.FindAsync(id);
                if (employee != null)
                {
                    var employeeName = employee.FullName;
                    _context.Employees.Remove(employee);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = $"تم حذف الموظف {employeeName} بنجاح";
                }
                else
                {
                    TempData["ErrorMessage"] = "الموظف غير موجود";
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"حدث خطأ أثناء حذف الموظف: {ex.Message}";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool EmployeeExists(int id)
        {
            return _context.Employees.Any(e => e.Id == id);
        }



        // AJAX Methods
        [HttpGet]
        public async Task<JsonResult> GetUniversities()
        {
            var universities = await _context.Universities
                .Where(u => u.IsActive)
                .OrderBy(u => u.Name)
                .Select(u => new { value = u.Id, text = u.Name })
                .ToListAsync();

            return Json(universities);
        }

        [HttpGet]
        public async Task<JsonResult> GetInstitutes()
        {
            var institutes = await _context.Institutes
                .Where(i => i.IsActive)
                .OrderBy(i => i.Name)
                .Select(i => new { value = i.Id, text = i.Name })
                .ToListAsync();

            return Json(institutes);
        }

        // API البحث المتقدم
        [HttpGet]
        public async Task<JsonResult> Search(
            string query = "",
            int page = 1,
            int pageSize = 20,
            string sortField = "CreatedDate",
            string sortDirection = "desc",
            int? rank = null,
            int? province = null,
            string? education = null,
            string? gender = null,
            DateTime? dateFrom = null,
            DateTime? dateTo = null)
        {
            try
            {
                var employeesQuery = _context.Employees
                    .Include(e => e.Rank)
                    .Include(e => e.Province)
                    .Include(e => e.University)
                    .Include(e => e.Institute)
                    .AsQueryable();

                // تطبيق البحث النصي
                if (!string.IsNullOrEmpty(query))
                {
                    var searchTerms = query.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var term in searchTerms)
                    {
                        employeesQuery = employeesQuery.Where(e =>
                            e.FirstName.Contains(term) ||
                            e.FatherName.Contains(term) ||
                            e.GrandFatherName.Contains(term) ||
                            e.GreatGrandFatherName.Contains(term) ||
                            e.LastName.Contains(term) ||
                            e.EmployeeNumber.Contains(term) ||
                            e.StatisticalNumber.Contains(term) ||
                            (e.Rank != null && e.Rank.Name.Contains(term)) ||
                            (e.Province != null && e.Province.Name.Contains(term))
                        );
                    }
                }

                // تطبيق الفلاتر
                if (rank.HasValue)
                    employeesQuery = employeesQuery.Where(e => e.RankId == rank.Value);

                if (province.HasValue)
                    employeesQuery = employeesQuery.Where(e => e.ProvinceId == province.Value);

                if (!string.IsNullOrEmpty(education))
                    employeesQuery = employeesQuery.Where(e => e.EducationLevel.ToString() == education);

                if (!string.IsNullOrEmpty(gender))
                    employeesQuery = employeesQuery.Where(e => e.Gender.ToString() == gender);

                if (dateFrom.HasValue)
                    employeesQuery = employeesQuery.Where(e => e.CreatedDate >= dateFrom.Value);

                if (dateTo.HasValue)
                    employeesQuery = employeesQuery.Where(e => e.CreatedDate <= dateTo.Value.AddDays(1));

                // تطبيق الترتيب
                employeesQuery = sortField.ToLower() switch
                {
                    "fullname" => sortDirection == "asc"
                        ? employeesQuery.OrderBy(e => e.FirstName).ThenBy(e => e.FatherName)
                        : employeesQuery.OrderByDescending(e => e.FirstName).ThenByDescending(e => e.FatherName),
                    "employeenumber" => sortDirection == "asc"
                        ? employeesQuery.OrderBy(e => e.EmployeeNumber)
                        : employeesQuery.OrderByDescending(e => e.EmployeeNumber),
                    "createddate" => sortDirection == "asc"
                        ? employeesQuery.OrderBy(e => e.CreatedDate)
                        : employeesQuery.OrderByDescending(e => e.CreatedDate),
                    _ => employeesQuery.OrderByDescending(e => e.CreatedDate)
                };

                // حساب العدد الإجمالي
                var totalCount = await employeesQuery.CountAsync();

                // تطبيق الترقيم
                var employees = await employeesQuery
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(e => new
                    {
                        e.Id,
                        e.EmployeeNumber,
                        e.StatisticalNumber,
                        FullName = e.FullName,
                        Rank = e.Rank != null ? e.Rank.Name : "",
                        Province = e.Province != null ? e.Province.Name : "",
                        Education = TranslateEducationLevel(e.EducationLevel),
                        Gender = e.Gender == Gender.Male ? "ذكر" : "أنثى",
                        CreatedDate = e.CreatedDate.ToString("yyyy-MM-dd"),
                        HasPhoto = false
                    })
                    .ToListAsync();

                // إنشاء اقتراحات البحث
                var suggestions = new List<string>();
                if (!string.IsNullOrEmpty(query) && employees.Any())
                {
                    suggestions = employees
                        .SelectMany(e => new[] { e.FullName, e.Rank, e.Province })
                        .Where(s => s.Contains(query, StringComparison.OrdinalIgnoreCase))
                        .Distinct()
                        .Take(5)
                        .ToList();
                }

                var result = new
                {
                    Data = employees,
                    TotalCount = totalCount,
                    CurrentPage = page,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                    PageSize = pageSize,
                    Suggestions = suggestions
                };

                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(new { Error = "حدث خطأ أثناء البحث", Details = ex.Message });
            }
        }

        // ترجمة مستوى التعليم
        private string TranslateEducationLevel(EducationLevel level)
        {
            return level switch
            {
                EducationLevel.Primary => "ابتدائية",
                EducationLevel.Intermediate => "متوسطة",
                EducationLevel.Secondary => "إعدادية",
                EducationLevel.Diploma => "دبلوم",
                EducationLevel.Bachelor => "بكالوريوس",
                EducationLevel.Master => "ماجستير",
                EducationLevel.PhD => "دكتوراه",
                _ => level.ToString()
            };
        }

        // تصدير البيانات
        [HttpGet]
        public async Task<IActionResult> Export(string format = "excel")
        {
            try
            {
                var employees = await _context.Employees
                    .Include(e => e.Rank)
                    .Include(e => e.Province)
                    .Include(e => e.University)
                    .Include(e => e.Institute)
                    .OrderBy(e => e.EmployeeNumber)
                    .ToListAsync();

                byte[] fileBytes;
                string fileName;
                string contentType;

                switch (format.ToLower())
                {
                    case "excel":
                        fileBytes = await _exportImportService.ExportToExcelAsync(employees);
                        fileName = $"employees_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                        contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                        break;
                    case "csv":
                        fileBytes = await _exportImportService.ExportToCsvAsync(employees);
                        fileName = $"employees_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                        contentType = "text/csv";
                        break;
                    case "json":
                        fileBytes = await _exportImportService.ExportToJsonAsync(employees);
                        fileName = $"employees_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                        contentType = "application/json";
                        break;
                    case "pdf":
                        fileBytes = await _exportImportService.ExportToPdfAsync(employees);
                        fileName = $"employees_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
                        contentType = "application/pdf";
                        break;
                    default:
                        return BadRequest("صيغة غير مدعومة");
                }

                return File(fileBytes, contentType, fileName);
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"حدث خطأ أثناء التصدير: {ex.Message}";
                return RedirectToAction(nameof(Index));
            }
        }

        // تحميل قالب
        [HttpGet]
        public async Task<IActionResult> DownloadTemplate(string format = "excel")
        {
            try
            {
                var fileBytes = await _exportImportService.GenerateTemplateAsync(format);
                var fileName = $"employee_template.{format}";
                var contentType = format.ToLower() switch
                {
                    "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "csv" => "text/csv",
                    "json" => "application/json",
                    _ => "application/octet-stream"
                };

                return File(fileBytes, contentType, fileName);
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"حدث خطأ أثناء إنشاء القالب: {ex.Message}";
                return RedirectToAction(nameof(Index));
            }
        }

        // صفحة الاستيراد
        [HttpGet]
        public IActionResult Import()
        {
            return View();
        }

        // استيراد البيانات
        [HttpPost]
        public async Task<IActionResult> Import(IFormFile file, string format)
        {
            if (file == null || file.Length == 0)
            {
                TempData["Error"] = "يرجى اختيار ملف للاستيراد";
                return View();
            }

            try
            {
                ImportResult result = format.ToLower() switch
                {
                    "excel" => await _exportImportService.ImportFromExcelAsync(file),
                    "csv" => await _exportImportService.ImportFromCsvAsync(file),
                    "json" => await _exportImportService.ImportFromJsonAsync(file),
                    _ => throw new ArgumentException("صيغة غير مدعومة")
                };

                if (result.HasErrors)
                {
                    TempData["ImportResult"] = $"تم استيراد {result.SuccessCount} من أصل {result.TotalProcessed} سجل. الأخطاء: {string.Join(", ", result.Errors.Take(5))}";
                }
                else
                {
                    TempData["Success"] = $"تم استيراد {result.SuccessCount} موظف بنجاح";
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"حدث خطأ أثناء الاستيراد: {ex.Message}";
                return View();
            }
        }

        // طباعة بيانات موظف واحد
        public async Task<IActionResult> Print(int id)
        {
            var employee = await _context.Employees
                .Include(e => e.Rank)
                .Include(e => e.Province)
                .Include(e => e.University)
                .Include(e => e.Institute)
                .FirstOrDefaultAsync(e => e.Id == id);

            if (employee == null)
            {
                return NotFound();
            }

            return View(employee);
        }

        // تصدير بيانات موظف واحد
        public async Task<IActionResult> ExportSingle(int id)
        {
            var employee = await _context.Employees
                .Include(e => e.Rank)
                .Include(e => e.Province)
                .Include(e => e.University)
                .Include(e => e.Institute)
                .FirstOrDefaultAsync(e => e.Id == id);

            if (employee == null)
            {
                return NotFound();
            }

            try
            {
                var employees = new List<Employee> { employee };
                var fileBytes = await _exportImportService.ExportToExcelAsync(employees);
                var fileName = $"employee_{employee.EmployeeNumber}_{DateTime.Now:yyyyMMdd}.xlsx";
                var contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

                return File(fileBytes, contentType, fileName);
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"حدث خطأ أثناء التصدير: {ex.Message}";
                return RedirectToAction(nameof(Details), new { id });
            }
        }



        // API endpoint لتحميل الكليات حسب الجامعة
        [HttpGet]
        public async Task<IActionResult> GetColleges(int universityId)
        {
            var colleges = await _context.Colleges
                .Where(c => c.UniversityId == universityId && c.IsActive)
                .Select(c => new { id = c.Id, name = c.Name })
                .ToListAsync();

            return Json(colleges);
        }

        [HttpGet]
        public async Task<IActionResult> GetAllColleges()
        {
            var colleges = await _context.Colleges
                .Where(c => c.IsActive)
                .Include(c => c.University)
                .Select(c => new { id = c.Id, name = c.Name, universityName = c.University != null ? c.University.Name : "" })
                .ToListAsync();

            return Json(colleges);
        }

        [HttpGet]
        public async Task<IActionResult> GetAllDepartments()
        {
            var departments = await _context.Departments
                .Where(d => d.IsActive)
                .Include(d => d.Institute)
                .Select(d => new { id = d.Id, name = d.Name, instituteName = d.Institute.Name })
                .ToListAsync();

            return Json(departments);
        }

        // معالجة صورة الموظف
        private async Task ProcessEmployeePhoto(Employee employee, EmployeeCreateViewModel viewModel)
        {
            try
            {
                // إذا تم رفع صورة من ملف
                if (viewModel.PhotoFile != null && viewModel.PhotoFile.Length > 0)
                {
                    // التحقق من نوع الملف
                    var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif" };
                    if (!allowedTypes.Contains(viewModel.PhotoFile.ContentType.ToLower()))
                    {
                        throw new InvalidOperationException("نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPEG, PNG أو GIF");
                    }

                    // التحقق من حجم الملف (5MB كحد أقصى)
                    if (viewModel.PhotoFile.Length > 5 * 1024 * 1024)
                    {
                        throw new InvalidOperationException("حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت");
                    }

                    using (var memoryStream = new MemoryStream())
                    {
                        await viewModel.PhotoFile.CopyToAsync(memoryStream);
                        employee.Photo = memoryStream.ToArray();
                        employee.PhotoContentType = viewModel.PhotoFile.ContentType;
                        employee.PhotoFileName = viewModel.PhotoFile.FileName;
                        employee.PhotoUploadDate = DateTime.Now;
                    }
                }
                // إذا تم التقاط صورة من الكاميرا (Base64)
                else if (!string.IsNullOrEmpty(viewModel.PhotoBase64))
                {
                    try
                    {
                        employee.Photo = Convert.FromBase64String(viewModel.PhotoBase64);
                        employee.PhotoContentType = viewModel.PhotoContentType ?? "image/jpeg";
                        employee.PhotoFileName = viewModel.PhotoFileName ?? "camera_capture.jpg";
                        employee.PhotoUploadDate = DateTime.Now;
                    }
                    catch (FormatException)
                    {
                        throw new InvalidOperationException("بيانات الصورة غير صحيحة");
                    }
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                var logger = HttpContext.RequestServices.GetService<ILogger<EmployeeController>>();
                logger?.LogError(ex, "خطأ في معالجة صورة الموظف");

                // يمكن إضافة رسالة خطأ للمستخدم هنا إذا لزم الأمر
                throw;
            }
        }

        // API لعرض صورة الموظف
        [HttpGet]
        public async Task<IActionResult> GetEmployeePhoto(int id)
        {
            var employee = await _context.Employees.FindAsync(id);

            if (employee?.Photo == null || employee.Photo.Length == 0)
            {
                // إرجاع صورة افتراضية
                var defaultImagePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "images", "default-avatar.png");
                if (System.IO.File.Exists(defaultImagePath))
                {
                    var defaultImage = await System.IO.File.ReadAllBytesAsync(defaultImagePath);
                    return File(defaultImage, "image/png");
                }

                return NotFound();
            }

            return File(employee.Photo, employee.PhotoContentType ?? "image/jpeg");
        }

        // API: Get Colleges by University
        [HttpGet]
        public async Task<IActionResult> GetCollegesByUniversity(int universityId)
        {
            try
            {
                var colleges = await _context.Colleges
                    .Where(c => c.UniversityId == universityId)
                    .OrderBy(c => c.Name)
                    .Select(c => new { value = c.Id, text = c.Name })
                    .ToListAsync();

                return Json(colleges);
            }
            catch (Exception ex)
            {
                return Json(new { error = "فشل في تحميل الكليات: " + ex.Message });
            }
        }

        // API: Get Departments by Institute
        [HttpGet]
        public async Task<IActionResult> GetDepartmentsByInstitute(int instituteId)
        {
            try
            {
                var departments = await _context.Departments
                    .Where(d => d.InstituteId == instituteId)
                    .OrderBy(d => d.Name)
                    .Select(d => new { value = d.Id, text = d.Name })
                    .ToListAsync();

                return Json(departments);
            }
            catch (Exception ex)
            {
                return Json(new { error = "فشل في تحميل الأقسام: " + ex.Message });
            }
        }
    }
}
