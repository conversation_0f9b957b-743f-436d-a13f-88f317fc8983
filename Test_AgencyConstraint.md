# اختبار قيد منع تكرار الوكالات في نفس الوزارة

## الهدف
التأكد من أن القيد الجديد يعمل بشكل صحيح:
- ✅ **يُسمح**: وكالات بنفس الاسم في وزارات مختلفة
- ❌ **يُمنع**: وكالات بنفس الاسم في نفس الوزارة

## القيد المطبق
```sql
-- فهرس فريد مركب على (Name, MinistryId)
CREATE UNIQUE INDEX IX_Agencies_Name_MinistryId ON Agencies (Name, MinistryId)
```

## سيناريوهات الاختبار

### ✅ السيناريو الأول: وكالات بنفس الاسم في وزارات مختلفة (مسموح)

**الخطوات:**
1. إنشاء وزارة الداخلية
2. إنشاء وزارة الدفاع
3. إنشاء "وكالة الشؤون الإدارية" تحت وزارة الداخلية
4. إنشاء "وكالة الشؤون الإدارية" تحت وزارة الدفاع

**النتيجة المتوقعة:** ✅ نجح الإنشاء

### ❌ السيناريو الثاني: وكالات بنفس الاسم في نفس الوزارة (ممنوع)

**الخطوات:**
1. إنشاء "وكالة الشؤون المالية" تحت وزارة الداخلية
2. محاولة إنشاء "وكالة الشؤون المالية" أخرى تحت نفس الوزارة

**النتيجة المتوقعة:** ❌ خطأ في قاعدة البيانات

## كيفية الاختبار

### من خلال واجهة التطبيق:
1. اذهب إلى: `http://localhost:5137/Constants/Ministries`
2. أنشئ وزارتين مختلفتين
3. اذهب إلى: `http://localhost:5137/Constants/Agencies`
4. جرب إنشاء الوكالات حسب السيناريوهات أعلاه

### من خلال SQL مباشرة:
```sql
-- إنشاء وزارتين
INSERT INTO Ministries (Name, Code, Description, EstablishedYear, Location, IsActive, CreatedDate) 
VALUES 
('وزارة الداخلية', 'MOI', 'وزارة الداخلية', 2003, 'بغداد', 1, GETDATE()),
('وزارة الدفاع', 'MOD', 'وزارة الدفاع', 2003, 'بغداد', 1, GETDATE());

-- اختبار السيناريو الأول (مسموح)
INSERT INTO Agencies (Name, Code, Description, EstablishedYear, IsActive, MinistryId, CreatedDate)
VALUES 
('وكالة الشؤون الإدارية', 'ADM1', 'وكالة تحت وزارة الداخلية', 2003, 1, 1, GETDATE()),
('وكالة الشؤون الإدارية', 'ADM2', 'وكالة تحت وزارة الدفاع', 2003, 1, 2, GETDATE());

-- اختبار السيناريو الثاني (ممنوع)
INSERT INTO Agencies (Name, Code, Description, EstablishedYear, IsActive, MinistryId, CreatedDate)
VALUES ('وكالة الشؤون الإدارية', 'ADM3', 'وكالة مكررة', 2003, 1, 1, GETDATE());
-- هذا الأمر سيفشل مع خطأ: Cannot insert duplicate key
```

## رسائل الخطأ المتوقعة

عند محاولة إنشاء وكالة مكررة في نفس الوزارة:

### في قاعدة البيانات:
```
Cannot insert duplicate key row in object 'dbo.Agencies' 
with unique index 'IX_Agencies_Name_MinistryId'. 
The duplicate key value is (اسم الوكالة, معرف الوزارة).
```

### في التطبيق:
```
An error occurred while saving the entity changes.
DbUpdateException: Cannot insert duplicate key...
```

## التحقق من القيد

```sql
-- عرض جميع الفهارس الفريدة على جدول Agencies
SELECT name, is_unique, type_desc 
FROM sys.indexes 
WHERE object_id = OBJECT_ID('Agencies') AND is_unique = 1;

-- عرض الوكالات مع وزاراتها
SELECT m.Name as Ministry, a.Name as Agency, a.Code
FROM Ministries m 
LEFT JOIN Agencies a ON m.Id = a.MinistryId
ORDER BY m.Name, a.Name;
```

## الفوائد من هذا القيد

1. **منع الأخطاء**: يمنع إنشاء وكالات مكررة بالخطأ في نفس الوزارة
2. **مرونة التنظيم**: يسمح بوجود وكالات بنفس الاسم في وزارات مختلفة
3. **سلامة البيانات**: يضمن التفرد المطلوب على مستوى الوزارة
4. **سهولة الإدارة**: يقلل من التعقيد في إدارة الهيكل التنظيمي

## ملاحظات مهمة

- ⚠️ **حساسية الأحرف**: القيد حساس لحالة الأحرف (Case Sensitive)
- 🔄 **التطبيق على المستويات الأخرى**: نفس المبدأ مطبق على:
  - المديريات (فريدة داخل نفس الوكالة)
  - الأقسام (فريدة داخل نفس المديرية)
  - الشعب (فريدة داخل نفس القسم)
- 📊 **الأداء**: الفهرس الفريد يحسن أداء البحث والاستعلامات
