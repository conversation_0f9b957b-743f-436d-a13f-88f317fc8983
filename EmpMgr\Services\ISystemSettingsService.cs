using EmpMgr.Models;

namespace EmpMgr.Services
{
    public interface ISystemSettingsService
    {
        Task<SystemSettings> GetSettingsAsync();
        Task<SystemSettings> UpdateSettingsAsync(SystemSettings settings, string updatedBy);
        Task<string> UploadLogoAsync(IFormFile logoFile);
        Task<string> UploadFaviconAsync(IFormFile faviconFile);
        Task<bool> DeleteFileAsync(string filePath);
        Task InitializeDefaultSettingsAsync();
    }
}
