using EmpMgr.Data;
using EmpMgr.Models;
using Microsoft.EntityFrameworkCore;

namespace EmpMgr.Services
{
    public class PromotionCalculationService : IPromotionCalculationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<PromotionCalculationService> _logger;

        public PromotionCalculationService(ApplicationDbContext context, ILogger<PromotionCalculationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<DateTime?> CalculateNextPromotionDateAsync(Employee employee)
        {
            try
            {
                if (employee.CurrentRank == null)
                {
                    _logger.LogWarning("لا يمكن حساب تاريخ الترقية للموظف {EmployeeId} - لا توجد رتبة حالية", employee.Id);
                    return null;
                }

                var rank = await _context.Ranks.FindAsync(employee.CurrentRankId);
                if (rank == null)
                {
                    _logger.LogWarning("لا يمكن العثور على الرتبة {RankId} للموظف {EmployeeId}", employee.CurrentRankId, employee.Id);
                    return null;
                }

                return CalculatePromotionDate(
                    employee.LastPromotionDate ?? employee.AppointmentDate,
                    rank,
                    employee.AppointmentDate,
                    employee.DateOfBirth
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب تاريخ الترقية للموظف {EmployeeId}", employee.Id);
                return null;
            }
        }

        public DateTime? CalculatePromotionDate(DateTime lastPromotionDate, Rank currentRank, DateTime serviceStartDate, DateTime? birthDate = null)
        {
            try
            {
                // حساب تاريخ الاستحقاق بناءً على مدة الرتبة
                var eligibilityDate = lastPromotionDate.AddYears(currentRank.RankDurationYears);

                // التحقق من قيود العمر إذا كانت محددة
                if (birthDate.HasValue)
                {
                    var ageAtEligibility = eligibilityDate.Year - birthDate.Value.Year;
                    if (eligibilityDate < birthDate.Value.AddYears(ageAtEligibility))
                        ageAtEligibility--;

                    // التحقق من الحد الأدنى للعمر
                    if (currentRank.MinAgeForPromotion.HasValue && ageAtEligibility < currentRank.MinAgeForPromotion.Value)
                    {
                        eligibilityDate = birthDate.Value.AddYears(currentRank.MinAgeForPromotion.Value);
                    }

                    // التحقق من الحد الأقصى للعمر
                    if (currentRank.MaxAgeForPromotion.HasValue && ageAtEligibility > currentRank.MaxAgeForPromotion.Value)
                    {
                        return null; // غير مؤهل للترقية بسبب تجاوز الحد الأقصى للعمر
                    }
                }

                return eligibilityDate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب تاريخ الترقية");
                return null;
            }
        }

        public async Task<PromotionEligibilityResult> CheckPromotionEligibilityAsync(Employee employee)
        {
            var result = new PromotionEligibilityResult();

            try
            {
                var nextPromotionDate = await CalculateNextPromotionDateAsync(employee);
                result.NextPromotionDate = nextPromotionDate;

                if (nextPromotionDate == null)
                {
                    result.IsEligible = false;
                    result.Status = "غير مؤهل للترقية";
                    result.MissingRequirements.Add("لا توجد رتبة محددة أو تجاوز الحد الأقصى للعمر");
                    return result;
                }

                var today = DateTime.Today;
                result.DaysUntilEligible = (nextPromotionDate.Value.Date - today).Days;

                if (result.DaysUntilEligible <= 0)
                {
                    result.IsEligible = true;
                    result.Status = "مؤهل للترقية";
                }
                else
                {
                    result.IsEligible = false;
                    result.Status = $"مؤهل للترقية بعد {result.DaysUntilEligible} يوم";
                }

                // إضافة المتطلبات
                var rank = await _context.Ranks.FindAsync(employee.CurrentRankId);
                if (rank != null)
                {
                    result.Requirements.Add($"مدة الرتبة: {rank.RankDurationYears} سنوات");

                    if (rank.RequiresTrainingCourses)
                    {
                        result.Requirements.Add($"دورات تدريبية: {rank.RequiredCoursesCount} دورة");
                        // يمكن إضافة فحص فعلي للدورات هنا
                    }

                    if (rank.RequiresPerformanceEvaluation && rank.MinPerformanceScore.HasValue)
                    {
                        result.Requirements.Add($"تقييم الأداء: {rank.MinPerformanceScore}% كحد أدنى");
                        // يمكن إضافة فحص فعلي لتقييم الأداء هنا
                    }

                    if (rank.MinAgeForPromotion.HasValue)
                    {
                        result.Requirements.Add($"الحد الأدنى للعمر: {rank.MinAgeForPromotion} سنة");
                    }

                    if (rank.MaxAgeForPromotion.HasValue)
                    {
                        result.Requirements.Add($"الحد الأقصى للعمر: {rank.MaxAgeForPromotion} سنة");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص أهلية الترقية للموظف {EmployeeId}", employee.Id);
                result.IsEligible = false;
                result.Status = "خطأ في الحساب";
                return result;
            }
        }

        public async Task<int?> CalculateDaysUntilPromotionAsync(Employee employee)
        {
            var nextPromotionDate = await CalculateNextPromotionDateAsync(employee);
            if (nextPromotionDate == null) return null;

            var today = DateTime.Today;
            var days = (nextPromotionDate.Value.Date - today).Days;
            return days > 0 ? days : 0;
        }

        public async Task<List<Employee>> GetEligibleEmployeesForPromotionAsync()
        {
            try
            {
                var employees = await _context.Employees
                    .Include(e => e.CurrentRank)
                    .Where(e => e.CurrentRankId != null)
                    .ToListAsync();

                var eligibleEmployees = new List<Employee>();

                foreach (var employee in employees)
                {
                    var nextPromotionDate = await CalculateNextPromotionDateAsync(employee);
                    if (nextPromotionDate.HasValue && nextPromotionDate.Value.Date <= DateTime.Today)
                    {
                        eligibleEmployees.Add(employee);
                    }
                }

                return eligibleEmployees;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على قائمة الموظفين المؤهلين للترقية");
                return new List<Employee>();
            }
        }

        public async Task<PromotionStatistics> GetPromotionStatisticsAsync()
        {
            try
            {
                var statistics = new PromotionStatistics();
                var today = DateTime.Today;
                var startOfYear = new DateTime(today.Year, 1, 1);
                var startOfMonth = new DateTime(today.Year, today.Month, 1);

                // إجمالي الموظفين
                statistics.TotalEmployees = await _context.Employees.CountAsync();

                // الموظفين المؤهلين للترقية
                var eligibleEmployees = await GetEligibleEmployeesForPromotionAsync();
                statistics.EligibleForPromotion = eligibleEmployees.Count;

                // الترقيات هذا العام والشهر (يحتاج إلى جدول ترقيات منفصل)
                // statistics.PromotedThisYear = await _context.Promotions.CountAsync(p => p.PromotionDate >= startOfYear);
                // statistics.PromotedThisMonth = await _context.Promotions.CountAsync(p => p.PromotionDate >= startOfMonth);

                // إحصائيات حسب الرتبة
                var rankStats = await _context.Employees
                    .Include(e => e.CurrentRank)
                    .Where(e => e.CurrentRankId != null)
                    .GroupBy(e => e.CurrentRank!.Name)
                    .Select(g => new { RankName = g.Key, Count = g.Count() })
                    .ToListAsync();

                foreach (var stat in rankStats)
                {
                    statistics.EligibleByRank[stat.RankName] = 0; // سيتم تحديثها لاحقاً
                }

                // حساب المؤهلين حسب الرتبة
                foreach (var employee in eligibleEmployees)
                {
                    if (employee.CurrentRank != null)
                    {
                        var rankName = employee.CurrentRank.Name;
                        if (statistics.EligibleByRank.ContainsKey(rankName))
                        {
                            statistics.EligibleByRank[rankName]++;
                        }
                        else
                        {
                            statistics.EligibleByRank[rankName] = 1;
                        }
                    }
                }

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إحصائيات الترقيات");
                return new PromotionStatistics();
            }
        }
    }
}
