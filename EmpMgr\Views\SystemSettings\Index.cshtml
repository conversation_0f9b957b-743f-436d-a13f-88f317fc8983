@model EmpMgr.Models.SystemSettings
@{
    ViewData["Title"] = "إعدادات النظام";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-cogs text-primary me-3"></i>
                            إعدادات النظام
                        </h1>
                        <p class="page-subtitle">إدارة إعدادات النظام العامة والمظهر</p>
                    </div>
                    <div class="page-actions">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetToDefaults()">
                            <i class="fas fa-undo me-2"></i>
                            إعادة تعيين افتراضي
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <form asp-action="Update" method="post" enctype="multipart/form-data" class="settings-form">
        @Html.AntiForgeryToken()
        
        <div class="row">
            <!-- القسم الأول: المعلومات الأساسية -->
            <div class="col-lg-8">
                <div class="settings-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-info-circle text-primary me-2"></i>
                            المعلومات الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="SystemName" class="form-label">
                                        <i class="fas fa-desktop me-2"></i>
                                        @Html.DisplayNameFor(m => m.SystemName)
                                    </label>
                                    <input asp-for="SystemName" class="form-control" />
                                    <span asp-validation-for="SystemName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="OrganizationName" class="form-label">
                                        <i class="fas fa-building me-2"></i>
                                        @Html.DisplayNameFor(m => m.OrganizationName)
                                    </label>
                                    <input asp-for="OrganizationName" class="form-control" />
                                    <span asp-validation-for="OrganizationName" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="SystemVersion" class="form-label">
                                        <i class="fas fa-code-branch me-2"></i>
                                        @Html.DisplayNameFor(m => m.SystemVersion)
                                    </label>
                                    <input asp-for="SystemVersion" class="form-control" />
                                    <span asp-validation-for="SystemVersion" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="DefaultLanguage" class="form-label">
                                        <i class="fas fa-language me-2"></i>
                                        @Html.DisplayNameFor(m => m.DefaultLanguage)
                                    </label>
                                    <select asp-for="DefaultLanguage" class="form-select">
                                        <option value="ar">العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                    <span asp-validation-for="DefaultLanguage" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="SystemDescription" class="form-label">
                                <i class="fas fa-align-left me-2"></i>
                                @Html.DisplayNameFor(m => m.SystemDescription)
                            </label>
                            <textarea asp-for="SystemDescription" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="SystemDescription" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <!-- قسم الألوان -->
                <div class="settings-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-palette text-warning me-2"></i>
                            ألوان النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="PrimaryColor" class="form-label">
                                        <i class="fas fa-circle text-primary me-2"></i>
                                        @Html.DisplayNameFor(m => m.PrimaryColor)
                                    </label>
                                    <div class="color-input-group">
                                        <input asp-for="PrimaryColor" type="color" class="form-control color-picker" />
                                        <input asp-for="PrimaryColor" type="text" class="form-control color-text" />
                                    </div>
                                    <span asp-validation-for="PrimaryColor" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="SecondaryColor" class="form-label">
                                        <i class="fas fa-circle text-secondary me-2"></i>
                                        @Html.DisplayNameFor(m => m.SecondaryColor)
                                    </label>
                                    <div class="color-input-group">
                                        <input asp-for="SecondaryColor" type="color" class="form-control color-picker" />
                                        <input asp-for="SecondaryColor" type="text" class="form-control color-text" />
                                    </div>
                                    <span asp-validation-for="SecondaryColor" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- القسم الثاني: الشعار والأيقونة -->
            <div class="col-lg-4">
                <div class="settings-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-image text-success me-2"></i>
                            الشعار والأيقونة
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- الشعار الحالي -->
                        <div class="current-logo-section">
                            <label class="form-label">الشعار الحالي</label>
                            <div class="logo-preview">
                                <img src="@(Model.LogoPath ?? "/images/moi-logo.png")" alt="شعار النظام" class="current-logo" id="currentLogo" />
                            </div>
                        </div>

                        <!-- رفع شعار جديد -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-upload me-2"></i>
                                رفع شعار جديد
                            </label>
                            <input type="file" name="logoFile" class="form-control" accept=".png,.jpg,.jpeg,.gif,.svg" onchange="previewLogo(this)" />
                            <small class="form-text text-muted">
                                الأنواع المدعومة: PNG, JPG, JPEG, GIF, SVG<br>
                                الحد الأقصى: 5 ميجابايت
                            </small>
                        </div>

                        <!-- أزرار إدارة الشعار -->
                        <div class="logo-actions">
                            <button type="button" class="btn btn-outline-warning btn-sm" onclick="resetLogo()">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين
                            </button>
                        </div>

                        <hr>

                        <!-- الأيقونة -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-star me-2"></i>
                                رفع أيقونة جديدة (Favicon)
                            </label>
                            <input type="file" name="faviconFile" class="form-control" accept=".ico,.png,.jpg,.jpeg" />
                            <small class="form-text text-muted">
                                الأنواع المدعومة: ICO, PNG, JPG, JPEG<br>
                                الحد الأقصى: 1 ميجابايت
                            </small>
                        </div>

                        <div class="favicon-actions">
                            <button type="button" class="btn btn-outline-warning btn-sm" onclick="resetFavicon()">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين الأيقونة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم معلومات المطور -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="settings-card developer-info">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-code text-info me-2"></i>
                            معلومات المطور
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="developer-details">
                                    <h6 class="developer-name">
                                        <i class="fas fa-user-tie text-primary me-2"></i>
                                        العقيد المهندس حيدر چياد ثويني
                                    </h6>
                                    <p class="developer-title text-muted mb-2">
                                        <i class="fas fa-laptop-code me-2"></i>
                                        مطور ومصمم النظام
                                    </p>
                                    <div class="developer-contact">
                                        <span class="badge bg-primary me-2">
                                            <i class="fas fa-envelope me-1"></i>
                                            مطور أنظمة إدارية
                                        </span>
                                        <span class="badge bg-success">
                                            <i class="fas fa-shield-alt me-1"></i>
                                            خبير أمن معلومات
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="copyright-info">
                                    <div class="copyright-text">
                                        <i class="fas fa-copyright text-warning me-2"></i>
                                        <strong>جميع الحقوق محفوظة</strong>
                                    </div>
                                    <div class="year-text text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        @DateTime.Now.Year
                                    </div>
                                    <div class="version-info mt-2">
                                        <span class="badge bg-dark">
                                            <i class="fas fa-tag me-1"></i>
                                            الإصدار @(Model?.SystemVersion ?? "1.0.0")
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-3">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="tech-info">
                                    <h6 class="text-primary">
                                        <i class="fas fa-cogs me-2"></i>
                                        التقنيات المستخدمة
                                    </h6>
                                    <div class="tech-badges">
                                        <span class="badge bg-info me-1 mb-1">ASP.NET Core</span>
                                        <span class="badge bg-info me-1 mb-1">Entity Framework</span>
                                        <span class="badge bg-info me-1 mb-1">SQL Server</span>
                                        <span class="badge bg-info me-1 mb-1">Bootstrap 5</span>
                                        <span class="badge bg-info me-1 mb-1">JavaScript</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="features-info">
                                    <h6 class="text-success">
                                        <i class="fas fa-star me-2"></i>
                                        مميزات النظام
                                    </h6>
                                    <ul class="feature-list">
                                        <li><i class="fas fa-check text-success me-1"></i> إدارة شاملة للموظفين</li>
                                        <li><i class="fas fa-check text-success me-1"></i> نظام ترقيات متقدم</li>
                                        <li><i class="fas fa-check text-success me-1"></i> تقارير تفاعلية</li>
                                        <li><i class="fas fa-check text-success me-1"></i> واجهة عربية متجاوبة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="form-actions">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save me-2"></i>
                حفظ الإعدادات
            </button>
            <a href="@Url.Action("Index", "Home")" class="btn btn-outline-secondary btn-lg">
                <i class="fas fa-times me-2"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>

<style>
    .page-header {
        background: linear-gradient(135deg, #f8f9fa, #ffffff);
        border-radius: 16px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }

    .page-title {
        font-size: 32px;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
    }

    .page-subtitle {
        color: #7f8c8d;
        font-size: 16px;
        margin: 8px 0 0 0;
    }

    .settings-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        margin-bottom: 30px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .settings-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    }

    .settings-card .card-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 20px 30px;
        border: none;
    }

    .settings-card .card-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
    }

    .settings-card .card-body {
        padding: 30px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .form-control, .form-select {
        border-radius: 12px;
        border: 2px solid #e9ecef;
        padding: 12px 16px;
        font-size: 16px;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
    }

    .color-input-group {
        display: flex;
        gap: 10px;
    }

    .color-picker {
        width: 60px;
        height: 50px;
        padding: 4px;
        border-radius: 12px;
    }

    .color-text {
        flex: 1;
    }

    .current-logo-section {
        margin-bottom: 25px;
    }

    .logo-preview {
        background: linear-gradient(135deg, #f8f9fa, #ffffff);
        border: 2px dashed #dee2e6;
        border-radius: 16px;
        padding: 20px;
        text-align: center;
        margin-top: 10px;
    }

    .current-logo {
        max-width: 120px;
        max-height: 120px;
        object-fit: contain;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .logo-actions, .favicon-actions {
        margin-top: 15px;
    }

    .form-actions {
        background: linear-gradient(135deg, #f8f9fa, #ffffff);
        border-radius: 16px;
        padding: 30px;
        margin-top: 30px;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }

    .btn {
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
    }

    .btn-lg {
        padding: 16px 32px;
        font-size: 18px;
    }

    .alert {
        border-radius: 16px;
        border: none;
        padding: 20px;
        margin-bottom: 30px;
    }

    .alert-success {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
        color: #155724;
    }

    .alert-danger {
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
        color: #721c24;
    }

    /* تصميم قسم معلومات المطور */
    .developer-info {
        background: linear-gradient(135deg, #f8f9fa, #ffffff);
        border: 1px solid #e9ecef;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .developer-info .card-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-bottom: none;
        padding: 1rem 1.5rem;
    }

    .developer-name {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .developer-title {
        font-size: 0.95rem;
        font-style: italic;
    }

    .developer-contact .badge {
        font-size: 0.8rem;
        padding: 0.5rem 0.8rem;
    }

    .copyright-info {
        text-align: center;
        padding: 1rem;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        border-radius: 10px;
    }

    .copyright-text {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .year-text {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .version-info .badge {
        font-size: 0.8rem;
        padding: 0.5rem 0.8rem;
    }

    .tech-badges .badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.6rem;
    }

    .feature-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .feature-list li {
        padding: 0.3rem 0;
        font-size: 0.9rem;
    }

    .tech-info, .features-info {
        padding: 1rem;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    /* تأثيرات تفاعلية */
    .developer-info:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
    }

    .tech-badges .badge:hover {
        transform: scale(1.05);
        transition: transform 0.2s ease;
    }
</style>

<script>
    function previewLogo(input) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('currentLogo').src = e.target.result;
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    function resetLogo() {
        if (confirm('هل أنت متأكد من إعادة تعيين الشعار الافتراضي؟')) {
            fetch('@Url.Action("ResetLogo")', {
                method: 'POST',
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                }
            }).then(() => {
                location.reload();
            });
        }
    }

    function resetFavicon() {
        if (confirm('هل أنت متأكد من إعادة تعيين الأيقونة الافتراضية؟')) {
            fetch('@Url.Action("ResetFavicon")', {
                method: 'POST',
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                }
            }).then(() => {
                location.reload();
            });
        }
    }

    function resetToDefaults() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
            // إعادة تعيين القيم الافتراضية
            document.querySelector('input[name="SystemName"]').value = 'نظام إدارة الضباط والمنتسبين';
            document.querySelector('input[name="OrganizationName"]').value = 'وزارة الداخلية - جمهورية العراق';
            document.querySelector('input[name="PrimaryColor"]').value = '#667eea';
            document.querySelector('input[name="SecondaryColor"]').value = '#764ba2';
            document.querySelector('input[name="SystemVersion"]').value = '1.0.0';
            document.querySelector('select[name="DefaultLanguage"]').value = 'ar';
            document.querySelector('textarea[name="SystemDescription"]').value = 'نظام شامل لإدارة بيانات الضباط والمنتسبين';
        }
    }

    // ربط منتقي الألوان مع حقل النص
    document.addEventListener('DOMContentLoaded', function() {
        const colorPickers = document.querySelectorAll('.color-picker');
        colorPickers.forEach(picker => {
            const textInput = picker.nextElementSibling;

            picker.addEventListener('change', function() {
                textInput.value = this.value;
            });

            textInput.addEventListener('input', function() {
                if (/^#[0-9A-F]{6}$/i.test(this.value)) {
                    picker.value = this.value;
                }
            });
        });
    });
</script>
