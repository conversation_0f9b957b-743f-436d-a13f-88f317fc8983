class PerformanceOptimizer {
    constructor() {
        this.cache = new Map();
        this.imageCache = new Map();
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.observers = new Map();
        this.preloadedImages = new Set();
    }

    // تحسين تحميل الصور مع Lazy Loading
    initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // مراقبة جميع الصور
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });

            this.observers.set('images', imageObserver);
        } else {
            // Fallback للمتصفحات القديمة
            document.querySelectorAll('img[data-src]').forEach(img => {
                this.loadImage(img);
            });
        }
    }

    // تحميل الصورة مع تحسينات
    async loadImage(img) {
        const src = img.dataset.src;
        if (!src || this.preloadedImages.has(src)) return;

        try {
            // إظهار placeholder أثناء التحميل
            img.style.filter = 'blur(5px)';
            img.style.transition = 'filter 0.3s ease';

            // تحميل الصورة
            const imageBlob = await this.fetchWithCache(src, 'image');
            const imageUrl = URL.createObjectURL(imageBlob);
            
            img.src = imageUrl;
            img.removeAttribute('data-src');
            
            // إزالة التأثير بعد التحميل
            img.onload = () => {
                img.style.filter = 'none';
                this.preloadedImages.add(src);
            };

        } catch (error) {
            console.error('خطأ في تحميل الصورة:', error);
            // إخفاء الصورة وإظهار أيقونة افتراضية
            img.style.display = 'none';
            const placeholder = document.createElement('div');
            placeholder.className = 'bg-light rounded-circle d-flex align-items-center justify-content-center';
            placeholder.style.cssText = img.style.cssText;
            placeholder.innerHTML = '<i class="fas fa-user text-muted"></i>';
            img.parentNode.replaceChild(placeholder, img);
        }
    }

    // نظام Cache متقدم
    async fetchWithCache(url, type = 'json', maxAge = 300000) { // 5 دقائق افتراضي
        const cacheKey = `${type}_${url}`;
        const cached = this.cache.get(cacheKey);

        // التحقق من صحة Cache
        if (cached && (Date.now() - cached.timestamp) < maxAge) {
            return cached.data;
        }

        try {
            const response = await fetch(url);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);

            let data;
            if (type === 'json') {
                data = await response.json();
            } else if (type === 'image') {
                data = await response.blob();
            } else {
                data = await response.text();
            }

            // حفظ في Cache
            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });

            return data;
        } catch (error) {
            console.error('خطأ في جلب البيانات:', error);
            throw error;
        }
    }

    // تحسين طلبات AJAX مع Queue
    async queueRequest(requestFn, priority = 'normal') {
        return new Promise((resolve, reject) => {
            const request = {
                fn: requestFn,
                resolve,
                reject,
                priority,
                timestamp: Date.now()
            };

            // إضافة للقائمة حسب الأولوية
            if (priority === 'high') {
                this.requestQueue.unshift(request);
            } else {
                this.requestQueue.push(request);
            }

            this.processQueue();
        });
    }

    // معالجة قائمة الطلبات
    async processQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) return;

        this.isProcessingQueue = true;

        while (this.requestQueue.length > 0) {
            const request = this.requestQueue.shift();
            
            try {
                const result = await request.fn();
                request.resolve(result);
            } catch (error) {
                request.reject(error);
            }

            // تأخير قصير لتجنب إرهاق الخادم
            await this.delay(50);
        }

        this.isProcessingQueue = false;
    }

    // تحسين تحميل البيانات مع Pagination
    async loadDataWithPagination(url, page = 1, pageSize = 20) {
        const cacheKey = `pagination_${url}_${page}_${pageSize}`;
        const cached = this.cache.get(cacheKey);

        if (cached && (Date.now() - cached.timestamp) < 60000) { // دقيقة واحدة
            return cached.data;
        }

        try {
            const response = await fetch(`${url}?page=${page}&pageSize=${pageSize}`);
            const data = await response.json();

            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });

            return data;
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            throw error;
        }
    }

    // تحسين النماذج مع Debouncing
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // تحسين الذاكرة
    optimizeMemory() {
        // تنظيف Cache القديم
        const now = Date.now();
        const maxAge = 600000; // 10 دقائق

        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > maxAge) {
                this.cache.delete(key);
            }
        }

        // تنظيف URLs المؤقتة
        this.preloadedImages.forEach(url => {
            if (url.startsWith('blob:')) {
                URL.revokeObjectURL(url);
            }
        });

        // تشغيل garbage collection إذا كان متاحاً
        if (window.gc) {
            window.gc();
        }
    }

    // تحسين الشبكة
    async preloadCriticalResources() {
        const criticalUrls = [
            '/css/site.css',
            '/js/camera.js',
            '/js/photo-manager.js',
            '/images/default-avatar.png'
        ];

        const preloadPromises = criticalUrls.map(url => {
            return this.preloadResource(url);
        });

        try {
            await Promise.all(preloadPromises);
            console.log('تم تحميل الموارد الأساسية مسبقاً');
        } catch (error) {
            console.warn('خطأ في التحميل المسبق:', error);
        }
    }

    // تحميل مورد مسبقاً
    preloadResource(url) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'preload';
            
            if (url.endsWith('.css')) {
                link.as = 'style';
            } else if (url.endsWith('.js')) {
                link.as = 'script';
            } else if (url.match(/\.(jpg|jpeg|png|gif|webp)$/)) {
                link.as = 'image';
            }
            
            link.href = url;
            link.onload = resolve;
            link.onerror = reject;
            
            document.head.appendChild(link);
        });
    }

    // مراقبة الأداء
    startPerformanceMonitoring() {
        // مراقبة استهلاك الذاكرة
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const memoryUsage = {
                    used: Math.round(memory.usedJSHeapSize / 1048576), // MB
                    total: Math.round(memory.totalJSHeapSize / 1048576), // MB
                    limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
                };

                // تحذير إذا تجاوز الاستهلاك 80%
                if (memoryUsage.used / memoryUsage.limit > 0.8) {
                    console.warn('استهلاك ذاكرة عالي:', memoryUsage);
                    this.optimizeMemory();
                }
            }, 30000); // كل 30 ثانية
        }

        // مراقبة سرعة الشبكة
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            const updateNetworkStatus = () => {
                const networkInfo = {
                    effectiveType: connection.effectiveType,
                    downlink: connection.downlink,
                    rtt: connection.rtt
                };

                // تحسين الجودة حسب سرعة الشبكة
                this.adaptToNetworkSpeed(networkInfo);
            };

            connection.addEventListener('change', updateNetworkStatus);
            updateNetworkStatus();
        }
    }

    // تكييف الجودة حسب سرعة الشبكة
    adaptToNetworkSpeed(networkInfo) {
        const isSlowNetwork = networkInfo.effectiveType === 'slow-2g' || 
                             networkInfo.effectiveType === '2g' ||
                             networkInfo.downlink < 1;

        if (isSlowNetwork) {
            // تقليل جودة الصور
            document.documentElement.style.setProperty('--image-quality', '0.6');
            
            // تأخير التحميل غير الضروري
            this.delayNonCriticalLoading();
            
            console.log('تم تفعيل وضع الشبكة البطيئة');
        } else {
            document.documentElement.style.setProperty('--image-quality', '0.9');
        }
    }

    // تأخير التحميل غير الضروري
    delayNonCriticalLoading() {
        // تأخير تحميل الصور غير المرئية
        const images = document.querySelectorAll('img:not([data-critical])');
        images.forEach(img => {
            if (!this.isElementInViewport(img)) {
                img.loading = 'lazy';
            }
        });
    }

    // التحقق من وجود العنصر في المنطقة المرئية
    isElementInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    // تأخير بسيط
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // تنظيف الموارد
    cleanup() {
        // إيقاف جميع المراقبين
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();

        // تنظيف Cache
        this.cache.clear();
        this.imageCache.clear();

        // تنظيف URLs
        this.preloadedImages.forEach(url => {
            if (url.startsWith('blob:')) {
                URL.revokeObjectURL(url);
            }
        });
        this.preloadedImages.clear();
    }

    // تهيئة جميع التحسينات
    init() {
        // تحميل الموارد الأساسية
        this.preloadCriticalResources();
        
        // تفعيل Lazy Loading
        this.initLazyLoading();
        
        // بدء مراقبة الأداء
        this.startPerformanceMonitoring();
        
        // تنظيف دوري للذاكرة
        setInterval(() => this.optimizeMemory(), 300000); // كل 5 دقائق
        
        console.log('تم تفعيل محسن الأداء');
    }
}

// إنشاء مثيل عام
const performanceOptimizer = new PerformanceOptimizer();

// تفعيل التحسينات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    performanceOptimizer.init();
});

// تنظيف عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    performanceOptimizer.cleanup();
});
