using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc.Rendering;
using EmpMgr.Data;
using EmpMgr.Models;
using EmpMgr.ViewModels;

namespace EmpMgr.Controllers
{
    [Authorize]
    public class ConstantsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ConstantsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Constants
        public IActionResult Index()
        {
            return View();
        }

        #region Ranks Management

        // GET: Constants/Ranks
        public async Task<IActionResult> Ranks()
        {
            var ranks = await _context.Ranks.OrderBy(r => r.Order).ToListAsync();
            return View(ranks);
        }

        // GET: Constants/CreateRank
        public IActionResult CreateRank()
        {
            return View();
        }

        // POST: Constants/CreateRank
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateRank(Rank rank)
        {
            if (ModelState.IsValid)
            {
                _context.Add(rank);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة الرتبة بنجاح";
                return RedirectToAction(nameof(Ranks));
            }
            return View(rank);
        }

        // GET: Constants/EditRank/5
        public async Task<IActionResult> EditRank(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var rank = await _context.Ranks.FindAsync(id);
            if (rank == null)
            {
                return NotFound();
            }
            return View(rank);
        }

        // POST: Constants/EditRank/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditRank(int id, Rank rank)
        {
            if (id != rank.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(rank);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث الرتبة بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!RankExists(rank.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Ranks));
            }
            return View(rank);
        }

        // POST: Constants/DeleteRank/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteRank(int id)
        {
            var rank = await _context.Ranks.FindAsync(id);
            if (rank != null)
            {
                // التحقق من عدم وجود موظفين مرتبطين بهذه الرتبة
                var employeesCount = await _context.Employees.CountAsync(e => e.RankId == id);
                if (employeesCount > 0)
                {
                    TempData["ErrorMessage"] = $"لا يمكن حذف الرتبة لأنها مرتبطة بـ {employeesCount} موظف";
                }
                else
                {
                    _context.Ranks.Remove(rank);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم حذف الرتبة بنجاح";
                }
            }

            return RedirectToAction(nameof(Ranks));
        }

        #endregion

        #region Provinces Management

        // GET: Constants/Provinces
        public async Task<IActionResult> Provinces()
        {
            var provinces = await _context.Provinces
                .Include(p => p.Employees)
                .OrderBy(p => p.Name)
                .ToListAsync();
            return View(provinces);
        }

        // GET: Constants/CreateProvince
        public IActionResult CreateProvince()
        {
            return View();
        }

        // POST: Constants/CreateProvince
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateProvince(Province province)
        {
            // تطبيع الاسم
            province.Name = province.Name?.Trim();

            if (string.IsNullOrWhiteSpace(province.Name))
            {
                ModelState.AddModelError(nameof(province.Name), "اسم المحافظة مطلوب");
            }

            // التحقق من عدم التكرار قبل الحفظ
            if (await _context.Provinces.AnyAsync(p => p.Name == province.Name))
            {
                ModelState.AddModelError(nameof(province.Name), "هذه المحافظة موجودة بالفعل");
            }

            if (!ModelState.IsValid)
            {
                return View(province);
            }

            try
            {
                _context.Add(province);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة المحافظة بنجاح";
                return RedirectToAction(nameof(Provinces));
            }
            catch (DbUpdateException)
            {
                // في حال وقعت مخالفة فهرس فريد نتيجة سباق أو اختلاف حساسية حالة الأحرف
                ModelState.AddModelError(nameof(province.Name), "هذه المحافظة موجودة بالفعل");
                return View(province);
            }
        }

        // GET: Constants/EditProvince/5
        public async Task<IActionResult> EditProvince(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var province = await _context.Provinces
                .Include(p => p.Employees)
                .FirstOrDefaultAsync(p => p.Id == id);
            if (province == null)
            {
                return NotFound();
            }
            return View(province);
        }

        // POST: Constants/EditProvince/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditProvince(int id, Province province)
        {
            if (id != province.Id)
            {
                return NotFound();
            }

            // تطبيع الاسم
            province.Name = province.Name?.Trim();

            if (string.IsNullOrWhiteSpace(province.Name))
            {
                ModelState.AddModelError(nameof(province.Name), "اسم المحافظة مطلوب");
            }

            // التحقق من عدم التكرار قبل الحفظ (مع استثناء السجل الحالي)
            if (await _context.Provinces.AnyAsync(p => p.Id != id && p.Name == province.Name))
            {
                ModelState.AddModelError(nameof(province.Name), "هذه المحافظة موجودة بالفعل");
            }

            if (!ModelState.IsValid)
            {
                return View(province);
            }

            try
            {
                _context.Update(province);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم تحديث المحافظة بنجاح";
                return RedirectToAction(nameof(Provinces));
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ProvinceExists(province.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (DbUpdateException)
            {
                ModelState.AddModelError(nameof(province.Name), "هذه المحافظة موجودة بالفعل");
                return View(province);
            }
        }

        // POST: Constants/DeleteProvince/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteProvince(int id)
        {
            var province = await _context.Provinces.FindAsync(id);
            if (province != null)
            {
                // التحقق من عدم وجود موظفين مرتبطين بهذه المحافظة
                var employeesCount = await _context.Employees.CountAsync(e => e.ProvinceId == id);
                if (employeesCount > 0)
                {
                    TempData["ErrorMessage"] = $"لا يمكن حذف المحافظة لأنها مرتبطة بـ {employeesCount} موظف";
                }
                else
                {
                    _context.Provinces.Remove(province);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم حذف المحافظة بنجاح";
                }
            }

            return RedirectToAction(nameof(Provinces));
        }

        #endregion

        #region Universities Management

        // GET: Constants/Universities
        public async Task<IActionResult> Universities()
        {
            var universities = await _context.Universities
                .Include(u => u.Employees)
                .OrderBy(u => u.Name)
                .ToListAsync();
            return View(universities);
        }

        // GET: Constants/CreateUniversity
        public IActionResult CreateUniversity()
        {
            return View();
        }

        // POST: Constants/CreateUniversity
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateUniversity(University university)
        {
            if (ModelState.IsValid)
            {
                _context.Add(university);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة الجامعة بنجاح";
                return RedirectToAction(nameof(Universities));
            }
            return View(university);
        }

        // GET: Constants/EditUniversity/5
        public async Task<IActionResult> EditUniversity(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var university = await _context.Universities
                .Include(u => u.Employees)
                .FirstOrDefaultAsync(u => u.Id == id);
            if (university == null)
            {
                return NotFound();
            }
            return View(university);
        }

        // POST: Constants/EditUniversity/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditUniversity(int id, University university)
        {
            if (id != university.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(university);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث الجامعة بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!UniversityExists(university.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Universities));
            }
            return View(university);
        }

        // POST: Constants/DeleteUniversity/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteUniversity(int id)
        {
            var university = await _context.Universities.FindAsync(id);
            if (university != null)
            {
                // التحقق من عدم وجود موظفين مرتبطين بهذه الجامعة
                var employeesCount = await _context.Employees.CountAsync(e => e.UniversityId == id);
                if (employeesCount > 0)
                {
                    TempData["ErrorMessage"] = $"لا يمكن حذف الجامعة لأنها مرتبطة بـ {employeesCount} موظف";
                }
                else
                {
                    _context.Universities.Remove(university);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم حذف الجامعة بنجاح";
                }
            }

            return RedirectToAction(nameof(Universities));
        }

        #endregion

        #region Institutes Management

        // GET: Constants/Institutes
        public async Task<IActionResult> Institutes()
        {
            var institutes = await _context.Institutes
                .Include(i => i.Employees)
                .OrderBy(i => i.Name)
                .ToListAsync();
            return View(institutes);
        }

        // GET: Constants/CreateInstitute
        public IActionResult CreateInstitute()
        {
            return View();
        }

        // POST: Constants/CreateInstitute
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateInstitute(Institute institute)
        {
            if (ModelState.IsValid)
            {
                _context.Add(institute);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة المعهد بنجاح";
                return RedirectToAction(nameof(Institutes));
            }
            return View(institute);
        }

        // GET: Constants/EditInstitute/5
        public async Task<IActionResult> EditInstitute(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var institute = await _context.Institutes
                .Include(i => i.Employees)
                .FirstOrDefaultAsync(i => i.Id == id);
            if (institute == null)
            {
                return NotFound();
            }
            return View(institute);
        }

        // POST: Constants/EditInstitute/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditInstitute(int id, Institute institute)
        {
            if (id != institute.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(institute);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث المعهد بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!InstituteExists(institute.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Institutes));
            }
            return View(institute);
        }

        // POST: Constants/DeleteInstitute/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteInstitute(int id)
        {
            var institute = await _context.Institutes.FindAsync(id);
            if (institute != null)
            {
                // التحقق من عدم وجود موظفين مرتبطين بهذا المعهد
                var employeesCount = await _context.Employees.CountAsync(e => e.InstituteId == id);
                if (employeesCount > 0)
                {
                    TempData["ErrorMessage"] = $"لا يمكن حذف المعهد لأنه مرتبط بـ {employeesCount} موظف";
                }
                else
                {
                    _context.Institutes.Remove(institute);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم حذف المعهد بنجاح";
                }
            }

            return RedirectToAction(nameof(Institutes));
        }

        #endregion

        #region Colleges Management

        // GET: Constants/Colleges
        public async Task<IActionResult> Colleges()
        {
            var colleges = await _context.Colleges
                .Include(c => c.University)
                .Include(c => c.Employees)
                .OrderBy(c => c.University != null ? c.University.Name : "")
                .ThenBy(c => c.Name)
                .ToListAsync();
            return View(colleges);
        }

        // GET: Constants/CreateCollege
        public async Task<IActionResult> CreateCollege()
        {
            ViewBag.Universities = await _context.Universities
                .Where(u => u.IsActive)
                .OrderBy(u => u.Name)
                .ToListAsync();
            return View();
        }

        // POST: Constants/CreateCollege
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateCollege(College college)
        {
            if (ModelState.IsValid)
            {
                _context.Add(college);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة الكلية بنجاح";
                return RedirectToAction(nameof(Colleges));
            }

            ViewBag.Universities = await _context.Universities
                .Where(u => u.IsActive)
                .OrderBy(u => u.Name)
                .ToListAsync();
            return View(college);
        }

        // GET: Constants/EditCollege/5
        public async Task<IActionResult> EditCollege(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var college = await _context.Colleges
                .Include(c => c.University)
                .Include(c => c.Employees)
                .FirstOrDefaultAsync(c => c.Id == id);
            if (college == null)
            {
                return NotFound();
            }

            ViewBag.Universities = await _context.Universities
                .Where(u => u.IsActive)
                .OrderBy(u => u.Name)
                .ToListAsync();
            return View(college);
        }

        // POST: Constants/EditCollege/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditCollege(int id, College college)
        {
            if (id != college.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(college);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث الكلية بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CollegeExists(college.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Colleges));
            }

            ViewBag.Universities = await _context.Universities
                .Where(u => u.IsActive)
                .OrderBy(u => u.Name)
                .ToListAsync();
            return View(college);
        }

        // POST: Constants/DeleteCollege/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteCollege(int id)
        {
            var college = await _context.Colleges.FindAsync(id);
            if (college != null)
            {
                // التحقق من عدم وجود موظفين مرتبطين بهذه الكلية
                var employeesCount = await _context.Employees.CountAsync(e => e.CollegeId == id);
                if (employeesCount > 0)
                {
                    TempData["ErrorMessage"] = $"لا يمكن حذف الكلية لأنها مرتبطة بـ {employeesCount} موظف";
                }
                else
                {
                    _context.Colleges.Remove(college);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم حذف الكلية بنجاح";
                }
            }

            return RedirectToAction(nameof(Colleges));
        }

        // GET: Constants/GetCollegesByUniversity/5
        [HttpGet]
        public async Task<JsonResult> GetCollegesByUniversity(int universityId)
        {
            var colleges = await _context.Colleges
                .Where(c => c.UniversityId == universityId && c.IsActive)
                .OrderBy(c => c.Name)
                .Select(c => new { id = c.Id, name = c.Name })
                .ToListAsync();

            return Json(colleges);
        }

        #endregion

        #region Departments Management

        // GET: Constants/Departments
        public async Task<IActionResult> Departments()
        {
            var departments = await _context.Departments
                .Include(d => d.Institute)
                .Include(d => d.Employees)
                .OrderBy(d => d.Institute.Name)
                .ThenBy(d => d.Name)
                .ToListAsync();
            return View(departments);
        }

        // GET: Constants/CreateDepartment
        public async Task<IActionResult> CreateDepartment()
        {
            ViewBag.Institutes = await _context.Institutes
                .Where(i => i.IsActive)
                .OrderBy(i => i.Name)
                .ToListAsync();
            return View();
        }

        // POST: Constants/CreateDepartment
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateDepartment(Department department)
        {
            // إزالة Institute من ModelState لأنه navigation property
            ModelState.Remove("Institute");

            if (ModelState.IsValid)
            {
                // التحقق من عدم تكرار اسم القسم في نفس المعهد
                var existingDepartment = await _context.Departments
                    .FirstOrDefaultAsync(d => d.Name == department.Name && d.InstituteId == department.InstituteId);

                if (existingDepartment != null)
                {
                    ModelState.AddModelError("Name", "يوجد قسم بنفس الاسم في هذا المعهد");
                    ViewBag.Institutes = await _context.Institutes
                        .Where(i => i.IsActive)
                        .OrderBy(i => i.Name)
                        .ToListAsync();
                    return View(department);
                }

                department.CreatedDate = DateTime.Now;
                _context.Add(department);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة القسم بنجاح";
                return RedirectToAction(nameof(Departments));
            }

            ViewBag.Institutes = await _context.Institutes
                .Where(i => i.IsActive)
                .OrderBy(i => i.Name)
                .ToListAsync();
            return View(department);
        }

        // GET: Constants/EditDepartment/5
        public async Task<IActionResult> EditDepartment(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var department = await _context.Departments.FindAsync(id);
            if (department == null)
            {
                return NotFound();
            }

            ViewBag.Institutes = await _context.Institutes
                .Where(i => i.IsActive)
                .OrderBy(i => i.Name)
                .ToListAsync();
            return View(department);
        }

        // POST: Constants/EditDepartment/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditDepartment(int id, Department department)
        {
            if (id != department.Id)
            {
                return NotFound();
            }

            // إزالة Institute من ModelState لأنه navigation property
            ModelState.Remove("Institute");

            if (ModelState.IsValid)
            {
                try
                {
                    // التحقق من عدم تكرار اسم القسم في نفس المعهد
                    var existingDepartment = await _context.Departments
                        .FirstOrDefaultAsync(d => d.Name == department.Name && d.InstituteId == department.InstituteId && d.Id != department.Id);

                    if (existingDepartment != null)
                    {
                        ModelState.AddModelError("Name", "يوجد قسم بنفس الاسم في هذا المعهد");
                        ViewBag.Institutes = await _context.Institutes
                            .Where(i => i.IsActive)
                            .OrderBy(i => i.Name)
                            .ToListAsync();
                        return View(department);
                    }

                    _context.Update(department);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث القسم بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!DepartmentExists(department.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Departments));
            }

            ViewBag.Institutes = await _context.Institutes
                .Where(i => i.IsActive)
                .OrderBy(i => i.Name)
                .ToListAsync();
            return View(department);
        }

        // GET: Constants/DeleteDepartment/5
        public async Task<IActionResult> DeleteDepartment(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var department = await _context.Departments
                .Include(d => d.Institute)
                .Include(d => d.Employees)
                .FirstOrDefaultAsync(d => d.Id == id);

            if (department == null)
            {
                return NotFound();
            }

            return View(department);
        }

        // POST: Constants/DeleteDepartment/5
        [HttpPost, ActionName("DeleteDepartment")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteDepartmentConfirmed(int id)
        {
            var department = await _context.Departments
                .Include(d => d.Employees)
                .FirstOrDefaultAsync(d => d.Id == id);

            if (department != null)
            {
                // التحقق من وجود موظفين مرتبطين بهذا القسم
                if (department.Employees.Any())
                {
                    TempData["ErrorMessage"] = $"لا يمكن حذف القسم '{department.Name}' لأنه مرتبط بـ {department.Employees.Count} موظف";
                    return RedirectToAction(nameof(Departments));
                }

                _context.Departments.Remove(department);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم حذف القسم بنجاح";
            }

            return RedirectToAction(nameof(Departments));
        }

        // GET: Constants/GetDepartmentsByInstitute/5
        [HttpGet]
        public async Task<JsonResult> GetDepartmentsByInstitute(int instituteId)
        {
            var departments = await _context.Departments
                .Where(d => d.InstituteId == instituteId && d.IsActive)
                .OrderBy(d => d.Name)
                .Select(d => new { id = d.Id, name = d.Name })
                .ToListAsync();

            return Json(departments);
        }

        #endregion

        #region Ministries Management

        // GET: Constants/Ministries
        public async Task<IActionResult> Ministries()
        {
            var ministries = await _context.Ministries
                .Include(m => m.Employees)
                .Include(m => m.Agencies)
                .OrderBy(m => m.Name)
                .ToListAsync();
            return View(ministries);
        }

        // GET: Constants/CreateMinistry
        public IActionResult CreateMinistry()
        {
            return View();
        }

        // POST: Constants/CreateMinistry
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateMinistry([Bind("Name,Code,Description,Location,EstablishedYear,IsActive")] Ministry ministry)
        {
            if (ModelState.IsValid)
            {
                ministry.CreatedDate = DateTime.Now;
                _context.Add(ministry);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة الوزارة بنجاح";
                return RedirectToAction(nameof(Ministries));
            }
            return View(ministry);
        }

        // GET: Constants/EditMinistry/5
        public async Task<IActionResult> EditMinistry(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var ministry = await _context.Ministries.FindAsync(id);
            if (ministry == null)
            {
                return NotFound();
            }
            return View(ministry);
        }

        // POST: Constants/EditMinistry/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditMinistry(int id, [Bind("Id,Name,Code,Description,Location,EstablishedYear,IsActive,CreatedDate")] Ministry ministry)
        {
            if (id != ministry.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(ministry);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث الوزارة بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!MinistryExists(ministry.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Ministries));
            }
            return View(ministry);
        }

        // GET: Constants/DeleteMinistry/5
        public async Task<IActionResult> DeleteMinistry(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var ministry = await _context.Ministries
                .Include(m => m.Employees)
                .Include(m => m.Agencies)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (ministry == null)
            {
                return NotFound();
            }

            return View(ministry);
        }

        // POST: Constants/DeleteMinistry/5
        [HttpPost, ActionName("DeleteMinistry")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteMinistryConfirmed(int id)
        {
            var ministry = await _context.Ministries
                .Include(m => m.Employees)
                .Include(m => m.Agencies)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (ministry != null)
            {
                if (ministry.Employees.Any() || ministry.Agencies.Any())
                {
                    TempData["ErrorMessage"] = "لا يمكن حذف الوزارة لأنها مرتبطة بموظفين أو وكالات";
                    return RedirectToAction(nameof(Ministries));
                }

                _context.Ministries.Remove(ministry);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم حذف الوزارة بنجاح";
            }

            return RedirectToAction(nameof(Ministries));
        }

        #endregion

        #region Agencies Management

        // GET: Constants/Agencies
        public async Task<IActionResult> Agencies()
        {
            var agencies = await _context.Agencies
                .Include(a => a.Ministry)
                .Include(a => a.Employees)
                .Include(a => a.Directorates)
                .OrderBy(a => a.Ministry.Name)
                .ThenBy(a => a.Name)
                .ToListAsync();
            return View(agencies);
        }

        // GET: Constants/CreateAgency
        public async Task<IActionResult> CreateAgency()
        {
            ViewBag.MinistryId = await GetMinistriesSelectList();
            return View();
        }

        // POST: Constants/CreateAgency
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateAgency(Agency agency)
        {
            // إزالة Ministry من ModelState لأنه navigation property
            ModelState.Remove("Ministry");

            // فحص التكرار - منع تكرار اسم الوكالة على مستوى النظام بالكامل
            var existingAgency = await _context.Agencies
                .FirstOrDefaultAsync(a => a.Name == agency.Name);

            if (existingAgency != null)
            {
                ModelState.AddModelError("Name", "يوجد وكالة بنفس هذا الاسم في النظام");
            }

            if (ModelState.IsValid)
            {
                agency.CreatedDate = DateTime.Now;
                _context.Add(agency);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة الوكالة بنجاح";
                return RedirectToAction(nameof(Agencies));
            }

            ViewBag.MinistryId = await GetMinistriesSelectList(agency.MinistryId);
            return View(agency);
        }

        // GET: Constants/EditAgency/5
        public async Task<IActionResult> EditAgency(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var agency = await _context.Agencies.FindAsync(id);
            if (agency == null)
            {
                return NotFound();
            }
            ViewBag.MinistryId = await GetMinistriesSelectList(agency.MinistryId);
            return View(agency);
        }

        // POST: Constants/EditAgency/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditAgency(int id, Agency agency)
        {
            if (id != agency.Id)
            {
                return NotFound();
            }

            // إزالة Ministry من ModelState لأنه navigation property
            ModelState.Remove("Ministry");

            // فحص التكرار - منع تكرار اسم الوكالة على مستوى النظام بالكامل
            var existingAgency = await _context.Agencies
                .FirstOrDefaultAsync(a => a.Name == agency.Name && a.Id != agency.Id);

            if (existingAgency != null)
            {
                ModelState.AddModelError("Name", "يوجد وكالة بنفس هذا الاسم في النظام");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(agency);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث الوكالة بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!AgencyExists(agency.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Agencies));
            }
            ViewBag.MinistryId = await GetMinistriesSelectList(agency.MinistryId);
            return View(agency);
        }

        // GET: Constants/DeleteAgency/5
        public async Task<IActionResult> DeleteAgency(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var agency = await _context.Agencies
                .Include(a => a.Ministry)
                .Include(a => a.Employees)
                .Include(a => a.Directorates)
                .FirstOrDefaultAsync(a => a.Id == id);
            if (agency == null)
            {
                return NotFound();
            }

            return View(agency);
        }

        // POST: Constants/DeleteAgency/5
        [HttpPost, ActionName("DeleteAgency")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteAgencyConfirmed(int id)
        {
            var agency = await _context.Agencies
                .Include(a => a.Employees)
                .Include(a => a.Directorates)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (agency != null)
            {
                if (agency.Employees.Any() || agency.Directorates.Any())
                {
                    TempData["ErrorMessage"] = "لا يمكن حذف الوكالة لأنها مرتبطة بموظفين أو مديريات";
                    return RedirectToAction(nameof(Agencies));
                }

                _context.Agencies.Remove(agency);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم حذف الوكالة بنجاح";
            }

            return RedirectToAction(nameof(Agencies));
        }

        // GET: Constants/GetAgenciesByMinistry/5
        [HttpGet]
        public async Task<JsonResult> GetAgenciesByMinistry(int ministryId)
        {
            try
            {
                // تسجيل المعلومات للتشخيص
                var logger = HttpContext.RequestServices.GetService<ILogger<ConstantsController>>();
                logger?.LogInformation($"GetAgenciesByMinistry called with ministryId: {ministryId}");

                // التحقق من وجود الوزارة
                var ministryExists = await _context.Ministries
                    .AnyAsync(m => m.Id == ministryId && m.IsActive);

                if (!ministryExists)
                {
                    logger?.LogWarning($"Ministry with ID {ministryId} not found or inactive");
                    return Json(new List<object>());
                }

                var agencies = await _context.Agencies
                    .Where(a => a.MinistryId == ministryId && a.IsActive)
                    .OrderBy(a => a.Name)
                    .Select(a => new { id = a.Id, name = a.Name })
                    .ToListAsync();

                logger?.LogInformation($"Found {agencies.Count} agencies for ministry {ministryId}");
                return Json(agencies);
            }
            catch (Exception ex)
            {
                var logger = HttpContext.RequestServices.GetService<ILogger<ConstantsController>>();
                logger?.LogError(ex, $"Error in GetAgenciesByMinistry for ministryId: {ministryId}");
                return Json(new { error = ex.Message });
            }
        }

        #endregion

        #region Directorates Management

        // GET: Constants/Directorates
        public async Task<IActionResult> Directorates()
        {
            var directorates = await _context.Directorates
                .Include(d => d.Agency)
                    .ThenInclude(a => a.Ministry)
                .Include(d => d.Employees)
                .Include(d => d.GovernmentDepartments)
                .OrderBy(d => d.Agency.Ministry.Name)
                .ThenBy(d => d.Agency.Name)
                .ThenBy(d => d.Name)
                .ToListAsync();
            return View(directorates);
        }

        // GET: Constants/CreateDirectorate
        public async Task<IActionResult> CreateDirectorate()
        {
            ViewBag.AgencyId = await GetAgenciesSelectList();
            return View();
        }

        // POST: Constants/CreateDirectorate
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateDirectorate(Directorate directorate)
        {
            // إزالة Agency من ModelState لأنه navigation property
            ModelState.Remove("Agency");

            if (ModelState.IsValid)
            {
                directorate.CreatedDate = DateTime.Now;
                _context.Add(directorate);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة المديرية بنجاح";
                return RedirectToAction(nameof(Directorates));
            }
            ViewBag.AgencyId = await GetAgenciesSelectList(directorate.AgencyId);
            return View(directorate);
        }

        // GET: Constants/EditDirectorate/5
        public async Task<IActionResult> EditDirectorate(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var directorate = await _context.Directorates.FindAsync(id);
            if (directorate == null)
            {
                return NotFound();
            }
            ViewBag.AgencyId = await GetAgenciesSelectList(directorate.AgencyId);
            return View(directorate);
        }

        // POST: Constants/EditDirectorate/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditDirectorate(int id, Directorate directorate)
        {
            if (id != directorate.Id)
            {
                return NotFound();
            }

            // إزالة Agency من ModelState لأنه navigation property
            ModelState.Remove("Agency");

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(directorate);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث المديرية بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!DirectorateExists(directorate.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Directorates));
            }
            ViewBag.AgencyId = await GetAgenciesSelectList(directorate.AgencyId);
            return View(directorate);
        }

        // GET: Constants/DeleteDirectorate/5
        public async Task<IActionResult> DeleteDirectorate(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var directorate = await _context.Directorates
                .Include(d => d.Agency)
                    .ThenInclude(a => a.Ministry)
                .Include(d => d.Employees)
                .Include(d => d.GovernmentDepartments)
                .FirstOrDefaultAsync(d => d.Id == id);
            if (directorate == null)
            {
                return NotFound();
            }

            return View(directorate);
        }

        // POST: Constants/DeleteDirectorate/5
        [HttpPost, ActionName("DeleteDirectorate")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteDirectorateConfirmed(int id)
        {
            var directorate = await _context.Directorates
                .Include(d => d.Employees)
                .Include(d => d.GovernmentDepartments)
                .FirstOrDefaultAsync(d => d.Id == id);

            if (directorate != null)
            {
                if (directorate.Employees.Any() || directorate.GovernmentDepartments.Any())
                {
                    TempData["ErrorMessage"] = "لا يمكن حذف المديرية لأنها مرتبطة بموظفين أو أقسام حكومية";
                    return RedirectToAction(nameof(Directorates));
                }

                _context.Directorates.Remove(directorate);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم حذف المديرية بنجاح";
            }

            return RedirectToAction(nameof(Directorates));
        }

        // GET: Constants/GetDirectoratesByAgency/5
        [HttpGet]
        public async Task<JsonResult> GetDirectoratesByAgency(int agencyId)
        {
            try
            {
                var logger = HttpContext.RequestServices.GetService<ILogger<ConstantsController>>();
                logger?.LogInformation($"GetDirectoratesByAgency called with agencyId: {agencyId}");

                // التحقق من وجود الوكالة
                var agencyExists = await _context.Agencies
                    .AnyAsync(a => a.Id == agencyId && a.IsActive);

                if (!agencyExists)
                {
                    logger?.LogWarning($"Agency with ID {agencyId} not found or inactive");
                    return Json(new List<object>());
                }

                var directorates = await _context.Directorates
                    .Where(d => d.AgencyId == agencyId && d.IsActive)
                    .OrderBy(d => d.Name)
                    .Select(d => new { id = d.Id, name = d.Name })
                    .ToListAsync();

                logger?.LogInformation($"Found {directorates.Count} directorates for agency {agencyId}");
                return Json(directorates);
            }
            catch (Exception ex)
            {
                var logger = HttpContext.RequestServices.GetService<ILogger<ConstantsController>>();
                logger?.LogError(ex, $"Error in GetDirectoratesByAgency for agencyId: {agencyId}");
                return Json(new { error = ex.Message });
            }
        }

        #endregion

        #region Government Departments Management

        // GET: Constants/GovernmentDepartments
        public async Task<IActionResult> GovernmentDepartments()
        {
            var departments = await _context.GovernmentDepartments
                .Include(gd => gd.Directorate)
                    .ThenInclude(d => d.Agency)
                        .ThenInclude(a => a.Ministry)
                .Include(gd => gd.Employees)
                .Include(gd => gd.Divisions)
                .OrderBy(gd => gd.Directorate.Agency.Ministry.Name)
                .ThenBy(gd => gd.Directorate.Agency.Name)
                .ThenBy(gd => gd.Directorate.Name)
                .ThenBy(gd => gd.Name)
                .ToListAsync();
            return View(departments);
        }

        // GET: Constants/CreateGovernmentDepartment
        public async Task<IActionResult> CreateGovernmentDepartment()
        {
            ViewBag.DirectorateId = await GetDirectoratesSelectList();
            return View();
        }

        // POST: Constants/CreateGovernmentDepartment
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateGovernmentDepartment(GovernmentDepartment department)
        {
            // إزالة Directorate من ModelState لأنه navigation property
            ModelState.Remove("Directorate");

            if (ModelState.IsValid)
            {
                department.CreatedDate = DateTime.Now;
                _context.Add(department);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة القسم الحكومي بنجاح";
                return RedirectToAction(nameof(GovernmentDepartments));
            }
            ViewBag.DirectorateId = await GetDirectoratesSelectList(department.DirectorateId);
            return View(department);
        }

        // GET: Constants/EditGovernmentDepartment/5
        public async Task<IActionResult> EditGovernmentDepartment(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var department = await _context.GovernmentDepartments.FindAsync(id);
            if (department == null)
            {
                return NotFound();
            }
            ViewBag.DirectorateId = await GetDirectoratesSelectList(department.DirectorateId);
            return View(department);
        }

        // POST: Constants/EditGovernmentDepartment/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditGovernmentDepartment(int id, GovernmentDepartment department)
        {
            if (id != department.Id)
            {
                return NotFound();
            }

            // إزالة Directorate من ModelState لأنه navigation property
            ModelState.Remove("Directorate");

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(department);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث القسم الحكومي بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!GovernmentDepartmentExists(department.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(GovernmentDepartments));
            }
            ViewBag.DirectorateId = await GetDirectoratesSelectList(department.DirectorateId);
            return View(department);
        }

        // GET: Constants/DeleteGovernmentDepartment/5
        public async Task<IActionResult> DeleteGovernmentDepartment(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var department = await _context.GovernmentDepartments
                .Include(gd => gd.Directorate)
                    .ThenInclude(d => d.Agency)
                        .ThenInclude(a => a.Ministry)
                .Include(gd => gd.Employees)
                .Include(gd => gd.Divisions)
                .FirstOrDefaultAsync(gd => gd.Id == id);
            if (department == null)
            {
                return NotFound();
            }

            return View(department);
        }

        // POST: Constants/DeleteGovernmentDepartment/5
        [HttpPost, ActionName("DeleteGovernmentDepartment")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteGovernmentDepartmentConfirmed(int id)
        {
            var department = await _context.GovernmentDepartments
                .Include(gd => gd.Employees)
                .Include(gd => gd.Divisions)
                .FirstOrDefaultAsync(gd => gd.Id == id);

            if (department != null)
            {
                if (department.Employees.Any() || department.Divisions.Any())
                {
                    TempData["ErrorMessage"] = "لا يمكن حذف القسم الحكومي لأنه مرتبط بموظفين أو شعب";
                    return RedirectToAction(nameof(GovernmentDepartments));
                }

                _context.GovernmentDepartments.Remove(department);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم حذف القسم الحكومي بنجاح";
            }

            return RedirectToAction(nameof(GovernmentDepartments));
        }

        // GET: Constants/GetGovernmentDepartmentsByDirectorate/5
        [HttpGet]
        public async Task<JsonResult> GetGovernmentDepartmentsByDirectorate(int directorateId)
        {
            try
            {
                var logger = HttpContext.RequestServices.GetService<ILogger<ConstantsController>>();
                logger?.LogInformation($"GetGovernmentDepartmentsByDirectorate called with directorateId: {directorateId}");

                // التحقق من وجود المديرية
                var directorateExists = await _context.Directorates
                    .AnyAsync(d => d.Id == directorateId && d.IsActive);

                if (!directorateExists)
                {
                    logger?.LogWarning($"Directorate with ID {directorateId} not found or inactive");
                    return Json(new List<object>());
                }

                var departments = await _context.GovernmentDepartments
                    .Where(gd => gd.DirectorateId == directorateId && gd.IsActive)
                    .OrderBy(gd => gd.Name)
                    .Select(gd => new { id = gd.Id, name = gd.Name })
                    .ToListAsync();

                logger?.LogInformation($"Found {departments.Count} government departments for directorate {directorateId}");
                return Json(departments);
            }
            catch (Exception ex)
            {
                var logger = HttpContext.RequestServices.GetService<ILogger<ConstantsController>>();
                logger?.LogError(ex, $"Error in GetGovernmentDepartmentsByDirectorate for directorateId: {directorateId}");
                return Json(new { error = ex.Message });
            }
        }

        #endregion

        #region Divisions Management

        // GET: Constants/Divisions
        public async Task<IActionResult> Divisions()
        {
            var divisions = await _context.Divisions
                .Include(div => div.GovernmentDepartment)
                    .ThenInclude(gd => gd.Directorate)
                        .ThenInclude(d => d.Agency)
                            .ThenInclude(a => a.Ministry)
                .Include(div => div.Employees)
                .OrderBy(div => div.GovernmentDepartment.Directorate.Agency.Ministry.Name)
                .ThenBy(div => div.GovernmentDepartment.Directorate.Agency.Name)
                .ThenBy(div => div.GovernmentDepartment.Directorate.Name)
                .ThenBy(div => div.GovernmentDepartment.Name)
                .ThenBy(div => div.Name)
                .ToListAsync();
            return View(divisions);
        }

        // GET: Constants/CreateDivision
        public async Task<IActionResult> CreateDivision()
        {
            ViewBag.GovernmentDepartmentId = await GetGovernmentDepartmentsSelectList();
            return View();
        }

        // POST: Constants/CreateDivision
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateDivision(Division division)
        {
            // إزالة GovernmentDepartment من ModelState لأنه navigation property
            ModelState.Remove("GovernmentDepartment");

            if (ModelState.IsValid)
            {
                division.CreatedDate = DateTime.Now;
                _context.Add(division);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة الشعبة بنجاح";
                return RedirectToAction(nameof(Divisions));
            }
            ViewBag.GovernmentDepartmentId = await GetGovernmentDepartmentsSelectList(division.GovernmentDepartmentId);
            return View(division);
        }

        // GET: Constants/EditDivision/5
        public async Task<IActionResult> EditDivision(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var division = await _context.Divisions.FindAsync(id);
            if (division == null)
            {
                return NotFound();
            }
            ViewBag.GovernmentDepartmentId = await GetGovernmentDepartmentsSelectList(division.GovernmentDepartmentId);
            return View(division);
        }

        // POST: Constants/EditDivision/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditDivision(int id, Division division)
        {
            if (id != division.Id)
            {
                return NotFound();
            }

            // إزالة GovernmentDepartment من ModelState لأنه navigation property
            ModelState.Remove("GovernmentDepartment");

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(division);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث الشعبة بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!DivisionExists(division.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Divisions));
            }
            ViewBag.GovernmentDepartmentId = await GetGovernmentDepartmentsSelectList(division.GovernmentDepartmentId);
            return View(division);
        }

        // GET: Constants/DeleteDivision/5
        public async Task<IActionResult> DeleteDivision(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var division = await _context.Divisions
                .Include(div => div.GovernmentDepartment)
                    .ThenInclude(gd => gd.Directorate)
                        .ThenInclude(d => d.Agency)
                            .ThenInclude(a => a.Ministry)
                .Include(div => div.Employees)
                .FirstOrDefaultAsync(div => div.Id == id);
            if (division == null)
            {
                return NotFound();
            }

            return View(division);
        }

        // POST: Constants/DeleteDivision/5
        [HttpPost, ActionName("DeleteDivision")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteDivisionConfirmed(int id)
        {
            var division = await _context.Divisions
                .Include(div => div.Employees)
                .FirstOrDefaultAsync(div => div.Id == id);

            if (division != null)
            {
                if (division.Employees.Any())
                {
                    TempData["ErrorMessage"] = "لا يمكن حذف الشعبة لأنها مرتبطة بموظفين";
                    return RedirectToAction(nameof(Divisions));
                }

                _context.Divisions.Remove(division);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم حذف الشعبة بنجاح";
            }

            return RedirectToAction(nameof(Divisions));
        }

        // GET: Constants/GetDivisionsByGovernmentDepartment/5
        [HttpGet]
        public async Task<JsonResult> GetDivisionsByGovernmentDepartment(int governmentDepartmentId)
        {
            try
            {
                var logger = HttpContext.RequestServices.GetService<ILogger<ConstantsController>>();
                logger?.LogInformation($"GetDivisionsByGovernmentDepartment called with governmentDepartmentId: {governmentDepartmentId}");

                // التحقق من وجود القسم الحكومي
                var departmentExists = await _context.GovernmentDepartments
                    .AnyAsync(gd => gd.Id == governmentDepartmentId && gd.IsActive);

                if (!departmentExists)
                {
                    logger?.LogWarning($"Government department with ID {governmentDepartmentId} not found or inactive");
                    return Json(new List<object>());
                }

                var divisions = await _context.Divisions
                    .Where(div => div.GovernmentDepartmentId == governmentDepartmentId && div.IsActive)
                    .OrderBy(div => div.Name)
                    .Select(div => new { id = div.Id, name = div.Name })
                    .ToListAsync();

                logger?.LogInformation($"Found {divisions.Count} divisions for government department {governmentDepartmentId}");
                return Json(divisions);
            }
            catch (Exception ex)
            {
                var logger = HttpContext.RequestServices.GetService<ILogger<ConstantsController>>();
                logger?.LogError(ex, $"Error in GetDivisionsByGovernmentDepartment for governmentDepartmentId: {governmentDepartmentId}");
                return Json(new { error = ex.Message });
            }
        }

        // GET: Constants/TestData - صفحة اختبار البيانات
        [HttpGet]
        public async Task<IActionResult> TestData()
        {
            var data = new
            {
                MinistriesCount = await _context.Ministries.CountAsync(),
                AgenciesCount = await _context.Agencies.CountAsync(),
                DirectoratesCount = await _context.Directorates.CountAsync(),
                GovernmentDepartmentsCount = await _context.GovernmentDepartments.CountAsync(),
                DivisionsCount = await _context.Divisions.CountAsync(),

                Ministries = await _context.Ministries.Select(m => new { m.Id, m.Name }).ToListAsync(),
                Agencies = await _context.Agencies.Select(a => new { a.Id, a.Name, a.MinistryId }).ToListAsync(),
                Directorates = await _context.Directorates.Select(d => new { d.Id, d.Name, d.AgencyId }).ToListAsync(),
                GovernmentDepartments = await _context.GovernmentDepartments.Select(gd => new { gd.Id, gd.Name, gd.DirectorateId }).ToListAsync(),
                Divisions = await _context.Divisions.Select(div => new { div.Id, div.Name, div.GovernmentDepartmentId }).ToListAsync()
            };

            return Json(data);
        }

        #endregion

        // Helper methods
        private bool RankExists(int id)
        {
            return _context.Ranks.Any(e => e.Id == id);
        }

        private bool ProvinceExists(int id)
        {
            return _context.Provinces.Any(e => e.Id == id);
        }

        private bool UniversityExists(int id)
        {
            return _context.Universities.Any(e => e.Id == id);
        }

        private bool InstituteExists(int id)
        {
            return _context.Institutes.Any(e => e.Id == id);
        }

        private bool CollegeExists(int id)
        {
            return _context.Colleges.Any(e => e.Id == id);
        }

        private bool DepartmentExists(int id)
        {
            return _context.Departments.Any(e => e.Id == id);
        }

        private bool MinistryExists(int id)
        {
            return _context.Ministries.Any(e => e.Id == id);
        }

        private bool AgencyExists(int id)
        {
            return _context.Agencies.Any(e => e.Id == id);
        }

        private bool DirectorateExists(int id)
        {
            return _context.Directorates.Any(e => e.Id == id);
        }

        private bool GovernmentDepartmentExists(int id)
        {
            return _context.GovernmentDepartments.Any(e => e.Id == id);
        }

        private bool DivisionExists(int id)
        {
            return _context.Divisions.Any(e => e.Id == id);
        }

        // Helper methods for SelectLists
        private async Task<SelectList> GetMinistriesSelectList(int? selectedValue = null)
        {
            var ministries = await _context.Ministries
                .Where(m => m.IsActive)
                .OrderBy(m => m.Name)
                .ToListAsync();
            return new SelectList(ministries, "Id", "Name", selectedValue);
        }

        private async Task<SelectList> GetAgenciesSelectList(int? selectedValue = null)
        {
            var agencies = await _context.Agencies
                .Include(a => a.Ministry)
                .Where(a => a.IsActive)
                .OrderBy(a => a.Ministry.Name)
                .ThenBy(a => a.Name)
                .ToListAsync();
            return new SelectList(agencies, "Id", "Name", selectedValue);
        }

        private async Task<SelectList> GetDirectoratesSelectList(int? selectedValue = null)
        {
            var directorates = await _context.Directorates
                .Include(d => d.Agency)
                    .ThenInclude(a => a.Ministry)
                .Where(d => d.IsActive)
                .OrderBy(d => d.Agency.Ministry.Name)
                .ThenBy(d => d.Agency.Name)
                .ThenBy(d => d.Name)
                .ToListAsync();
            return new SelectList(directorates, "Id", "Name", selectedValue);
        }

        private async Task<SelectList> GetGovernmentDepartmentsSelectList(int? selectedValue = null)
        {
            var departments = await _context.GovernmentDepartments
                .Include(gd => gd.Directorate)
                    .ThenInclude(d => d.Agency)
                        .ThenInclude(a => a.Ministry)
                .Where(gd => gd.IsActive)
                .OrderBy(gd => gd.Directorate.Agency.Ministry.Name)
                .ThenBy(gd => gd.Directorate.Agency.Name)
                .ThenBy(gd => gd.Directorate.Name)
                .ThenBy(gd => gd.Name)
                .ToListAsync();
            return new SelectList(departments, "Id", "Name", selectedValue);
        }

        // APIs للبحث المتقدم
        [HttpGet]
        public async Task<JsonResult> GetRanks()
        {
            var ranks = await _context.Ranks
                .Where(r => r.IsActive)
                .OrderBy(r => r.Order)
                .Select(r => new { id = r.Id, name = r.Name })
                .ToListAsync();

            return Json(ranks);
        }

        [HttpGet]
        public async Task<IActionResult> GetRankInfo(int id)
        {
            try
            {
                var rank = await _context.Ranks
                    .Where(r => r.Id == id && r.IsActive)
                    .Select(r => new {
                        id = r.Id,
                        name = r.Name,
                        rankDurationYears = r.RankDurationYears,
                        minAgeForPromotion = r.MinAgeForPromotion,
                        maxAgeForPromotion = r.MaxAgeForPromotion,
                        requiresTrainingCourses = r.RequiresTrainingCourses,
                        requiredCoursesCount = r.RequiredCoursesCount,
                        requiresPerformanceEvaluation = r.RequiresPerformanceEvaluation,
                        minPerformanceScore = r.MinPerformanceScore
                    })
                    .FirstOrDefaultAsync();

                if (rank == null)
                {
                    return NotFound();
                }

                return Json(rank);
            }
            catch (Exception ex)
            {
                // يمكن إضافة logging هنا لاحقاً
                return BadRequest("خطأ في جلب معلومات الرتبة");
            }
        }

        [HttpGet]
        public async Task<JsonResult> GetProvinces()
        {
            var provinces = await _context.Provinces
                .Where(p => p.IsActive)
                .OrderBy(p => p.Name)
                .Select(p => new { id = p.Id, name = p.Name })
                .ToListAsync();

            return Json(provinces);
        }

        [HttpGet]
        public async Task<JsonResult> GetUniversities()
        {
            var universities = await _context.Universities
                .Where(u => u.IsActive)
                .OrderBy(u => u.Name)
                .Select(u => new { id = u.Id, name = u.Name, type = u.Type })
                .ToListAsync();

            return Json(universities);
        }

        [HttpGet]
        public async Task<JsonResult> GetInstitutes()
        {
            var institutes = await _context.Institutes
                .Where(i => i.IsActive)
                .OrderBy(i => i.Name)
                .Select(i => new { id = i.Id, name = i.Name, type = i.Type })
                .ToListAsync();

            return Json(institutes);
        }

        [HttpGet]
        public async Task<JsonResult> GetColleges()
        {
            var colleges = await _context.Colleges
                .Where(c => c.IsActive)
                .Include(c => c.University)
                .OrderBy(c => c.University != null ? c.University.Name : "")
                .ThenBy(c => c.Name)
                .Select(c => new { id = c.Id, name = c.Name, universityName = c.University != null ? c.University.Name : "", type = c.Type })
                .ToListAsync();

            return Json(colleges);
        }

        [HttpGet]
        public async Task<JsonResult> GetMinistries()
        {
            var ministries = await _context.Ministries
                .Where(m => m.IsActive)
                .OrderBy(m => m.Name)
                .Select(m => new { id = m.Id, name = m.Name })
                .ToListAsync();

            return Json(ministries);
        }
    }
}
