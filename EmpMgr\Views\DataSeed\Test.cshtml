@{
    ViewData["Title"] = "اختبار البيانات";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-vial me-2"></i>
                        اختبار البيانات والـ APIs
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>اختبار البنية الهرمية الحكومية</h5>
                            <div class="mb-3">
                                <label for="testMinistrySelect" class="form-label">اختر الوزارة:</label>
                                <select id="testMinistrySelect" class="form-select" onchange="testLoadAgencies()">
                                    <option value="">اختر الوزارة</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="testAgencySelect" class="form-label">الوكالات:</label>
                                <select id="testAgencySelect" class="form-select" onchange="testLoadDirectorates()" disabled>
                                    <option value="">اختر الوكالة</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="testDirectorateSelect" class="form-label">المديريات:</label>
                                <select id="testDirectorateSelect" class="form-select" onchange="testLoadGovernmentDepartments()" disabled>
                                    <option value="">اختر المديرية</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="testGovernmentDepartmentSelect" class="form-label">الأقسام الحكومية:</label>
                                <select id="testGovernmentDepartmentSelect" class="form-select" onchange="testLoadDivisions()" disabled>
                                    <option value="">اختر القسم الحكومي</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="testDivisionSelect" class="form-label">الشعب:</label>
                                <select id="testDivisionSelect" class="form-select" disabled>
                                    <option value="">اختر الشعبة</option>
                                </select>
                            </div>

                            <button type="button" class="btn btn-primary" onclick="loadTestMinistries()">
                                <i class="fas fa-refresh me-2"></i>
                                تحميل الوزارات
                            </button>

                            <button type="button" class="btn btn-info" onclick="checkAllData()">
                                فحص جميع البيانات
                            </button>
                        </div>

                        <div class="col-md-6">
                            <h5>نتائج الاختبار</h5>
                            <div id="debugOutput" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto;">
                                جاهز للاختبار...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function log(message) {
        const output = document.getElementById('debugOutput');
        const timestamp = new Date().toLocaleTimeString();
        output.textContent += `[${timestamp}] ${message}\n`;
        output.scrollTop = output.scrollHeight;
        console.log(message);
    }

    // تحميل الوزارات
    async function loadTestMinistries() {
        const ministrySelect = document.getElementById('testMinistrySelect');

        log('جاري تحميل الوزارات...');

        try {
            const response = await fetch('/Constants/GetMinistries');
            log(`حالة الاستجابة: ${response.status}`);

            if (response.ok) {
                const ministries = await response.json();
                log(`تم استلام الوزارات: ${JSON.stringify(ministries, null, 2)}`);

                ministrySelect.innerHTML = '<option value="">اختر الوزارة</option>';

                if (ministries && ministries.length > 0) {
                    ministries.forEach(ministry => {
                        const option = document.createElement('option');
                        option.value = ministry.id;
                        option.textContent = ministry.name;
                        ministrySelect.appendChild(option);
                    });
                    log(`تم إضافة ${ministries.length} وزارة بنجاح`);
                } else {
                    log('لا توجد وزارات في قاعدة البيانات');
                }
            } else {
                const errorText = await response.text();
                log(`خطأ في الاستجابة: ${errorText}`);
            }
        } catch (error) {
            log(`حدث خطأ: ${error.message}`);
            log(`تفاصيل الخطأ: ${error.stack}`);
        }
    }

    // تحميل الوكالات
    async function testLoadAgencies() {
        const ministryId = document.getElementById('testMinistrySelect').value;
        const agencySelect = document.getElementById('testAgencySelect');
        const directorateSelect = document.getElementById('testDirectorateSelect');
        const departmentSelect = document.getElementById('testGovernmentDepartmentSelect');
        const divisionSelect = document.getElementById('testDivisionSelect');

        log(`اختبار تحميل الوكالات للوزارة: ${ministryId}`);

        // إعادة تعيين جميع المستويات التابعة
        agencySelect.innerHTML = '<option value="">اختر الوكالة</option>';
        directorateSelect.innerHTML = '<option value="">اختر المديرية</option>';
        departmentSelect.innerHTML = '<option value="">اختر القسم الحكومي</option>';
        divisionSelect.innerHTML = '<option value="">اختر الشعبة</option>';

        agencySelect.disabled = true;
        directorateSelect.disabled = true;
        departmentSelect.disabled = true;
        divisionSelect.disabled = true;

        if (ministryId) {
            try {
                const url = `/Constants/GetAgenciesByMinistry/${ministryId}`;
                log(`جلب البيانات من: ${url}`);

                const response = await fetch(url);
                log(`حالة الاستجابة: ${response.status}`);

                if (response.ok) {
                    const responseText = await response.text();
                    log(`الاستجابة الخام: ${responseText}`);

                    let agencies;
                    try {
                        agencies = JSON.parse(responseText);
                    } catch (parseError) {
                        log(`خطأ في تحليل JSON: ${parseError.message}`);
                        return;
                    }

                    log(`الوكالات المستلمة: ${JSON.stringify(agencies, null, 2)}`);

                    if (agencies && agencies.length > 0) {
                        agencies.forEach(agency => {
                            const option = document.createElement('option');
                            option.value = agency.id;
                            option.textContent = agency.name;
                            agencySelect.appendChild(option);
                        });
                        agencySelect.disabled = false;
                        log(`تم إضافة ${agencies.length} وكالة بنجاح`);
                    } else {
                        log('لا توجد وكالات لهذه الوزارة');
                    }
                } else {
                    const errorText = await response.text();
                    log(`خطأ في الاستجابة: ${errorText}`);
                }
            } catch (error) {
                log(`حدث خطأ: ${error.message}`);
                log(`تفاصيل الخطأ: ${error.stack}`);
            }
        } else {
            log('لم يتم اختيار وزارة');
        }
    }

    // تحميل المديريات
    async function testLoadDirectorates() {
        const agencyId = document.getElementById('testAgencySelect').value;
        const directorateSelect = document.getElementById('testDirectorateSelect');
        const departmentSelect = document.getElementById('testGovernmentDepartmentSelect');
        const divisionSelect = document.getElementById('testDivisionSelect');

        log(`اختبار تحميل المديريات للوكالة: ${agencyId}`);

        directorateSelect.innerHTML = '<option value="">اختر المديرية</option>';
        departmentSelect.innerHTML = '<option value="">اختر القسم الحكومي</option>';
        divisionSelect.innerHTML = '<option value="">اختر الشعبة</option>';

        directorateSelect.disabled = true;
        departmentSelect.disabled = true;
        divisionSelect.disabled = true;

        if (agencyId) {
            try {
                const url = `/Constants/GetDirectoratesByAgency/${agencyId}`;
                log(`جلب البيانات من: ${url}`);

                const response = await fetch(url);
                log(`حالة الاستجابة: ${response.status}`);

                if (response.ok) {
                    const directorates = await response.json();
                    log(`المديريات المستلمة: ${JSON.stringify(directorates, null, 2)}`);

                    if (directorates && directorates.length > 0) {
                        directorates.forEach(directorate => {
                            const option = document.createElement('option');
                            option.value = directorate.id;
                            option.textContent = directorate.name;
                            directorateSelect.appendChild(option);
                        });
                        directorateSelect.disabled = false;
                        log(`تم إضافة ${directorates.length} مديرية بنجاح`);
                    } else {
                        log('لا توجد مديريات لهذه الوكالة');
                    }
                } else {
                    const errorText = await response.text();
                    log(`خطأ في الاستجابة: ${errorText}`);
                }
            } catch (error) {
                log(`حدث خطأ: ${error.message}`);
            }
        } else {
            log('لم يتم اختيار وكالة');
        }
    }

    // تحميل الأقسام الحكومية
    async function testLoadGovernmentDepartments() {
        const directorateId = document.getElementById('testDirectorateSelect').value;
        const departmentSelect = document.getElementById('testGovernmentDepartmentSelect');
        const divisionSelect = document.getElementById('testDivisionSelect');

        log(`اختبار تحميل الأقسام الحكومية للمديرية: ${directorateId}`);

        departmentSelect.innerHTML = '<option value="">اختر القسم الحكومي</option>';
        divisionSelect.innerHTML = '<option value="">اختر الشعبة</option>';

        departmentSelect.disabled = true;
        divisionSelect.disabled = true;

        if (directorateId) {
            try {
                const url = `/Constants/GetGovernmentDepartmentsByDirectorate/${directorateId}`;
                log(`جلب البيانات من: ${url}`);

                const response = await fetch(url);
                log(`حالة الاستجابة: ${response.status}`);

                if (response.ok) {
                    const departments = await response.json();
                    log(`الأقسام الحكومية المستلمة: ${JSON.stringify(departments, null, 2)}`);

                    if (departments && departments.length > 0) {
                        departments.forEach(department => {
                            const option = document.createElement('option');
                            option.value = department.id;
                            option.textContent = department.name;
                            departmentSelect.appendChild(option);
                        });
                        departmentSelect.disabled = false;
                        log(`تم إضافة ${departments.length} قسم حكومي بنجاح`);
                    } else {
                        log('لا توجد أقسام حكومية لهذه المديرية');
                    }
                } else {
                    const errorText = await response.text();
                    log(`خطأ في الاستجابة: ${errorText}`);
                }
            } catch (error) {
                log(`حدث خطأ: ${error.message}`);
            }
        } else {
            log('لم يتم اختيار مديرية');
        }
    }

    // تحميل الشعب
    async function testLoadDivisions() {
        const departmentId = document.getElementById('testGovernmentDepartmentSelect').value;
        const divisionSelect = document.getElementById('testDivisionSelect');

        log(`اختبار تحميل الشعب للقسم الحكومي: ${departmentId}`);

        divisionSelect.innerHTML = '<option value="">اختر الشعبة</option>';
        divisionSelect.disabled = true;

        if (departmentId) {
            try {
                const url = `/Constants/GetDivisionsByGovernmentDepartment/${departmentId}`;
                log(`جلب البيانات من: ${url}`);

                const response = await fetch(url);
                log(`حالة الاستجابة: ${response.status}`);

                if (response.ok) {
                    const divisions = await response.json();
                    log(`الشعب المستلمة: ${JSON.stringify(divisions, null, 2)}`);

                    if (divisions && divisions.length > 0) {
                        divisions.forEach(division => {
                            const option = document.createElement('option');
                            option.value = division.id;
                            option.textContent = division.name;
                            divisionSelect.appendChild(option);
                        });
                        divisionSelect.disabled = false;
                        log(`تم إضافة ${divisions.length} شعبة بنجاح`);
                    } else {
                        log('لا توجد شعب لهذا القسم الحكومي');
                    }
                } else {
                    const errorText = await response.text();
                    log(`خطأ في الاستجابة: ${errorText}`);
                }
            } catch (error) {
                log(`حدث خطأ: ${error.message}`);
            }
        } else {
            log('لم يتم اختيار قسم حكومي');
        }
    }

    async function checkAllData() {
        log('=== فحص جميع البيانات ===');

        try {
            log('فحص البيانات...');
            const response = await fetch('/DataSeed/CheckData');
            if (response.ok) {
                const data = await response.json();
                log(`الجامعات: ${data.universities}`);
                log(`المعاهد: ${data.institutes}`);
                log(`الكليات: ${data.colleges}`);
                log(`الأقسام: ${data.departments}`);
                log(`الوزارات: ${data.ministries}`);
                log(`الوكالات: ${data.agencies}`);
                log(`المديريات: ${data.directorates}`);
                log(`الأقسام الحكومية: ${data.governmentDepartments}`);
                log(`الشعب: ${data.divisions}`);

                if (data.ministries === 0) {
                    log('⚠️ لا توجد وزارات في قاعدة البيانات!');
                    log('يرجى إضافة البيانات الحكومية أولاً');
                }

                if (data.agencies === 0) {
                    log('⚠️ لا توجد وكالات في قاعدة البيانات!');
                }
            } else {
                log('فشل في جلب البيانات');
            }
        } catch (error) {
            log(`خطأ في فحص البيانات: ${error.message}`);
        }
    }

    // تحميل البيانات عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(() => {
            checkAllData();
            loadTestMinistries();
        }, 1000);
    });
</script>
