@model EmpMgr.Models.University

@{
    ViewData["Title"] = "تعديل الجامعة";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الجامعة
                </h2>
                <a asp-action="Universities" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للجامعات
                </a>
            </div>

            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-university me-2"></i>
                        تعديل بيانات الجامعة: @Model.Name
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="EditUniversity" method="post" id="universityForm" novalidate>
                        <input asp-for="Id" type="hidden" />
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label asp-for="Name" class="form-label required"></label>
                                <input asp-for="Name" class="form-control" placeholder="أدخل اسم الجامعة" required />
                                <span asp-validation-for="Name" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    أدخل الاسم الكامل للجامعة (مثال: جامعة بغداد، الجامعة التكنولوجية)
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label asp-for="Type" class="form-label required"></label>
                                <select asp-for="Type" class="form-select" required>
                                    <option value="">اختر نوع الجامعة</option>
                                    <option value="@((int)EmpMgr.Models.UniversityType.Government)">حكومية</option>
                                    <option value="@((int)EmpMgr.Models.UniversityType.Private)">أهلية</option>
                                    <option value="@((int)EmpMgr.Models.UniversityType.Technical)">تقنية</option>
                                </select>
                                <span asp-validation-for="Type" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    حدد نوع الجامعة
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label asp-for="Location" class="form-label"></label>
                                <input asp-for="Location" class="form-control" placeholder="موقع الجامعة (اختياري)" />
                                <span asp-validation-for="Location" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    أدخل موقع الجامعة أو المحافظة (اختياري)
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsActive" class="form-check-label">
                                        الجامعة نشطة
                                    </label>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    الجامعات النشطة فقط تظهر في قوائم الاختيار
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية عن الجامعة -->
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>
                                        معلومات الجامعة
                                    </h6>
                                    <p class="mb-1">
                                        <strong>عدد الموظفين المرتبطين:</strong> 
                                        <span class="badge bg-primary">@Model.Employees.Count</span>
                                    </p>
                                    <p class="mb-1">
                                        <strong>نوع الجامعة:</strong> 
                                        @switch (Model.Type)
                                        {
                                            case EmpMgr.Models.UniversityType.Government:
                                                <span class="badge bg-success">حكومية</span>
                                                break;
                                            case EmpMgr.Models.UniversityType.Private:
                                                <span class="badge bg-warning text-dark">أهلية</span>
                                                break;
                                            case EmpMgr.Models.UniversityType.Technical:
                                                <span class="badge bg-info">تقنية</span>
                                                break;
                                        }
                                    </p>
                                    @if (Model.Employees.Count > 0)
                                    {
                                        <p class="mb-0 text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            تنبيه: يوجد موظفين مرتبطين بهذه الجامعة. تأكد من صحة التعديلات.
                                        </p>
                                    }
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-warning btn-lg text-dark">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التعديلات
                                </button>
                                <a asp-action="Universities" class="btn btn-outline-secondary btn-lg ms-2">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                            <div>
                                @if (Model.Employees.Count == 0)
                                {
                                    <button type="button" class="btn btn-outline-danger btn-lg" 
                                            onclick="confirmDelete(@Model.Id, '@Model.Name')">
                                        <i class="fas fa-trash me-2"></i>
                                        حذف الجامعة
                                    </button>
                                }
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- نصائح -->
            <div class="card mt-4 border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح التعديل
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تأكد من صحة اسم الجامعة ونوعها قبل الحفظ
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            إلغاء تفعيل الجامعة سيخفيها من قوائم الاختيار للموظفين الجدد
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            لا يمكن حذف الجامعة إذا كان هناك موظفين مرتبطين بها
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للتأكيد من الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الجامعة <strong id="universityName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    هذا الإجراء لا يمكن التراجع عنه
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" class="d-inline">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من صحة النموذج
            const form = document.getElementById('universityForm');
            
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });

            // تنظيف المدخلات
            const nameInput = document.getElementById('Name');
            const locationInput = document.getElementById('Location');

            // تنظيف حقول النص يتم بواسطة form-fixes.js العام

            // إصلاح زر المسافة يتم بواسطة form-fixes.js العام
        });

        function confirmDelete(universityId, universityName) {
            document.getElementById('universityName').textContent = universityName;
            document.getElementById('deleteForm').action = '@Url.Action("DeleteUniversity")/' + universityId;
            
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: #dc3545;
        }

        .form-control:focus, .form-select:focus {
            border-color: #ffc107;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }

        .card {
            border: none;
            border-radius: 15px;
        }

        .card-header {
            border-radius: 15px 15px 0 0 !important;
        }

        .btn {
            border-radius: 10px;
        }

        .form-check-input:checked {
            background-color: #ffc107;
            border-color: #ffc107;
        }
    </style>
}
