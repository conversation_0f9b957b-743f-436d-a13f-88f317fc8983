@model IEnumerable<EmpMgr.Models.Rank>
@{
    ViewData["Title"] = "إدارة الرتب";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-medal me-2"></i>
                            إدارة الرتب
                        </h4>
                        <div>
                            <a asp-action="CreateRank" class="btn btn-light">
                                <i class="fas fa-plus me-2"></i>
                                إضافة رتبة جديدة
                            </a>
                            <a asp-action="Index" class="btn btn-outline-light">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للثوابت
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }
                    
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الترتيب</th>
                                    <th>اسم الرتبة</th>
                                    <th>مدة الرتبة</th>

                                    <th>متطلبات إضافية</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var rank in Model)
                                {
                                    <tr>
                                        <td>
                                            <span class="badge bg-secondary">@rank.Order</span>
                                        </td>
                                        <td>
                                            <strong>@rank.Name</strong>
                                            @if (!string.IsNullOrEmpty(rank.Description))
                                            {
                                                <br><small class="text-muted">@rank.Description</small>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-clock me-1"></i>
                                                @rank.RankDurationYears سنة
                                            </span>
                                        </td>

                                        <td>
                                            <div class="d-flex flex-wrap gap-1">
                                                @if (rank.RequiresTrainingCourses && rank.RequiredCoursesCount > 0)
                                                {
                                                    <span class="badge bg-success" title="دورات تدريبية مطلوبة">
                                                        <i class="fas fa-graduation-cap me-1"></i>
                                                        @rank.RequiredCoursesCount دورة
                                                    </span>
                                                }
                                                @if (rank.RequiresPerformanceEvaluation && rank.MinPerformanceScore.HasValue)
                                                {
                                                    <span class="badge bg-warning text-dark" title="تقييم أداء مطلوب">
                                                        <i class="fas fa-star me-1"></i>
                                                        @rank.MinPerformanceScore%
                                                    </span>
                                                }
                                                @if (rank.MinAgeForPromotion.HasValue || rank.MaxAgeForPromotion.HasValue)
                                                {
                                                    <span class="badge bg-primary" title="قيود العمر">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        @if (rank.MinAgeForPromotion.HasValue && rank.MaxAgeForPromotion.HasValue)
                                                        {
                                                            <text>@<EMAIL> سنة</text>
                                                        }
                                                        else if (rank.MinAgeForPromotion.HasValue)
                                                        {
                                                            <text>+@rank.MinAgeForPromotion سنة</text>
                                                        }
                                                        else if (rank.MaxAgeForPromotion.HasValue)
                                                        {
                                                            <text>-@rank.MaxAgeForPromotion سنة</text>
                                                        }
                                                    </span>
                                                }
                                                @if (!rank.RequiresTrainingCourses && !rank.RequiresPerformanceEvaluation && !rank.MinAgeForPromotion.HasValue && !rank.MaxAgeForPromotion.HasValue)
                                                {
                                                    <span class="text-muted">
                                                        <i class="fas fa-minus"></i>
                                                        لا توجد
                                                    </span>
                                                }
                                            </div>
                                        </td>
                                        <td>
                                            @if (rank.IsActive)
                                            {
                                                <span class="badge bg-success">نشط</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">غير نشط</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="EditRank" asp-route-id="@rank.Id" 
                                                   class="btn btn-outline-warning btn-sm" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                                        title="حذف" onclick="confirmDelete(@rank.Id, '@rank.Name')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    
                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-medal fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد رتب مسجلة</h5>
                            <p class="text-muted">ابدأ بإضافة رتبة جديدة للنظام</p>
                            <a asp-action="CreateRank" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة رتبة جديدة
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد الحذف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الرتبة <strong id="rankName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-warning me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(rankId, rankName) {
            document.getElementById('rankName').textContent = rankName;
            document.getElementById('deleteForm').action = '@Url.Action("DeleteRank")/' + rankId;
            
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }
    </script>
}

<style>
    .table th {
        white-space: nowrap;
        vertical-align: middle;
    }

    .table td {
        vertical-align: middle;
    }

    .badge {
        font-size: 0.75em;
    }

    .table-responsive {
        border-radius: 0.375rem;
    }

    .promotion-requirements {
        max-width: 200px;
    }

    .promotion-requirements .badge {
        margin: 1px;
        font-size: 0.7em;
    }
</style>
