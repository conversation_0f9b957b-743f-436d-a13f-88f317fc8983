using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmpMgr.Models
{
    public class Employee
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "رقم الموظف")]
        public string EmployeeNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "الرقم الإحصائي")]
        [RegularExpression(@"^\d+$", ErrorMessage = "الرقم الإحصائي يجب أن يحتوي على أرقام فقط")]
        public string StatisticalNumber { get; set; } = string.Empty;



        // البيانات الشخصية
        [Required]
        [Display(Name = "الاسم")]
        [RegularExpression(@"^[\u0600-\u06FF\s]+$", ErrorMessage = "الاسم يجب أن يحتوي على حروف عربية فقط")]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "اسم الأب")]
        [RegularExpression(@"^[\u0600-\u06FF\s]+$", ErrorMessage = "اسم الأب يجب أن يحتوي على حروف عربية فقط")]
        public string FatherName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "اسم الجد")]
        [RegularExpression(@"^[\u0600-\u06FF\s]+$", ErrorMessage = "اسم الجد يجب أن يحتوي على حروف عربية فقط")]
        public string GrandFatherName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "اسم والد الجد")]
        [RegularExpression(@"^[\u0600-\u06FF\s]+$", ErrorMessage = "اسم والد الجد يجب أن يحتوي على حروف عربية فقط")]
        public string GreatGrandFatherName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "اللقب")]
        [RegularExpression(@"^[\u0600-\u06FF\s]+$", ErrorMessage = "اللقب يجب أن يحتوي على حروف عربية فقط")]
        public string LastName { get; set; } = string.Empty;

        [NotMapped]
        [Display(Name = "الاسم الكامل")]
        public string FullName => $"{FirstName} {FatherName} {GrandFatherName} {GreatGrandFatherName} {LastName}";

        // الصورة الشخصية
        [Display(Name = "الصورة الشخصية")]
        public byte[]? Photo { get; set; }

        [Display(Name = "نوع الصورة")]
        [StringLength(50)]
        public string? PhotoContentType { get; set; }

        [Display(Name = "اسم ملف الصورة")]
        [StringLength(255)]
        public string? PhotoFileName { get; set; }

        [Display(Name = "تاريخ رفع الصورة")]
        public DateTime? PhotoUploadDate { get; set; }

        // خاصية مساعدة للتحقق من وجود صورة
        [NotMapped]
        public bool HasPhoto => Photo != null && Photo.Length > 0;

        // الرتبة
        [Required]
        [Display(Name = "الرتبة")]
        public int RankId { get; set; }
        [ForeignKey("RankId")]
        public virtual Rank? Rank { get; set; }

        // البيانات الشخصية الأخرى
        [Required]
        [Display(Name = "الحالة الاجتماعية")]
        public MaritalStatus MaritalStatus { get; set; }

        [Required]
        [Display(Name = "الجنس")]
        public Gender Gender { get; set; }

        [Required]
        [Display(Name = "فصيلة الدم")]
        public BloodType BloodType { get; set; }

        [Required]
        [Display(Name = "الحالة الصحية")]
        public HealthStatus HealthStatus { get; set; }

        [Required]
        [Display(Name = "تاريخ الولادة")]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [Required]
        [Display(Name = "محل الولادة")]
        [StringLength(200, ErrorMessage = "محل الولادة يجب أن يكون أقل من 200 حرف")]
        public string PlaceOfBirth { get; set; } = string.Empty;

        // معلومات الاتصال والعنوان
        [Required]
        [Display(Name = "المحافظة")]
        public int ProvinceId { get; set; }
        [ForeignKey("ProvinceId")]
        public virtual Province? Province { get; set; }

        [Display(Name = "القضاء")]
        [StringLength(200, ErrorMessage = "القضاء يجب أن يكون أقل من 200 حرف")]
        public string? District { get; set; }

        [Display(Name = "الناحية")]
        [StringLength(200, ErrorMessage = "الناحية يجب أن تكون أقل من 200 حرف")]
        public string? Subdistrict { get; set; }

        [Display(Name = "القرية")]
        [StringLength(200, ErrorMessage = "القرية يجب أن تكون أقل من 200 حرف")]
        public string? Village { get; set; }

        [Display(Name = "الحي")]
        [StringLength(200, ErrorMessage = "الحي يجب أن يكون أقل من 200 حرف")]
        public string? Neighborhood { get; set; }

        [Display(Name = "المحلة")]
        [StringLength(200, ErrorMessage = "المحلة يجب أن تكون أقل من 200 حرف")]
        public string? Quarter { get; set; }

        [Display(Name = "الزقاق")]
        [StringLength(200, ErrorMessage = "الزقاق يجب أن يكون أقل من 200 حرف")]
        public string? Alley { get; set; }

        [Display(Name = "الدار")]
        [StringLength(200, ErrorMessage = "الدار يجب أن تكون أقل من 200 حرف")]
        public string? House { get; set; }

        [Display(Name = "أقرب نقطة دالة")]
        [StringLength(500, ErrorMessage = "أقرب نقطة دالة يجب أن تكون أقل من 500 حرف")]
        public string? NearestLandmark { get; set; }

        [NotMapped]
        [Display(Name = "العنوان الكامل")]
        public string FullAddress
        {
            get
            {
                var addressParts = new List<string>();
                if (Province != null) addressParts.Add($"محافظة {Province.Name}");
                if (!string.IsNullOrEmpty(District)) addressParts.Add($"قضاء {District}");
                if (!string.IsNullOrEmpty(Subdistrict)) addressParts.Add($"ناحية {Subdistrict}");
                if (!string.IsNullOrEmpty(Village)) addressParts.Add($"قرية {Village}");
                if (!string.IsNullOrEmpty(Neighborhood)) addressParts.Add($"حي {Neighborhood}");
                if (!string.IsNullOrEmpty(Quarter)) addressParts.Add($"محلة {Quarter}");
                if (!string.IsNullOrEmpty(Alley)) addressParts.Add($"زقاق {Alley}");
                if (!string.IsNullOrEmpty(House)) addressParts.Add($"دار {House}");
                if (!string.IsNullOrEmpty(NearestLandmark)) addressParts.Add($"قرب {NearestLandmark}");
                
                return string.Join(" - ", addressParts);
            }
        }

        // البيانات الوظيفية
        [Display(Name = "تاريخ التعيين")]
        [DataType(DataType.Date)]
        public DateTime AppointmentDate { get; set; } = DateTime.Today;

        [Display(Name = "تاريخ آخر ترقية")]
        [DataType(DataType.Date)]
        public DateTime? LastPromotionDate { get; set; }

        [Display(Name = "الرتبة الحالية")]
        public int? CurrentRankId { get; set; }
        [ForeignKey("CurrentRankId")]
        public virtual Rank? CurrentRank { get; set; }

        [Required]
        [Display(Name = "التحصيل الدراسي")]
        public EducationLevel EducationLevel { get; set; }

        [Display(Name = "الجامعة")]
        public int? UniversityId { get; set; }
        [ForeignKey("UniversityId")]
        public virtual University? University { get; set; }

        [Display(Name = "الكلية")]
        public int? CollegeId { get; set; }
        [ForeignKey("CollegeId")]
        public virtual College? College { get; set; }

        [Display(Name = "القسم الأكاديمي")]
        public int? AcademicDepartmentId { get; set; }
        [ForeignKey("AcademicDepartmentId")]
        public virtual AcademicDepartment? AcademicDepartment { get; set; }

        [Display(Name = "المعهد")]
        public int? InstituteId { get; set; }
        [ForeignKey("InstituteId")]
        public virtual Institute? Institute { get; set; }

        [Display(Name = "القسم")]
        public int? DepartmentId { get; set; }
        [ForeignKey("DepartmentId")]
        public virtual Department? Department { get; set; }

        [Display(Name = "التخصص")]
        [StringLength(100, ErrorMessage = "التخصص يجب أن يكون أقل من 100 حرف")]
        public string? Specialization { get; set; }

        // البنية الهرمية الحكومية
        [Display(Name = "الوزارة")]
        public int? MinistryId { get; set; }
        [ForeignKey("MinistryId")]
        public virtual Ministry? Ministry { get; set; }

        [Display(Name = "الوكالة")]
        public int? AgencyId { get; set; }
        [ForeignKey("AgencyId")]
        public virtual Agency? Agency { get; set; }

        [Display(Name = "المديرية")]
        public int? DirectorateId { get; set; }
        [ForeignKey("DirectorateId")]
        public virtual Directorate? Directorate { get; set; }

        [Display(Name = "القسم الحكومي")]
        public int? GovernmentDepartmentId { get; set; }
        [ForeignKey("GovernmentDepartmentId")]
        public virtual GovernmentDepartment? GovernmentDepartment { get; set; }

        [Display(Name = "الشعبة")]
        public int? DivisionId { get; set; }
        [ForeignKey("DivisionId")]
        public virtual Division? Division { get; set; }



        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedDate { get; set; }

        // خصائص محسوبة للترقيات
        [Display(Name = "تاريخ استحقاق الترقية القادمة")]
        [DataType(DataType.Date)]
        public DateTime? NextPromotionEligibilityDate { get; set; }

        [Display(Name = "تاريخ استحقاق الترقية (محسوب)")]
        [DataType(DataType.Date)]
        public DateTime? CalculatedPromotionDate { get; set; }

        [Display(Name = "عدد الأيام حتى الترقية")]
        public int? DaysUntilPromotion { get; set; }

        [Display(Name = "مؤهل للترقية")]
        public bool IsEligibleForPromotion { get; set; } = false;

        [Display(Name = "ملاحظات الترقية")]
        [StringLength(500, ErrorMessage = "ملاحظات الترقية يجب أن تكون أقل من 500 حرف")]
        public string? PromotionNotes { get; set; }

        [Display(Name = "حالة الترقية")]
        [StringLength(100, ErrorMessage = "حالة الترقية يجب أن تكون أقل من 100 حرف")]
        public string? PromotionStatus { get; set; }
    }

    // التعدادات (Enums)
    public enum MaritalStatus
    {
        [Display(Name = "أعزب")]
        Single = 1,
        [Display(Name = "متزوج")]
        Married = 2,
        [Display(Name = "مطلق")]
        Divorced = 3,
        [Display(Name = "أرمل")]
        Widowed = 4
    }

    public enum Gender
    {
        [Display(Name = "ذكر")]
        Male = 1,
        [Display(Name = "أنثى")]
        Female = 2
    }

    public enum BloodType
    {
        [Display(Name = "A+")]
        APositive = 1,
        [Display(Name = "A-")]
        ANegative = 2,
        [Display(Name = "B+")]
        BPositive = 3,
        [Display(Name = "B-")]
        BNegative = 4,
        [Display(Name = "AB+")]
        ABPositive = 5,
        [Display(Name = "AB-")]
        ABNegative = 6,
        [Display(Name = "O+")]
        OPositive = 7,
        [Display(Name = "O-")]
        ONegative = 8
    }

    public enum HealthStatus
    {
        [Display(Name = "سليم")]
        Healthy = 1,
        [Display(Name = "مريض")]
        Sick = 2,
        [Display(Name = "معاق")]
        Disabled = 3
    }

    public enum EducationLevel
    {
        [Display(Name = "يقرأ ويكتب")]
        Literate = 0,
        [Display(Name = "ابتدائية")]
        Primary = 1,
        [Display(Name = "متوسطة")]
        Intermediate = 2,
        [Display(Name = "إعدادية")]
        Secondary = 3,
        [Display(Name = "دبلوم")]
        Diploma = 4,
        [Display(Name = "بكالوريوس")]
        Bachelor = 5,
        [Display(Name = "ماجستير")]
        Master = 6,
        [Display(Name = "دكتوراه")]
        PhD = 7
    }
}
