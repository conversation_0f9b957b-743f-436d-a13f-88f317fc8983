// Loading indicators and UI improvements
class LoadingManager {
    constructor() {
        this.createLoadingOverlay();
        this.setupFormSubmitHandlers();
        this.setupAjaxHandlers();
    }

    createLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'loading-overlay';
        overlay.className = 'loading-overlay d-none';
        overlay.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <div class="loading-text mt-3">جاري التحميل...</div>
            </div>
        `;
        document.body.appendChild(overlay);
    }

    show(message = 'جاري التحميل...') {
        const overlay = document.getElementById('loading-overlay');
        const text = overlay.querySelector('.loading-text');
        text.textContent = message;
        overlay.classList.remove('d-none');
    }

    hide() {
        const overlay = document.getElementById('loading-overlay');
        overlay.classList.add('d-none');
    }

    setupFormSubmitHandlers() {
        document.addEventListener('submit', (e) => {
            if (e.target.tagName === 'FORM') {
                this.show('جاري الحفظ...');
            }
        });
    }

    setupAjaxHandlers() {
        // jQuery AJAX handlers if jQuery is available
        if (typeof $ !== 'undefined') {
            $(document).ajaxStart(() => this.show());
            $(document).ajaxStop(() => this.hide());
        }

        // Fetch API interceptor
        const originalFetch = window.fetch;
        window.fetch = (...args) => {
            this.show();
            return originalFetch(...args).finally(() => this.hide());
        };
    }
}

// Toast notifications
class ToastManager {
    constructor() {
        this.createToastContainer();
    }

    createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }

    show(message, type = 'success', duration = 5000) {
        const toastId = 'toast-' + Date.now();
        const iconClass = type === 'success' ? 'fa-check-circle' : 
                         type === 'error' ? 'fa-exclamation-circle' : 
                         type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';
        
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = `toast align-items-center text-bg-${type === 'error' ? 'danger' : type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas ${iconClass} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        document.getElementById('toast-container').appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast, { delay: duration });
        bsToast.show();

        // Remove toast element after it's hidden
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    success(message, duration = 5000) {
        this.show(message, 'success', duration);
    }

    error(message, duration = 7000) {
        this.show(message, 'error', duration);
    }

    warning(message, duration = 6000) {
        this.show(message, 'warning', duration);
    }

    info(message, duration = 5000) {
        this.show(message, 'info', duration);
    }
}

// Initialize managers when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.loadingManager = new LoadingManager();
    window.toastManager = new ToastManager();

    // Show success/error messages from TempData
    const successMessage = document.querySelector('[data-success-message]');
    if (successMessage) {
        window.toastManager.success(successMessage.dataset.successMessage);
    }

    const errorMessage = document.querySelector('[data-error-message]');
    if (errorMessage) {
        window.toastManager.error(errorMessage.dataset.errorMessage);
    }
});

// Utility functions
function showLoading(message) {
    if (window.loadingManager) {
        window.loadingManager.show(message);
    }
}

function hideLoading() {
    if (window.loadingManager) {
        window.loadingManager.hide();
    }
}

function showToast(message, type = 'success') {
    if (window.toastManager) {
        window.toastManager.show(message, type);
    }
}
