/* تحسينات النماذج */

/* تحسين مظهر حقول النص */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
    transform: translateY(-1px);
}

/* تحسين مظهر الحقول المطلوبة */
.form-control:required, .form-select:required {
    border-left: 4px solid #ffc107;
}

.form-control:required:valid, .form-select:required:valid {
    border-left: 4px solid #198754;
}

.form-control:required:invalid, .form-select:required:invalid {
    border-left: 4px solid #dc3545;
}

/* تحسين مظهر التسميات */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* تحسين مظهر النصوص المساعدة */
.form-text {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
}

.form-text i {
    margin-left: 0.25rem;
    color: #0d6efd;
}

/* تحسين مظهر رسائل الخطأ */
.text-danger {
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
    display: block;
}

.is-invalid {
    border-color: #dc3545 !important;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* تحسين مظهر الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

/* تأثير الموجة للأزرار */
.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: width 0.6s, height 0.6s;
    transform: translate(-50%, -50%);
    z-index: 0;
}

.btn:active::before {
    width: 300px;
    height: 300px;
}

.btn * {
    position: relative;
    z-index: 1;
}

/* تحسين مظهر البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border-bottom: none;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark, #0056b3) 100%);
}

.card-body {
    padding: 2rem;
}

/* تحسين مظهر القوائم المنسدلة المحسنة */
.enhanced-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 0.75rem center;
    background-size: 16px 12px;
    padding-left: 2.5rem;
}

/* تحسين مظهر مفاتيح التبديل */
.form-check-input {
    border-radius: 6px;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
    transform: scale(1.1);
}

.form-switch .form-check-input {
    border-radius: 2rem;
    width: 3rem;
    height: 1.5rem;
}

.form-switch .form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

/* تحسين مظهر مناطق النص */
textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

/* تحسين مظهر حقول الأرقام */
input[type="number"].form-control {
    text-align: center;
}

/* تحسين مظهر حقول التاريخ */
input[type="date"].form-control {
    position: relative;
}

/* تحسين مظهر مجموعات الأزرار */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-radius: 8px 0 0 8px;
}

.btn-group .btn:last-child {
    border-radius: 0 8px 8px 0;
}

/* تحسين مظهر التنبيهات */
.alert {
    border: none;
    border-radius: 12px;
    border-left: 4px solid;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    border-left-color: #198754;
    background: linear-gradient(135deg, #d1eddd 0%, #c3e6cb 100%);
}

.alert-danger {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.alert-warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.alert-info {
    border-left-color: #0dcaf0;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .form-label {
        font-size: 0.9rem;
    }
    
    .form-control, .form-select {
        font-size: 0.9rem;
    }
}

/* تحسين إمكانية الوصول */
.form-control:focus, .form-select:focus, .btn:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* تحسين مظهر الجداول في النماذج */
.table-responsive {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* تحسين مظهر الشارات */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

/* تحسين مظهر أشرطة التقدم */
.progress {
    border-radius: 10px;
    height: 8px;
    background-color: #f8f9fa;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}
