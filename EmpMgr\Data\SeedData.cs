using Microsoft.EntityFrameworkCore;
using EmpMgr.Models;

namespace EmpMgr.Data
{
    public static class SeedData
    {
        public static async Task SeedAsync(ApplicationDbContext context, bool seedSampleData = false)
        {
            // تأكد من إنشاء قاعدة البيانات
            await context.Database.EnsureCreatedAsync();

            // إذا لم يتم طلب إنشاء البيانات التجريبية، اخرج من الدالة
            if (!seedSampleData)
                return;

            // إضافة الرتب
            if (!context.Ranks.Any())
            {
                var ranks = new List<Rank>
                {
                    new Rank { Name = "فريق أول", Order = 1, IsActive = true },
                    new Rank { Name = "فريق", Order = 2, IsActive = true },
                    new Rank { Name = "عميد", Order = 3, IsActive = true },
                    new Rank { Name = "عقيد", Order = 4, IsActive = true },
                    new Rank { Name = "مقدم", Order = 5, IsActive = true },
                    new Rank { Name = "رائد", Order = 6, IsActive = true },
                    new Rank { Name = "نقيب", Order = 7, IsActive = true },
                    new Rank { Name = "ملازم أول", Order = 8, IsActive = true },
                    new Rank { Name = "ملازم", Order = 9, IsActive = true },
                    new Rank { Name = "وكيل ضابط أول", Order = 10, IsActive = true },
                    new Rank { Name = "وكيل ضابط", Order = 11, IsActive = true },
                    new Rank { Name = "رئيس عرفاء", Order = 12, IsActive = true },
                    new Rank { Name = "عريف أول", Order = 13, IsActive = true },
                    new Rank { Name = "عريف", Order = 14, IsActive = true },
                    new Rank { Name = "نائب عريف", Order = 15, IsActive = true },
                    new Rank { Name = "شرطي أول", Order = 16, IsActive = true },
                    new Rank { Name = "شرطي", Order = 17, IsActive = true }
                };

                context.Ranks.AddRange(ranks);
                await context.SaveChangesAsync();
            }

            // إضافة المحافظات مع فحص دقيق لتجنب التكرار
            try
            {
                var existingProvinces = await context.Provinces.Select(p => p.Name).ToListAsync();
                var provincesToAdd = new List<Province>();

                var allProvinces = new List<string>
                {
                    "بغداد", "البصرة", "نينوى", "أربيل", "النجف", "كربلاء", "بابل", "الأنبار",
                    "ديالى", "ذي قار", "المثنى", "القادسية", "ميسان", "واسط", "صلاح الدين",
                    "كركوك", "دهوك", "السليمانية"
                };

                foreach (var provinceName in allProvinces)
                {
                    if (!existingProvinces.Contains(provinceName))
                    {
                        // فحص إضافي للتأكد من عدم وجود المحافظة
                        var exists = await context.Provinces.AnyAsync(p => p.Name == provinceName);
                        if (!exists)
                        {
                            provincesToAdd.Add(new Province { Name = provinceName, IsActive = true });
                        }
                    }
                }

                if (provincesToAdd.Any())
                {
                    context.Provinces.AddRange(provincesToAdd);
                    await context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء المفاتيح المكررة
                if (!ex.Message.Contains("duplicate key"))
                {
                    throw;
                }
            }

            // إضافة الجامعات
            if (!context.Universities.Any())
            {
                var universities = new List<University>
                {
                    new University { Name = "جامعة بغداد", IsActive = true },
                    new University { Name = "الجامعة المستنصرية", IsActive = true },
                    new University { Name = "الجامعة التكنولوجية", IsActive = true },
                    new University { Name = "جامعة البصرة", IsActive = true },
                    new University { Name = "جامعة الموصل", IsActive = true },
                    new University { Name = "جامعة الكوفة", IsActive = true },
                    new University { Name = "جامعة كربلاء", IsActive = true },
                    new University { Name = "جامعة بابل", IsActive = true },
                    new University { Name = "جامعة الأنبار", IsActive = true },
                    new University { Name = "جامعة ديالى", IsActive = true },
                    new University { Name = "جامعة ذي قار", IsActive = true },
                    new University { Name = "جامعة المثنى", IsActive = true },
                    new University { Name = "جامعة القادسية", IsActive = true },
                    new University { Name = "جامعة ميسان", IsActive = true },
                    new University { Name = "جامعة واسط", IsActive = true },
                    new University { Name = "جامعة تكريت", IsActive = true },
                    new University { Name = "جامعة كركوك", IsActive = true },
                    new University { Name = "جامعة دهوك", IsActive = true },
                    new University { Name = "جامعة السليمانية", IsActive = true },
                    new University { Name = "جامعة كردستان", IsActive = true }
                };

                context.Universities.AddRange(universities);
                await context.SaveChangesAsync();
            }

            // إضافة المعاهد
            if (!context.Institutes.Any())
            {
                var institutes = new List<Institute>
                {
                    new Institute { Name = "معهد الإدارة التقني", IsActive = true },
                    new Institute { Name = "المعهد التقني الطبي", IsActive = true },
                    new Institute { Name = "المعهد التقني الهندسي", IsActive = true },
                    new Institute { Name = "معهد الفنون الجميلة", IsActive = true },
                    new Institute { Name = "معهد إعداد المعلمين", IsActive = true },
                    new Institute { Name = "معهد التدريب المهني", IsActive = true },
                    new Institute { Name = "المعهد العالي للدراسات المحاسبية", IsActive = true },
                    new Institute { Name = "معهد التخطيط العمراني", IsActive = true },
                    new Institute { Name = "المعهد التقني للحاسوب", IsActive = true },
                    new Institute { Name = "معهد التدريب النفطي", IsActive = true }
                };

                context.Institutes.AddRange(institutes);
                await context.SaveChangesAsync();
            }

            // إضافة الوزارات
            if (!context.Ministries.Any())
            {
                var ministries = new List<Ministry>
                {
                    new Ministry
                    {
                        Name = "وزارة الداخلية",
                        Code = "MOI",
                        Description = "وزارة الداخلية - مسؤولة عن الأمن الداخلي والشرطة",
                        EstablishedYear = 1921,
                        Location = "بغداد",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new Ministry
                    {
                        Name = "وزارة الدفاع",
                        Code = "MOD",
                        Description = "وزارة الدفاع - مسؤولة عن القوات المسلحة",
                        EstablishedYear = 1921,
                        Location = "بغداد",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new Ministry
                    {
                        Name = "وزارة التربية",
                        Code = "MOE",
                        Description = "وزارة التربية - مسؤولة عن التعليم العام",
                        EstablishedYear = 1921,
                        Location = "بغداد",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    }
                };

                context.Ministries.AddRange(ministries);
                await context.SaveChangesAsync();
            }

            // إضافة الوكالات
            if (!context.Agencies.Any())
            {
                var ministry = context.Ministries.First(m => m.Name == "وزارة الداخلية");

                var agencies = new List<Agency>
                {
                    new Agency
                    {
                        Name = "وكالة الوزارة لشؤون الشرطة",
                        Code = "POL",
                        Description = "وكالة مسؤولة عن شؤون الشرطة والأمن العام",
                        EstablishedYear = 2003,
                        MinistryId = ministry.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new Agency
                    {
                        Name = "وكالة الوزارة للشؤون الإدارية",
                        Code = "ADM",
                        Description = "وكالة مسؤولة عن الشؤون الإدارية والمالية",
                        EstablishedYear = 2003,
                        MinistryId = ministry.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new Agency
                    {
                        Name = "وكالة الوزارة للشؤون الأمنية",
                        Code = "SEC",
                        Description = "وكالة مسؤولة عن الشؤون الأمنية والاستخبارات",
                        EstablishedYear = 2003,
                        MinistryId = ministry.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    }
                };

                context.Agencies.AddRange(agencies);
                await context.SaveChangesAsync();
            }

            // إضافة المديريات
            if (!context.Directorates.Any())
            {
                var policeAgency = context.Agencies.First(a => a.Name == "وكالة الوزارة لشؤون الشرطة");
                var adminAgency = context.Agencies.First(a => a.Name == "وكالة الوزارة للشؤون الإدارية");

                var directorates = new List<Directorate>
                {
                    new Directorate
                    {
                        Name = "مديرية شرطة بغداد",
                        Code = "BGD-POL",
                        Description = "مديرية مسؤولة عن الأمن في محافظة بغداد",
                        EstablishedYear = 2003,
                        AgencyId = policeAgency.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new Directorate
                    {
                        Name = "مديرية شرطة البصرة",
                        Code = "BSR-POL",
                        Description = "مديرية مسؤولة عن الأمن في محافظة البصرة",
                        EstablishedYear = 2003,
                        AgencyId = policeAgency.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new Directorate
                    {
                        Name = "مديرية الموارد البشرية",
                        Code = "HR-DIR",
                        Description = "مديرية مسؤولة عن إدارة الموارد البشرية",
                        EstablishedYear = 2003,
                        AgencyId = adminAgency.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new Directorate
                    {
                        Name = "مديرية الشؤون المالية",
                        Code = "FIN-DIR",
                        Description = "مديرية مسؤولة عن الشؤون المالية والمحاسبية",
                        EstablishedYear = 2003,
                        AgencyId = adminAgency.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    }
                };

                context.Directorates.AddRange(directorates);
                await context.SaveChangesAsync();
            }

            // إضافة الأقسام الحكومية
            if (!context.GovernmentDepartments.Any())
            {
                var baghdadPolice = context.Directorates.First(d => d.Name == "مديرية شرطة بغداد");
                var hrDirectorate = context.Directorates.First(d => d.Name == "مديرية الموارد البشرية");

                var govDepartments = new List<GovernmentDepartment>
                {
                    new GovernmentDepartment
                    {
                        Name = "قسم الدوريات",
                        Code = "PATROL",
                        Description = "قسم مسؤول عن دوريات الشرطة",
                        EstablishedYear = 2003,
                        DirectorateId = baghdadPolice.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new GovernmentDepartment
                    {
                        Name = "قسم المرور",
                        Code = "TRAFFIC",
                        Description = "قسم مسؤول عن تنظيم المرور",
                        EstablishedYear = 2003,
                        DirectorateId = baghdadPolice.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new GovernmentDepartment
                    {
                        Name = "قسم التوظيف",
                        Code = "RECRUIT",
                        Description = "قسم مسؤول عن توظيف الموظفين الجدد",
                        EstablishedYear = 2003,
                        DirectorateId = hrDirectorate.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new GovernmentDepartment
                    {
                        Name = "قسم التدريب",
                        Code = "TRAIN",
                        Description = "قسم مسؤول عن تدريب الموظفين",
                        EstablishedYear = 2003,
                        DirectorateId = hrDirectorate.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    }
                };

                context.GovernmentDepartments.AddRange(govDepartments);
                await context.SaveChangesAsync();
            }

            // إضافة الشعب
            if (!context.Divisions.Any())
            {
                var patrolDept = context.GovernmentDepartments.First(gd => gd.Name == "قسم الدوريات");
                var recruitDept = context.GovernmentDepartments.First(gd => gd.Name == "قسم التوظيف");

                var divisions = new List<Division>
                {
                    new Division
                    {
                        Name = "شعبة الدوريات النهارية",
                        Code = "DAY-PAT",
                        Description = "شعبة مسؤولة عن الدوريات النهارية",
                        EstablishedYear = 2003,
                        GovernmentDepartmentId = patrolDept.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new Division
                    {
                        Name = "شعبة الدوريات الليلية",
                        Code = "NIGHT-PAT",
                        Description = "شعبة مسؤولة عن الدوريات الليلية",
                        EstablishedYear = 2003,
                        GovernmentDepartmentId = patrolDept.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new Division
                    {
                        Name = "شعبة فحص الطلبات",
                        Code = "APP-REV",
                        Description = "شعبة مسؤولة عن فحص طلبات التوظيف",
                        EstablishedYear = 2003,
                        GovernmentDepartmentId = recruitDept.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new Division
                    {
                        Name = "شعبة المقابلات",
                        Code = "INTERVIEW",
                        Description = "شعبة مسؤولة عن إجراء المقابلات",
                        EstablishedYear = 2003,
                        GovernmentDepartmentId = recruitDept.Id,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    }
                };

                context.Divisions.AddRange(divisions);
                await context.SaveChangesAsync();
            }
        }
    }
}
