@model IEnumerable<EmpMgr.Models.Employee>
@{
    ViewData["Title"] = "قائمة الموظفين";

    // تعريف رتب الضباط بناءً على الرتب الموجودة في SeedData
    var officerRanks = new[] { "فريق أول", "فريق", "عميد", "عقيد", "مقدم", "رائد", "نقيب", "ملازم أول", "ملازم" };
    int totalOfficers = Model.Count(e => e.Rank != null && officerRanks.Contains(e.Rank!.Name));
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            قائمة الموظفين
                        </h4>
                        <div class="employees-actions">
                            <div class="btn-group me-2" role="group">
                                <button type="button" class="btn btn-outline-light dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-download me-2"></i>
                                    تصدير
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" asp-action="Export" asp-route-format="excel">
                                        <i class="fas fa-file-excel me-2 text-success"></i>Excel
                                    </a></li>
                                    <li><a class="dropdown-item" asp-action="Export" asp-route-format="csv">
                                        <i class="fas fa-file-csv me-2 text-info"></i>CSV
                                    </a></li>
                                    <li><a class="dropdown-item" asp-action="Export" asp-route-format="json">
                                        <i class="fas fa-file-code me-2 text-warning"></i>JSON
                                    </a></li>
                                    <li><a class="dropdown-item" asp-action="Export" asp-route-format="pdf">
                                        <i class="fas fa-file-pdf me-2 text-danger"></i>PDF
                                    </a></li>
                                </ul>
                            </div>
                            <a asp-action="Import" class="btn btn-outline-light me-2">
                                <i class="fas fa-upload me-2"></i>
                                استيراد
                            </a>
                            <a asp-action="Create" class="btn btn-light">
                                <i class="fas fa-plus me-2"></i>
                                إضافة موظف جديد
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stat-card bg-primary">
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-content">
                                    <h3 id="totalEmployees">@Model.Count()</h3>
                                    <p>إجمالي الموظفين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card bg-success">
                                <div class="stat-icon">
                                    <i class="fas fa-user-tie"></i>
                                </div>
                                <div class="stat-content">
                                    <h3 id="totalOfficers">@totalOfficers</h3>
                                    <p>الضباط</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card bg-info">
                                <div class="stat-icon">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                <div class="stat-content">
                                    <h3 id="totalGraduates">@Model.Count(e => e.EducationLevel == EmpMgr.Models.EducationLevel.Bachelor || e.EducationLevel == EmpMgr.Models.EducationLevel.Master || e.EducationLevel == EmpMgr.Models.EducationLevel.PhD)</h3>
                                    <p>حملة الشهادات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card bg-warning">
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-content">
                                    <h3 id="recentEmployees">@Model.Count(e => e.CreatedDate >= DateTime.Now.AddDays(-30))</h3>
                                    <p>المضافين حديثاً</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلاتر البحث المحسنة -->
                    <div class="search-filters-container mb-4">
                        <!-- البحث الرئيسي -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="search-input-wrapper">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" class="form-control search-input" id="mainSearchInput"
                                           placeholder="البحث بالاسم، رقم الموظف، الرقم الإحصائي، الرتبة، أو المحافظة..."
                                           autocomplete="off">
                                    <button class="btn btn-clear" type="button" id="clearMainSearch" style="display: none;">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="search-suggestions" id="searchSuggestions"></div>
                            </div>
                        </div>

                        <!-- الفلاتر المتقدمة -->
                        <div class="filters-section">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">
                                    <i class="fas fa-filter me-2"></i>
                                    فلاتر متقدمة
                                </h6>
                                <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#advancedFilters" aria-expanded="false">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>

                            <div class="collapse" id="advancedFilters">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">الرتبة</label>
                                        <select class="form-select" id="rankFilter">
                                            <option value="">جميع الرتب</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">المحافظة</label>
                                        <select class="form-select" id="provinceFilter">
                                            <option value="">جميع المحافظات</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">المستوى التعليمي</label>
                                        <select class="form-select" id="educationFilter">
                                            <option value="">جميع المستويات</option>
                                            <option value="Primary">ابتدائية</option>
                                            <option value="Secondary">متوسطة</option>
                                            <option value="HighSchool">إعدادية</option>
                                            <option value="Diploma">دبلوم</option>
                                            <option value="Bachelor">بكالوريوس</option>
                                            <option value="Master">ماجستير</option>
                                            <option value="PhD">دكتوراه</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">الجنس</label>
                                        <select class="form-select" id="genderFilter">
                                            <option value="">الكل</option>
                                            <option value="Male">ذكر</option>
                                            <option value="Female">أنثى</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row g-3 mt-2">
                                    <div class="col-md-4">
                                        <label class="form-label">من تاريخ</label>
                                        <input type="date" class="form-control" id="dateFromFilter">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">إلى تاريخ</label>
                                        <input type="date" class="form-control" id="dateToFilter">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">الإجراءات</label>
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-primary" onclick="applyFilters()">
                                                <i class="fas fa-search me-1"></i>تطبيق
                                            </button>
                                            <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- شريط أدوات الجدول -->
                    <div class="table-toolbar mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="table-info">
                                <span class="text-muted">
                                    عرض <span id="currentCount">@Model.Count()</span> من أصل <span id="totalCount">@Model.Count()</span> موظف
                                </span>
                            </div>
                            <div class="table-actions">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                                        <i class="fas fa-check-square me-1"></i>تحديد الكل
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearSelection()">
                                        <i class="fas fa-square me-1"></i>إلغاء التحديد
                                    </button>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-info dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fas fa-sort me-1"></i>ترتيب
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="sortTable('name')">الاسم</a></li>
                                            <li><a class="dropdown-item" href="#" onclick="sortTable('employeeNumber')">رقم الموظف</a></li>
                                            <li><a class="dropdown-item" href="#" onclick="sortTable('rank')">الرتبة</a></li>
                                            <li><a class="dropdown-item" href="#" onclick="sortTable('date')">تاريخ الإضافة</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الموظفين المحسن -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="employeesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" class="form-check-input" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                    </th>
                                    <th width="60">الصورة</th>
                                    <th width="120" class="sortable" data-sort="employeeNumber">
                                        رقم الموظف
                                        <i class="fas fa-sort ms-1"></i>
                                    </th>
                                    <th width="120" class="sortable" data-sort="statisticalNumber">
                                        الرقم الإحصائي
                                        <i class="fas fa-sort ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="name">
                                        الاسم الكامل
                                        <i class="fas fa-sort ms-1"></i>
                                    </th>
                                    <th width="120" class="sortable" data-sort="rank">
                                        الرتبة
                                        <i class="fas fa-sort ms-1"></i>
                                    </th>
                                    <th width="120">المحافظة</th>
                                    <th width="120">التحصيل الدراسي</th>
                                    <th width="100" class="sortable" data-sort="date">
                                        تاريخ الإضافة
                                        <i class="fas fa-sort ms-1"></i>
                                    </th>
                                    <th width="150">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var employee in Model)
                                {
                                    <tr class="employee-row" data-employee-id="@employee.Id">
                                        <td>
                                            <input type="checkbox" class="form-check-input employee-checkbox" value="@employee.Id">
                                        </td>
                                        <td>
                                            <div class="employee-photo">
                                                @if (employee.HasPhoto)
                                                {
                                                    <img src="@Url.Action("GetEmployeePhoto", "Employee", new { id = employee.Id })"
                                                         alt="صورة @employee.FullName"
                                                         class="employee-avatar"
                                                         style="width: 45px; height: 45px;" />
                                                }
                                                else
                                                {
                                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center employee-avatar"
                                                         style="width: 45px; height: 45px;">
                                                        <i class="fas fa-user text-muted"></i>
                                                    </div>
                                                }
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary employee-number" title="رقم الموظف">
                                                @employee.EmployeeNumber
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info statistical-number" title="الرقم الإحصائي">
                                                @employee.StatisticalNumber
                                            </span>
                                        </td>
                                        <td>
                                            <div class="employee-name">
                                                <strong>@employee.FullName</strong>
                                                <small class="text-muted d-block">
                                                    @if (employee.Gender == EmpMgr.Models.Gender.Male)
                                                    {
                                                        <i class="fas fa-mars text-primary me-1"></i><text>ذكر</text>
                                                    }
                                                    else
                                                    {
                                                        <i class="fas fa-venus text-danger me-1"></i><text>أنثى</text>
                                                    }
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            @if (employee.Rank != null)
                                            {
                                                <span class="badge bg-success rank-badge" title="@employee.Rank.Description">
                                                    @employee.Rank.Name
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">غير محدد</span>
                                            }
                                        </td>
                                        <td>
                                            @if (employee.Province != null)
                                            {
                                                <span class="badge bg-outline-secondary province-badge">
                                                    @employee.Province.Name
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">غير محدد</span>
                                            }
                                        </td>
                                        <td>
                                            @switch (employee.EducationLevel)
                                            {
                                                case EducationLevel.Primary:
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-graduation-cap me-1"></i>ابتدائية
                                                    </span>
                                                    break;
                                                case EducationLevel.Intermediate:
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-graduation-cap me-1"></i>متوسطة
                                                    </span>
                                                    break;
                                                case EducationLevel.Secondary:
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-graduation-cap me-1"></i>إعدادية
                                                    </span>
                                                    break;
                                                case EducationLevel.Diploma:
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-certificate me-1"></i>دبلوم
                                                    </span>
                                                    break;
                                                case EducationLevel.Bachelor:
                                                    <span class="badge bg-primary">
                                                        <i class="fas fa-user-graduate me-1"></i>بكالوريوس
                                                    </span>
                                                    break;
                                                case EducationLevel.Master:
                                                    <span class="badge bg-info">
                                                        <i class="fas fa-medal me-1"></i>ماجستير
                                                    </span>
                                                    break;
                                                case EducationLevel.PhD:
                                                    <span class="badge bg-dark">
                                                        <i class="fas fa-crown me-1"></i>دكتوراه
                                                    </span>
                                                    break;
                                                default:
                                                    <span class="badge bg-light text-dark">غير محدد</span>
                                                    break;
                                            }
                                        </td>
                                        <td>
                                            <div class="date-info">
                                                <small class="text-muted d-block">
                                                    <i class="fas fa-calendar-plus me-1"></i>
                                                    @employee.CreatedDate.ToString("yyyy/MM/dd")
                                                </small>
                                                <small class="text-muted">
                                                    @{
                                                        var daysDiff = (DateTime.Now - employee.CreatedDate).Days;
                                                    }
                                                    @if (daysDiff == 0)
                                                    {
                                                        <span class="badge bg-success">اليوم</span>
                                                    }
                                                    else if (daysDiff <= 7)
                                                    {
                                                        <span class="badge bg-info">@daysDiff أيام</span>
                                                    }
                                                    else if (daysDiff <= 30)
                                                    {
                                                        <span class="badge bg-warning">@daysDiff يوم</span>
                                                    }
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@employee.Id"
                                                   class="btn btn-outline-info btn-sm" title="عرض التفاصيل"
                                                   data-bs-toggle="tooltip">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@employee.Id"
                                                   class="btn btn-outline-warning btn-sm" title="تعديل"
                                                   data-bs-toggle="tooltip">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle"
                                                            data-bs-toggle="dropdown" title="المزيد">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" href="#" onclick="printEmployee(@employee.Id)">
                                                                <i class="fas fa-print me-2"></i>طباعة
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" href="#" onclick="exportEmployee(@employee.Id)">
                                                                <i class="fas fa-download me-2"></i>تصدير
                                                            </a>
                                                        </li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <a class="dropdown-item text-danger" href="#"
                                                               onclick="confirmDelete(@employee.Id, '@employee.FullName')">
                                                                <i class="fas fa-trash me-2"></i>حذف
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    
                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد موظفين مسجلين</h5>
                            <p class="text-muted">ابدأ بإضافة موظف جديد للنظام</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة موظف جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد الحذف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الموظف <strong id="employeeName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-warning me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <input type="hidden" id="employeeIdToDelete" name="id" value="" />
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="~/css/employee-management.css" asp-append-version="true" />
}

@section Scripts {
    <script>
        // متغيرات عامة
        let currentSort = { field: 'date', direction: 'desc' };
        let selectedEmployees = [];
        let allEmployees = [];

        // تأكيد الحذف
        function confirmDelete(employeeId, employeeName) {
            document.getElementById('employeeName').textContent = employeeName;
            document.getElementById('deleteForm').action = '@Url.Action("Delete", "Employee")/' + employeeId;
            document.getElementById('employeeIdToDelete').value = employeeId;

            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        // البحث المتقدم
        function performAdvancedSearch() {
            const searchTerm = document.getElementById('mainSearchInput').value.toLowerCase();
            const rankFilter = document.getElementById('rankFilter').value;
            const provinceFilter = document.getElementById('provinceFilter').value;
            const educationFilter = document.getElementById('educationFilter').value;
            const genderFilter = document.getElementById('genderFilter').value;
            const dateFrom = document.getElementById('dateFromFilter').value;
            const dateTo = document.getElementById('dateToFilter').value;

            const table = document.getElementById('employeesTable');
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            let visibleCount = 0;

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.cells;

                // استخراج البيانات من الصف
                const employeeNumber = cells[2].textContent.toLowerCase();
                const statisticalNumber = cells[3].textContent.toLowerCase();
                const fullName = cells[4].textContent.toLowerCase();
                const rank = cells[5].textContent.toLowerCase();
                const province = cells[6].textContent.toLowerCase();
                const education = cells[7].textContent.toLowerCase();

                // تطبيق فلاتر البحث
                let showRow = true;

                // البحث النصي
                if (searchTerm) {
                    showRow = showRow && (
                        fullName.includes(searchTerm) ||
                        employeeNumber.includes(searchTerm) ||
                        statisticalNumber.includes(searchTerm) ||
                        rank.includes(searchTerm) ||
                        province.includes(searchTerm)
                    );
                }

                // فلتر الرتبة
                if (rankFilter && !rank.includes(rankFilter.toLowerCase())) {
                    showRow = false;
                }

                // فلتر المحافظة
                if (provinceFilter && !province.includes(provinceFilter.toLowerCase())) {
                    showRow = false;
                }

                // فلتر التعليم
                if (educationFilter && !education.includes(educationFilter.toLowerCase())) {
                    showRow = false;
                }

                row.style.display = showRow ? '' : 'none';
                if (showRow) visibleCount++;
            }

            // تحديث عداد النتائج
            document.getElementById('currentCount').textContent = visibleCount;

            // إظهار/إخفاء زر المسح
            const clearBtn = document.getElementById('clearMainSearch');
            clearBtn.style.display = searchTerm ? 'block' : 'none';
        }

        // تطبيق الفلاتر
        function applyFilters() {
            performAdvancedSearch();
        }

        // إعادة تعيين الفلاتر
        function resetFilters() {
            document.getElementById('mainSearchInput').value = '';
            document.getElementById('rankFilter').value = '';
            document.getElementById('provinceFilter').value = '';
            document.getElementById('educationFilter').value = '';
            document.getElementById('genderFilter').value = '';
            document.getElementById('dateFromFilter').value = '';
            document.getElementById('dateToFilter').value = '';

            performAdvancedSearch();
        }

        // ترتيب الجدول
        function sortTable(field) {
            if (currentSort.field === field) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.field = field;
                currentSort.direction = 'asc';
            }

            // تطبيق الترتيب
            const table = document.getElementById('employeesTable');
            const tbody = table.getElementsByTagName('tbody')[0];
            const rows = Array.from(tbody.getElementsByTagName('tr'));

            rows.sort((a, b) => {
                let aVal, bVal;

                switch (field) {
                    case 'name':
                        aVal = a.cells[4].textContent.trim();
                        bVal = b.cells[4].textContent.trim();
                        break;
                    case 'employeeNumber':
                        aVal = a.cells[2].textContent.trim();
                        bVal = b.cells[2].textContent.trim();
                        break;
                    case 'rank':
                        aVal = a.cells[5].textContent.trim();
                        bVal = b.cells[5].textContent.trim();
                        break;
                    case 'date':
                        aVal = new Date(a.cells[8].textContent.trim());
                        bVal = new Date(b.cells[8].textContent.trim());
                        break;
                    default:
                        return 0;
                }

                if (currentSort.direction === 'asc') {
                    return aVal > bVal ? 1 : -1;
                } else {
                    return aVal < bVal ? 1 : -1;
                }
            });

            // إعادة ترتيب الصفوف
            rows.forEach(row => tbody.appendChild(row));

            // تحديث أيقونات الترتيب
            updateSortIcons(field);
        }

        // تحديث أيقونات الترتيب
        function updateSortIcons(activeField) {
            const sortableHeaders = document.querySelectorAll('.sortable');
            sortableHeaders.forEach(header => {
                const icon = header.querySelector('i');
                const field = header.getAttribute('data-sort');

                if (field === activeField) {
                    icon.className = currentSort.direction === 'asc' ? 'fas fa-sort-up ms-1' : 'fas fa-sort-down ms-1';
                } else {
                    icon.className = 'fas fa-sort ms-1';
                }
            });
        }

        // تحديد/إلغاء تحديد الكل
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const employeeCheckboxes = document.querySelectorAll('.employee-checkbox');

            employeeCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
                toggleRowSelection(checkbox);
            });
        }

        // تحديد صف
        function toggleRowSelection(checkbox) {
            const row = checkbox.closest('tr');
            if (checkbox.checked) {
                row.classList.add('selected');
                selectedEmployees.push(checkbox.value);
            } else {
                row.classList.remove('selected');
                selectedEmployees = selectedEmployees.filter(id => id !== checkbox.value);
            }

            updateSelectionActions();
        }

        // تحديث إجراءات التحديد
        function updateSelectionActions() {
            const selectedCount = selectedEmployees.length;
            // يمكن إضافة إجراءات للعناصر المحددة هنا
        }



        // طباعة بيانات الموظف
        function printEmployee(employeeId) {
            window.open('@Url.Action("Print")/' + employeeId, '_blank');
        }

        // تصدير بيانات الموظف
        function exportEmployee(employeeId) {
            window.location.href = '@Url.Action("ExportSingle")/' + employeeId;
        }

        // تحميل البيانات للفلاتر
        function loadFilterData() {
            // تحميل الرتب
            fetch('@Url.Action("GetRanks", "Constants")')
                .then(response => response.json())
                .then(data => {
                    const rankSelect = document.getElementById('rankFilter');
                    data.forEach(rank => {
                        const option = document.createElement('option');
                        option.value = rank.name;
                        option.textContent = rank.name;
                        rankSelect.appendChild(option);
                    });
                });

            // تحميل المحافظات
            fetch('@Url.Action("GetProvinces", "Constants")')
                .then(response => response.json())
                .then(data => {
                    const provinceSelect = document.getElementById('provinceFilter');
                    data.forEach(province => {
                        const option = document.createElement('option');
                        option.value = province.name;
                        option.textContent = province.name;
                        provinceSelect.appendChild(option);
                    });
                });
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل بيانات الفلاتر
            loadFilterData();

            // إضافة مستمعي الأحداث
            document.getElementById('mainSearchInput').addEventListener('input', performAdvancedSearch);
            document.getElementById('clearMainSearch').addEventListener('click', () => {
                document.getElementById('mainSearchInput').value = '';
                performAdvancedSearch();
            });

            // مستمعي أحداث الفلاتر
            ['rankFilter', 'provinceFilter', 'educationFilter', 'genderFilter', 'dateFromFilter', 'dateToFilter'].forEach(id => {
                document.getElementById(id).addEventListener('change', performAdvancedSearch);
            });

            // مستمعي أحداث الترتيب
            document.querySelectorAll('.sortable').forEach(header => {
                header.addEventListener('click', () => {
                    const field = header.getAttribute('data-sort');
                    sortTable(field);
                });
            });

            // مستمعي أحداث التحديد
            document.querySelectorAll('.employee-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', () => toggleRowSelection(checkbox));
            });

            // تفعيل tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>

    <style>
        /* تنسيق الصور في قائمة الموظفين */
        .employee-photo {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .employee-avatar {
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .employee-avatar:hover {
            transform: scale(1.1);
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }

        .employee-name {
            text-align: right;
        }

        .employee-name strong {
            display: block;
            margin-bottom: 2px;
            font-weight: 600;
            color: #2c3e50;
        }

        .employee-name small {
            font-weight: 400;
            font-size: 0.8rem;
        }
    </style>
}
