using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using EmpMgr.Services;
using EmpMgr.Models;

namespace EmpMgr.Controllers
{
    [Authorize]
    public class SystemSettingsController : Controller
    {
        private readonly ISystemSettingsService _settingsService;
        private readonly ILogger<SystemSettingsController> _logger;

        public SystemSettingsController(
            ISystemSettingsService settingsService,
            ILogger<SystemSettingsController> logger)
        {
            _settingsService = settingsService;
            _logger = logger;
        }

        // GET: SystemSettings
        public async Task<IActionResult> Index()
        {
            try
            {
                var settings = await _settingsService.GetSettingsAsync();
                return View(settings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل إعدادات النظام");
                TempData["ErrorMessage"] = "حدث خطأ في تحميل إعدادات النظام";
                return View(new SystemSettings());
            }
        }

        // POST: SystemSettings/Update
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Update(SystemSettings settings, IFormFile? logoFile, IFormFile? faviconFile)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return View("Index", settings);
                }

                // رفع الشعار الجديد إذا تم اختياره
                if (logoFile != null && logoFile.Length > 0)
                {
                    var currentSettings = await _settingsService.GetSettingsAsync();
                    
                    // حذف الشعار القديم
                    if (!string.IsNullOrEmpty(currentSettings.LogoPath) && 
                        currentSettings.LogoPath != "/images/moi-logo.png")
                    {
                        await _settingsService.DeleteFileAsync(currentSettings.LogoPath);
                    }

                    // رفع الشعار الجديد
                    settings.LogoPath = await _settingsService.UploadLogoAsync(logoFile);
                }

                // رفع الأيقونة الجديدة إذا تم اختيارها
                if (faviconFile != null && faviconFile.Length > 0)
                {
                    var currentSettings = await _settingsService.GetSettingsAsync();
                    
                    // حذف الأيقونة القديمة
                    if (!string.IsNullOrEmpty(currentSettings.FaviconPath) && 
                        currentSettings.FaviconPath != "/images/favicon.ico")
                    {
                        await _settingsService.DeleteFileAsync(currentSettings.FaviconPath);
                    }

                    // رفع الأيقونة الجديدة
                    settings.FaviconPath = await _settingsService.UploadFaviconAsync(faviconFile);
                }

                var updatedSettings = await _settingsService.UpdateSettingsAsync(settings, User.Identity?.Name ?? "Unknown");
                
                TempData["SuccessMessage"] = "تم تحديث إعدادات النظام بنجاح";
                return RedirectToAction(nameof(Index));
            }
            catch (ArgumentException ex)
            {
                ModelState.AddModelError("", ex.Message);
                return View("Index", settings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعدادات النظام");
                ModelState.AddModelError("", "حدث خطأ في تحديث إعدادات النظام");
                return View("Index", settings);
            }
        }

        // POST: SystemSettings/ResetLogo
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ResetLogo()
        {
            try
            {
                var settings = await _settingsService.GetSettingsAsync();
                
                // حذف الشعار الحالي إذا لم يكن الافتراضي
                if (!string.IsNullOrEmpty(settings.LogoPath) && 
                    settings.LogoPath != "/images/moi-logo.png")
                {
                    await _settingsService.DeleteFileAsync(settings.LogoPath);
                }

                // إعادة تعيين الشعار الافتراضي
                settings.LogoPath = "/images/moi-logo.png";
                await _settingsService.UpdateSettingsAsync(settings, User.Identity?.Name ?? "Unknown");
                
                TempData["SuccessMessage"] = "تم إعادة تعيين الشعار الافتراضي";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إعادة تعيين الشعار");
                TempData["ErrorMessage"] = "حدث خطأ في إعادة تعيين الشعار";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: SystemSettings/ResetFavicon
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ResetFavicon()
        {
            try
            {
                var settings = await _settingsService.GetSettingsAsync();
                
                // حذف الأيقونة الحالية إذا لم تكن الافتراضية
                if (!string.IsNullOrEmpty(settings.FaviconPath) && 
                    settings.FaviconPath != "/images/favicon.ico")
                {
                    await _settingsService.DeleteFileAsync(settings.FaviconPath);
                }

                // إعادة تعيين الأيقونة الافتراضية
                settings.FaviconPath = "/images/favicon.ico";
                await _settingsService.UpdateSettingsAsync(settings, User.Identity?.Name ?? "Unknown");
                
                TempData["SuccessMessage"] = "تم إعادة تعيين الأيقونة الافتراضية";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إعادة تعيين الأيقونة");
                TempData["ErrorMessage"] = "حدث خطأ في إعادة تعيين الأيقونة";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: SystemSettings/GetCurrentLogo
        [HttpGet]
        public async Task<IActionResult> GetCurrentLogo()
        {
            try
            {
                var settings = await _settingsService.GetSettingsAsync();
                return Json(new { logoPath = settings.LogoPath });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الشعار الحالي");
                return Json(new { logoPath = "/images/moi-logo.png" });
            }
        }
    }
}
